# 蜂巢语义分割主实验

## 概述
基于MAE预训练ViT-Base模型的8类蜂巢语义分割实验。使用UperNet分割头进行像素级分类。

## 实验架构
- **Backbone**: MAE预训练的ViT-Base (patch_size=16)
- **分割头**: UperNet with FPN
- **类别数**: 8类 (Background, Capped_Brood, Eggs, Honey, Honeycomb, Larvae, Nectar, Pollen)
- **输入尺寸**: 512x512
- **训练策略**: 监督学习 + 类别权重平衡

## 目录结构
```
main_experiment/
├── complete_training_pipeline.sh  # 完整训练流水线脚本
├── start_training.sh              # 快速启动脚本
├── configs/                       # 配置文件
│   ├── semantic_segmentation_config_correct.yaml
│   └── paths_config.yaml
├── core/                          # 核心模块
│   ├── models/                    # 模型定义
│   ├── losses/                    # 损失函数
│   ├── datasets/                  # 数据集
│   └── config/                    # 配置管理
├── scripts/                       # 训练和推理脚本
│   ├── train_semantic_segmentation.py
│   └── run_inference.py
├── data_preparation/              # 数据预处理
│   └── create_training_patches.py
├── evaluation/                    # 评估模块
├── training/                      # 训练器
├── utils/                         # 工具函数
├── visualization/                 # 可视化
└── outputs/                       # 输出结果
```

## 快速开始

### 1. 完整训练流水线
```bash
# 运行完整的训练流水线（包含数据预处理）
./complete_training_pipeline.sh
```

### 2. 仅训练（数据已准备好）
```bash
# 直接开始训练
./start_training.sh
```

### 3. 手动训练
```bash
# 使用Python脚本训练
python scripts/train_semantic_segmentation.py \
    --config configs/semantic_segmentation_config_correct.yaml \
    --device cuda:0
```

## 数据要求
- **原始图像**: `/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/static/uploads`
- **标注文件**: `/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/data/annotations`
- **预训练权重**: `/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/best_checkpoint_epoch_1556.pth`

## 主要特性
1. **自动数据预处理**: 从JSON标注生成训练图块
2. **类别权重平衡**: 针对稀有类别的权重调整
3. **多层特征提取**: 使用ViT的第3、6、9、12层特征
4. **组合损失函数**: Dice + CrossEntropy + Focal Loss
5. **详细评估指标**: 每类别的IoU、Dice、F1分数

## 训练配置
- **优化器**: AdamW (lr=1e-4, weight_decay=1e-4)
- **调度器**: CosineAnnealingLR
- **批次大小**: 16 (RTX 3090) / 8 (RTX 2080)
- **训练轮数**: 100
- **验证间隔**: 每5轮

## 输出结果
训练完成后，在`outputs/`目录下会生成：
- 模型检查点 (.pth)
- 训练日志 (.log)
- 评估指标 (.json)
- 可视化结果 (.png)
- TensorBoard日志

## 性能指标
主要关注指标：
- **Overall mIoU**: 所有类别的平均IoU
- **Honeycomb IoU**: 蜂巢类别的IoU（最重要）
- **Rare Classes mIoU**: 稀有类别的平均IoU
- **Class-wise Metrics**: 每个类别的详细指标

## 注意事项
1. 确保有足够的GPU显存（建议≥8GB）
2. 数据预处理可能需要较长时间（约30分钟）
3. 训练过程会自动保存最佳模型
4. 可通过TensorBoard监控训练进度
