#!/bin/bash

# 完整的语义分割训练流程
# 1. 清空输出目录
# 2. 从原始标注重新生成分块
# 3. 启动训练

echo "🚀 完整语义分割训练流程"
echo "=========================================="
echo "📋 流程说明:"
echo "  1️⃣ 清空训练数据目录"
echo "  2️⃣ 检查并生成mask文件"
echo "  3️⃣ 从原始标注重新生成分块（32线程并行）"
echo "  4️⃣ 检查生成的分块数量"
echo "  5️⃣ 启动语义分割训练"
echo "=========================================="

# 设置路径
PROJECT_ROOT="/home/<USER>/PycharmProjects/20250629"
EXPERIMENT_ROOT="$PROJECT_ROOT/training_experiments_organized/main_experiment"
TRAINING_DATA_DIR="$PROJECT_ROOT/training_data/honeycomb_8class_patches"
ANNOTATION_DIR="$PROJECT_ROOT/bee_cell_annotation/data/annotations"
IMAGE_DIR="$PROJECT_ROOT/bee_cell_annotation/static/uploads"
MASK_DIR="$PROJECT_ROOT/bee_cell_annotation/data/processed_masks"

echo "📁 路径配置:"
echo "  原始标注: $ANNOTATION_DIR"
echo "  原始图像: $IMAGE_DIR"
echo "  处理后mask: $MASK_DIR"
echo "  训练数据: $TRAINING_DATA_DIR"
echo ""

# 步骤1: 清空训练数据目录
echo "🧹 步骤1: 清空训练数据目录"
if [ -d "$TRAINING_DATA_DIR" ]; then
    echo "  删除现有训练数据: $TRAINING_DATA_DIR"
    rm -rf "$TRAINING_DATA_DIR"
fi
echo "  ✅ 训练数据目录已清空"
echo ""

# 步骤2: 生成mask文件（如果不存在）
echo "🎭 步骤2: 检查并生成mask文件"
if [ ! -d "$MASK_DIR" ] || [ -z "$(ls -A $MASK_DIR 2>/dev/null)" ]; then
    echo "  mask目录不存在或为空，开始生成mask..."
    cd "$EXPERIMENT_ROOT"
    python -c "
import sys
sys.path.append('.')
from utils.annotation_processor import BeeAnnotationProcessor

processor = BeeAnnotationProcessor('$ANNOTATION_DIR')
processor.process_all_annotations()
print('✅ mask文件生成完成')
"
else
    echo "  ✅ mask文件已存在"
fi
echo ""

# 步骤3: 生成训练分块
echo "📦 步骤3: 从原始标注生成训练分块"
echo "  开始分块处理（使用32线程并行）..."
cd "$EXPERIMENT_ROOT"
python data_preparation/create_training_patches.py
if [ $? -eq 0 ]; then
    echo "  ✅ 训练分块生成完成"
else
    echo "  ❌ 训练分块生成失败"
    exit 1
fi
echo ""

# 步骤4: 检查生成的分块数量
echo "📊 步骤4: 检查生成的分块"
if [ -d "$TRAINING_DATA_DIR" ]; then
    TRAIN_IMAGES=$(find "$TRAINING_DATA_DIR/train/images" -name "*.png" 2>/dev/null | wc -l)
    TRAIN_MASKS=$(find "$TRAINING_DATA_DIR/train/masks" -name "*.png" 2>/dev/null | wc -l)
    VAL_IMAGES=$(find "$TRAINING_DATA_DIR/val/images" -name "*.png" 2>/dev/null | wc -l)
    VAL_MASKS=$(find "$TRAINING_DATA_DIR/val/masks" -name "*.png" 2>/dev/null | wc -l)
    
    echo "  训练集: $TRAIN_IMAGES 图像, $TRAIN_MASKS masks"
    echo "  验证集: $VAL_IMAGES 图像, $VAL_MASKS masks"
    
    if [ $TRAIN_IMAGES -eq 0 ] || [ $TRAIN_MASKS -eq 0 ]; then
        echo "  ❌ 训练数据生成失败，分块数量为0"
        exit 1
    fi
    echo "  ✅ 分块数据检查通过"
else
    echo "  ❌ 训练数据目录不存在"
    exit 1
fi
echo ""

# 步骤5: 启动训练
echo "🎯 步骤5: 启动语义分割训练"
echo "配置文件: configs/semantic_segmentation_config_correct.yaml"
echo "训练数据: $TRAINING_DATA_DIR"
echo "=========================================="

# 检查配置文件
if [ ! -f "configs/semantic_segmentation_config_correct.yaml" ]; then
    echo "❌ 配置文件不存在"
    exit 1
fi

# 检查MAE权重
MAE_WEIGHTS="/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/best_checkpoint_epoch_1556.pth"
if [ ! -f "$MAE_WEIGHTS" ]; then
    echo "❌ MAE权重文件不存在: $MAE_WEIGHTS"
    exit 1
fi

echo "✅ 环境检查通过"
echo "🎯 真实数据分布:"
echo "  honeycomb: 42.37% (权重: 2.2)"
echo "  background: 39.39% (权重: 2.3)"
echo "  capped_brood: 10.81% (权重: 4.4)"
echo "  eggs: 0.34% (权重: 50.0)"
echo "  larvae: 0.65% (权重: 36.2)"
echo "=========================================="

# 启动训练 (使用GPU 0)
export CUDA_VISIBLE_DEVICES=0

# 选择配置文件
# 选项1: 标准优化配置 (无伪标签) - 当前使用
cd "$EXPERIMENT_ROOT"
python scripts/train_semantic_segmentation.py \
    --config configs/semantic_segmentation_config_correct.yaml

# 选项2: 带伪标签的优化配置 (需要进一步集成)
# python scripts/train_semantic_segmentation.py \
#     --config configs/semantic_segmentation_config_with_pseudo_labels.yaml

echo ""
echo "🎉 训练流程完成！"
