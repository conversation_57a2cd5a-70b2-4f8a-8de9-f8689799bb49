#!/usr/bin/env python3
"""
为8类蜂巢语义分割创建512x512训练图块

新的honeycomb逻辑：
- background(0): 完全未被标注的区域
- honeycomb(4): 所有被标注覆盖的区域（基础蜂巢结构）
- 其他类别(1,2,3,5,6,7): 在honeycomb基础上的具体内容物细分

Author: AI Assistant
Date: 2024
"""

import os
import numpy as np
import cv2
from PIL import Image
from pathlib import Path
import json
import logging
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import shutil
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing
from functools import partial

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HoneycombPatchExtractor:
    """蜂巢训练图块提取器"""
    
    def __init__(self,
                 image_dir: str,
                 mask_dir: str,
                 output_dir: str,
                 patch_size: int = 512,
                 stride: int = 384,
                 min_honeycomb_ratio: float = 0.1,
                 num_workers: int = None):
        """
        Args:
            image_dir: 原始图像目录
            mask_dir: mask文件目录
            output_dir: 输出目录
            patch_size: 图块大小
            stride: 滑动步长
            min_honeycomb_ratio: 最小蜂巢内容比例
            num_workers: 并行处理线程数，None表示自动检测
        """
        self.image_dir = Path(image_dir)
        self.mask_dir = Path(mask_dir)
        self.output_dir = Path(output_dir)
        self.patch_size = patch_size
        self.stride = stride
        self.min_honeycomb_ratio = min_honeycomb_ratio

        # 设置并行处理线程数 - 充分利用双路E5
        if num_workers is None:
            cpu_count = os.cpu_count() or 8
            self.num_workers = min(cpu_count, 32)  # 最多32线程
        else:
            self.num_workers = num_workers

        logger.info(f"🚀 使用 {self.num_workers} 个线程进行并行处理")
        
        # 创建输出目录结构
        self.train_images_dir = self.output_dir / 'train' / 'images'
        self.train_masks_dir = self.output_dir / 'train' / 'masks'
        self.val_images_dir = self.output_dir / 'val' / 'images'
        self.val_masks_dir = self.output_dir / 'val' / 'masks'
        
        for dir_path in [self.train_images_dir, self.train_masks_dir, 
                        self.val_images_dir, self.val_masks_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 8类语义分割类别
        self.class_names = [
            'background',    # 0
            'capped_brood',  # 1
            'eggs',          # 2
            'honey',         # 3
            'honeycomb',     # 4
            'larvae',        # 5
            'nectar',        # 6
            'pollen'         # 7
        ]
        
        # 统计信息
        self.stats = {
            'total_images': 0,
            'total_patches': 0,
            'valid_patches': 0,
            'train_patches': 0,
            'val_patches': 0,
            'class_pixel_counts': defaultdict(int),
            'patch_class_distribution': defaultdict(int)
        }
        
        logger.info(f"图块提取器初始化完成")
        logger.info(f"图像目录: {self.image_dir}")
        logger.info(f"Mask目录: {self.mask_dir}")
        logger.info(f"输出目录: {self.output_dir}")
        logger.info(f"图块大小: {patch_size}x{patch_size}, 步长: {stride}")
        logger.info(f"最小蜂巢比例: {min_honeycomb_ratio}")
    
    def get_image_mask_pairs(self) -> List[Tuple[Path, Path]]:
        """获取所有图像-mask对"""
        pairs = []
        
        # 查找所有JPG图像
        image_files = list(self.image_dir.glob('*.JPG'))
        
        for image_file in image_files:
            # 构建对应的mask文件名
            mask_name = image_file.stem + '_mask.png'
            mask_file = self.mask_dir / mask_name
            
            if mask_file.exists():
                pairs.append((image_file, mask_file))
            else:
                logger.warning(f"找不到对应的mask文件: {mask_name}")
        
        logger.info(f"找到 {len(pairs)} 个有效的图像-mask对")
        return pairs
    
    def analyze_patch_quality(self, mask_patch: np.ndarray) -> Dict:
        """分析图块质量"""
        total_pixels = self.patch_size * self.patch_size
        
        # 统计各类别像素数
        unique_values, counts = np.unique(mask_patch, return_counts=True)
        class_counts = dict(zip(unique_values, counts))
        
        # 计算蜂巢相关像素（所有非背景像素）
        honeycomb_pixels = total_pixels - class_counts.get(0, 0)  # 总像素 - 背景像素
        honeycomb_ratio = honeycomb_pixels / total_pixels
        
        # 计算主要类别
        main_class = 0  # 默认背景
        max_pixels = class_counts.get(0, 0)
        for class_id, pixel_count in class_counts.items():
            if pixel_count > max_pixels:
                max_pixels = pixel_count
                main_class = class_id
        
        return {
            'honeycomb_ratio': honeycomb_ratio,
            'main_class': main_class,
            'class_counts': class_counts,
            'is_valid': honeycomb_ratio >= self.min_honeycomb_ratio
        }
    
    def extract_patches_from_image(self, image_path: Path, mask_path: Path, 
                                 is_validation: bool = False) -> int:
        """从单个图像提取图块"""
        try:
            # 读取图像和mask
            image = np.array(Image.open(image_path))
            mask = np.array(Image.open(mask_path))
            
            if image.shape[:2] != mask.shape[:2]:
                logger.error(f"图像和mask尺寸不匹配: {image_path.name}")
                return 0
            
            height, width = image.shape[:2]
            patch_count = 0
            valid_count = 0
            
            # 选择输出目录
            if is_validation:
                images_dir = self.val_images_dir
                masks_dir = self.val_masks_dir
            else:
                images_dir = self.train_images_dir
                masks_dir = self.train_masks_dir
            
            base_name = image_path.stem
            
            # 滑动窗口提取图块
            for y in range(0, height - self.patch_size + 1, self.stride):
                for x in range(0, width - self.patch_size + 1, self.stride):
                    # 提取图块
                    image_patch = image[y:y+self.patch_size, x:x+self.patch_size]
                    mask_patch = mask[y:y+self.patch_size, x:x+self.patch_size]
                    
                    # 分析图块质量
                    quality = self.analyze_patch_quality(mask_patch)
                    
                    if quality['is_valid']:
                        # 保存图块
                        patch_name = f"{base_name}_patch_{patch_count:04d}"
                        
                        # 保存图像图块
                        image_patch_path = images_dir / f"{patch_name}.png"
                        Image.fromarray(image_patch).save(image_patch_path)
                        
                        # 保存mask图块
                        mask_patch_path = masks_dir / f"{patch_name}.png"
                        Image.fromarray(mask_patch).save(mask_patch_path)
                        
                        valid_count += 1
                        
                        # 更新统计
                        for class_id, pixel_count in quality['class_counts'].items():
                            self.stats['class_pixel_counts'][class_id] += pixel_count
                        
                        self.stats['patch_class_distribution'][quality['main_class']] += 1
                        
                        if is_validation:
                            self.stats['val_patches'] += 1
                        else:
                            self.stats['train_patches'] += 1
                    
                    patch_count += 1
            
            self.stats['total_patches'] += patch_count
            self.stats['valid_patches'] += valid_count
            
            logger.info(f"处理完成: {image_path.name} -> {valid_count}/{patch_count} 有效图块")
            return valid_count
            
        except Exception as e:
            logger.error(f"处理图像 {image_path.name} 时出错: {e}")
            return 0
    
    def analyze_image_classes(self, mask_path: Path) -> set:
        """分析单个mask图像包含的类别"""
        try:
            mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
            if mask is None:
                return set()
            unique_classes = set(np.unique(mask))
            return unique_classes
        except Exception as e:
            logger.warning(f"分析mask {mask_path} 时出错: {e}")
            return set()

    def stratified_split_train_val(self, pairs: List[Tuple[Path, Path]],
                                  val_ratio: float = 0.2) -> Tuple[List, List]:
        """使用分层采样划分训练集和验证集，确保每个类别都有代表"""
        logger.info("开始分层采样划分训练集和验证集...")

        # 分析每个图像包含的类别
        image_classes = {}
        class_to_images = defaultdict(list)

        logger.info("分析图像类别分布...")
        for i, (image_path, mask_path) in enumerate(pairs):
            classes = self.analyze_image_classes(mask_path)
            image_classes[i] = classes

            # 记录每个类别出现在哪些图像中
            for class_id in classes:
                if class_id < 8:  # 只考虑有效类别
                    class_to_images[class_id].append(i)

        # 显示类别分布
        class_names = ['background', 'capped_brood', 'eggs', 'honey',
                      'honeycomb', 'larvae', 'nectar', 'pollen']
        logger.info("原始图像类别分布:")
        for class_id in range(8):
            count = len(class_to_images[class_id])
            logger.info(f"  {class_names[class_id]:<12}: {count:3d} 图像")

        # 分层采样策略
        np.random.seed(42)
        val_indices = set()
        train_indices = set()

        # 稀有类别优先分配（确保验证集中有足够代表）
        rare_classes = [2, 5, 6, 7]  # eggs, larvae, nectar, pollen
        min_val_per_rare_class = 2  # 每个稀有类别至少2个验证样本

        logger.info("为稀有类别分配验证样本...")
        for class_id in rare_classes:
            if class_id in class_to_images and len(class_to_images[class_id]) > 0:
                images_with_class = class_to_images[class_id]
                # 为稀有类别分配更多验证样本（30-40%）
                val_count = max(min_val_per_rare_class,
                              int(len(images_with_class) * 0.35))
                val_count = min(val_count, len(images_with_class))

                val_samples = np.random.choice(images_with_class, val_count, replace=False)
                val_indices.update(val_samples)

                logger.info(f"  {class_names[class_id]:<12}: {val_count}/{len(images_with_class)} -> 验证集")

        # 为剩余图像分配
        remaining_indices = [i for i in range(len(pairs)) if i not in val_indices]
        target_val_total = int(len(pairs) * val_ratio)
        additional_val_needed = max(0, target_val_total - len(val_indices))
        additional_val_needed = min(additional_val_needed, len(remaining_indices))

        if additional_val_needed > 0:
            additional_val = np.random.choice(remaining_indices, additional_val_needed, replace=False)
            val_indices.update(additional_val)

        # 剩余的都是训练集
        train_indices = set(range(len(pairs))) - val_indices

        # 转换为列表
        train_pairs = [pairs[i] for i in train_indices]
        val_pairs = [pairs[i] for i in val_indices]

        logger.info(f"最终分配: 训练集 {len(train_pairs)}, 验证集 {len(val_pairs)}")

        # 验证分配结果
        self.validate_split_balance(train_pairs, val_pairs, class_names)

        return train_pairs, val_pairs

    def validate_split_balance(self, train_pairs: List, val_pairs: List, class_names: List[str]):
        """验证分割结果的类别平衡性"""
        logger.info("验证分割结果的类别平衡性...")

        def count_classes_in_pairs(pairs):
            class_counts = defaultdict(int)
            for _, mask_path in pairs:
                classes = self.analyze_image_classes(mask_path)
                for class_id in classes:
                    if class_id < 8:
                        class_counts[class_id] += 1
            return class_counts

        train_counts = count_classes_in_pairs(train_pairs)
        val_counts = count_classes_in_pairs(val_pairs)

        logger.info("分割后类别分布:")
        logger.info(f"{'类别':<12} {'训练集':<8} {'验证集':<8} {'验证集比例':<10} {'状态'}")
        logger.info("-" * 55)

        all_balanced = True
        for class_id in range(8):
            train_count = train_counts[class_id]
            val_count = val_counts[class_id]
            total_count = train_count + val_count
            val_ratio = val_count / total_count * 100 if total_count > 0 else 0

            status = "✅" if val_count > 0 else "❌"
            if val_count == 0 and total_count > 0:
                all_balanced = False

            logger.info(f"{class_names[class_id]:<12} {train_count:<8} {val_count:<8} {val_ratio:6.1f}%     {status}")

        if all_balanced:
            logger.info("✅ 所有类别在验证集中都有代表")
        else:
            logger.warning("⚠️ 某些类别在验证集中缺失，可能影响训练效果")

    def process_pairs_parallel(self, pairs: List[Tuple[Path, Path]], is_validation: bool):
        """并行处理图像对"""
        from concurrent.futures import ThreadPoolExecutor
        from tqdm import tqdm

        # 创建处理函数
        def process_single_pair(pair_data):
            image_path, mask_path = pair_data
            return self.extract_patches_from_image(image_path, mask_path, is_validation)

        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
            # 提交所有任务
            futures = [executor.submit(process_single_pair, pair) for pair in pairs]

            # 使用tqdm显示进度
            dataset_name = "验证集" if is_validation else "训练集"
            for future in tqdm(futures, desc=f"处理{dataset_name}", unit="图像"):
                try:
                    future.result()  # 等待任务完成
                    if not is_validation:
                        self.stats['total_images'] = getattr(self.stats, 'total_images', 0) + 1
                except Exception as e:
                    logger.error(f"处理图像时出错: {e}")

        logger.info(f"✅ {dataset_name}处理完成")
    
    def process_all_images(self):
        """处理所有图像"""
        # 获取所有图像-mask对
        pairs = self.get_image_mask_pairs()
        if not pairs:
            logger.error("没有找到有效的图像-mask对")
            return
        
        # 划分训练集和验证集（使用分层采样）
        train_pairs, val_pairs = self.stratified_split_train_val(pairs)
        
        # 并行处理训练集
        logger.info(f"开始并行处理训练集 ({len(train_pairs)} 个图像)...")
        self.process_pairs_parallel(train_pairs, is_validation=False)

        # 并行处理验证集
        logger.info(f"开始并行处理验证集 ({len(val_pairs)} 个图像)...")
        self.process_pairs_parallel(val_pairs, is_validation=True)
        
        # 保存数据集信息
        self._save_dataset_info()
        self._print_statistics()
    
    def _save_dataset_info(self):
        """保存数据集信息"""
        # 转换统计数据中的numpy类型为Python原生类型
        stats_dict = {}
        for key, value in self.stats.items():
            if isinstance(value, dict):
                stats_dict[key] = {str(k): int(v) if hasattr(v, 'item') else v for k, v in value.items()}
            else:
                stats_dict[key] = int(value) if hasattr(value, 'item') else value

        dataset_info = {
            'dataset_name': 'honeycomb_8class_segmentation',
            'description': '蜂巢8类语义分割数据集（新honeycomb逻辑）',
            'classes': {i: name for i, name in enumerate(self.class_names)},
            'num_classes': len(self.class_names),
            'patch_size': self.patch_size,
            'stride': self.stride,
            'min_honeycomb_ratio': self.min_honeycomb_ratio,
            'statistics': stats_dict,
            'honeycomb_logic': {
                'background': '完全未被标注的区域',
                'honeycomb': '所有被标注覆盖的区域（基础蜂巢结构）',
                'content_classes': '在honeycomb基础上的具体内容物细分'
            },
            'created_by': 'HoneycombPatchExtractor'
        }
        
        info_file = self.output_dir / 'dataset_info.json'
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(dataset_info, f, indent=2, ensure_ascii=False)
        
        logger.info(f"数据集信息已保存: {info_file}")
    
    def _print_statistics(self):
        """打印统计信息"""
        logger.info("=" * 60)
        logger.info("训练数据集创建统计")
        logger.info("=" * 60)
        logger.info(f"总图像数: {self.stats['total_images']}")
        logger.info(f"总图块数: {self.stats['total_patches']}")
        logger.info(f"有效图块数: {self.stats['valid_patches']}")
        logger.info(f"训练图块数: {self.stats['train_patches']}")
        logger.info(f"验证图块数: {self.stats['val_patches']}")
        
        logger.info(f"\n类别像素分布:")
        total_pixels = sum(self.stats['class_pixel_counts'].values())
        for class_id in range(len(self.class_names)):
            pixel_count = self.stats['class_pixel_counts'][class_id]
            percentage = pixel_count / total_pixels * 100 if total_pixels > 0 else 0
            logger.info(f"  {class_id}: {self.class_names[class_id]} - {pixel_count:,} 像素 ({percentage:.2f}%)")
        
        logger.info(f"\n图块主要类别分布:")
        for class_id in range(len(self.class_names)):
            patch_count = self.stats['patch_class_distribution'][class_id]
            logger.info(f"  {class_id}: {self.class_names[class_id]} - {patch_count} 个图块")
        
        logger.info(f"\n输出目录: {self.output_dir}")
        logger.info("=" * 60)


def main():
    """主函数"""
    import time
    start_time = time.time()

    # 从环境变量或配置文件读取路径
    base_dir = os.environ.get('BEE_PROJECT_ROOT', '/home/<USER>/PycharmProjects/20250629')
    image_dir = os.path.join(base_dir, 'bee_cell_annotation/static/uploads')
    mask_dir = os.path.join(base_dir, 'bee_cell_annotation/data/processed_masks')
    output_dir = os.path.join(base_dir, 'training_data/honeycomb_8class_patches')

    # 检测CPU核心数
    cpu_count = os.cpu_count() or 8
    num_workers = min(cpu_count, 32)  # 最多32线程，充分利用双路E5

    logger.info(f"🖥️  检测到 {cpu_count} 个CPU核心")
    logger.info(f"🚀 将使用 {num_workers} 个线程进行并行处理")

    # 创建图块提取器
    extractor = HoneycombPatchExtractor(
        image_dir=image_dir,
        mask_dir=mask_dir,
        output_dir=output_dir,
        patch_size=512,
        stride=384,
        min_honeycomb_ratio=0.1,
        num_workers=num_workers
    )

    # 处理所有图像
    extractor.process_all_images()

    # 计算处理时间
    end_time = time.time()
    processing_time = end_time - start_time

    print(f"\n🎉 训练数据集创建完成！")
    print(f"📁 输出目录: {output_dir}")
    print(f"⏱️  处理时间: {processing_time:.1f} 秒")
    print(f"🚀 并行线程数: {num_workers}")
    print("📊 请检查 dataset_info.json 了解详细统计信息")


if __name__ == "__main__":
    main()
