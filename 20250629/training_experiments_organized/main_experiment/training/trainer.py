#!/usr/bin/env python3
"""
分割模型训练器
重构后的模块化训练器，支持标准训练和伪标签训练
"""

import os
import sys
import time
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Tuple

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler
from torch.amp import autocast
import numpy as np
from tqdm import tqdm

# 导入重构后的模块
try:
    from ..core.config import BaseConfig
    from ..core.models import ViTBackbone, ViTUPerNetHead, MultiLayerViTFeatureExtractor
    from ..core.datasets import HoneycombDataset, create_dataloaders, MixedDataLoader
    from ..core.losses import create_loss_function
    from ..utils import SegmentationMetrics, MetricsTracker, MAEWeightLoader
    from ..utils import create_scheduler, adjust_learning_rate
    from ..utils import PseudoLabelManager, create_unlabeled_dataloader
    from ..utils.pseudo_labeling import AcademicEnhancedPseudoLabelGenerator
except ImportError:
    # 当作为脚本直接运行时的绝对导入
    from core.config import BaseConfig
    from core.models import ViTBackbone, ViTUPerNetHead, MultiLayerViTFeatureExtractor
    from core.datasets import HoneycombDataset, create_dataloaders, MixedDataLoader
    from core.losses import create_loss_function
    from utils import SegmentationMetrics, MetricsTracker, MAEWeightLoader
    from utils import create_scheduler, adjust_learning_rate
    from utils import PseudoLabelManager, create_unlabeled_dataloader
    from utils.pseudo_labeling import AcademicEnhancedPseudoLabelGenerator


class SegmentationTrainer:
    """分割模型训练器"""
    
    def __init__(self, config: BaseConfig):
        self.config = config
        self.device = config.get_device()
        
        # 设置输出目录
        self.output_dir = Path(config.system.output_dir) / config.system.experiment_name
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self._setup_logging()
        
        # 初始化组件
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.loss_function = None
        self.scaler = None
        self.train_loader = None
        self.val_loader = None

        # 伪标签相关组件
        self.pseudo_label_manager = None
        self.unlabeled_dataloader = None
        self.mixed_dataloader = None

        # 训练状态
        self.current_epoch = 0
        self.best_metric = 0.0
        self.metrics_tracker = MetricsTracker()
        
        # 监控
        self.writer = None
        self.use_wandb = False
        
        logging.info(f"训练器初始化完成，设备: {self.device}")
    
    def _setup_logging(self):
        """设置日志系统"""
        log_file = self.output_dir / "training.log"
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def setup_model(self):
        """设置模型"""
        logging.info("正在设置模型...")
        
        # 创建ViT特征提取器
        # 使用标准的timm模型名称，图像尺寸通过img_size参数指定
        model_name = f"vit_{self.config.model.get_model_size()}_patch{self.config.model.patch_size}_224"
        feature_extractor = MultiLayerViTFeatureExtractor(
            model_name=model_name,
            img_size=self.config.model.image_size,
            pretrained_path=self.config.model.mae_checkpoint_path if self.config.model.use_pretrained else None
        )
        
        # 创建分割头
        segmentation_head = ViTUPerNetHead(
            feature_channels=self.config.model.embed_dim,
            fpn_dim=self.config.model.upernet_channels,
            num_classes=self.config.model.num_classes,
            pool_scales=self.config.model.upernet_pool_scales
        )
        
        # 创建组合模型包装器
        class SegmentationModel(nn.Module):
            def __init__(self, feature_extractor, segmentation_head, target_size=(128, 128)):
                super().__init__()
                self.feature_extractor = feature_extractor
                self.segmentation_head = segmentation_head
                self.target_size = target_size

            def forward(self, x):
                # 提取特征
                raw_features = self.feature_extractor(x)

                # 转换特征格式：从嵌套字典提取spatial特征
                features = {}
                for layer_name, layer_features in raw_features.items():
                    if isinstance(layer_features, dict) and 'spatial' in layer_features:
                        features[layer_name] = layer_features['spatial']
                    else:
                        features[layer_name] = layer_features

                # 分割预测，传递target_size
                output_dict = self.segmentation_head(features, self.target_size)

                # 返回主要输出（训练时可能需要辅助输出，但这里先返回主要输出）
                if isinstance(output_dict, dict) and 'main' in output_dict:
                    return output_dict['main']
                else:
                    return output_dict

        # 计算目标尺寸（通常是输入尺寸的1/4）
        target_size = (self.config.model.image_size // 4, self.config.model.image_size // 4)

        self.model = SegmentationModel(
            feature_extractor,
            segmentation_head,
            target_size
        ).to(self.device)
        
        # 加载预训练权重
        if self.config.model.use_pretrained and self.config.model.mae_checkpoint_path:
            try:
                weight_loader = MAEWeightLoader(self.config.model.mae_checkpoint_path)
                missing_keys, unexpected_keys = weight_loader.load_to_feature_extractor(self.model.feature_extractor)  # 加载到特征提取器
                logging.info(f"MAE预训练权重加载成功，缺失键: {len(missing_keys)}, 意外键: {len(unexpected_keys)}")
            except FileNotFoundError as e:
                logging.warning(f"MAE预训练权重文件未找到: {e}")
                logging.info("将使用随机初始化权重")
            except Exception as e:
                logging.warning(f"MAE预训练权重加载失败: {e}")
                logging.info("将使用随机初始化权重")
        
        logging.info(f"模型设置完成，参数量: {sum(p.numel() for p in self.model.parameters()):,}")
    
    def setup_data(self):
        """设置数据加载器"""
        logging.info("正在设置数据加载器...")
        
        # 创建数据加载器
        self.train_loader, self.val_loader = create_dataloaders(self.config.data)
        
        logging.info(f"数据加载器设置完成")
        logging.info(f"训练集: {len(self.train_loader)} batches")
        logging.info(f"验证集: {len(self.val_loader)} batches")
    
    def setup_optimizer(self):
        """设置优化器"""
        logging.info("正在设置优化器...")
        
        if self.config.training.optimizer.lower() == "adamw":
            self.optimizer = optim.AdamW(
                self.model.parameters(),
                lr=self.config.training.learning_rate,
                weight_decay=self.config.training.weight_decay,
                betas=self.config.training.betas
            )
        elif self.config.training.optimizer.lower() == "sgd":
            self.optimizer = optim.SGD(
                self.model.parameters(),
                lr=self.config.training.learning_rate,
                weight_decay=self.config.training.weight_decay,
                momentum=self.config.training.momentum
            )
        else:
            raise ValueError(f"不支持的优化器: {self.config.training.optimizer}")
        
        logging.info(f"优化器设置完成: {self.config.training.optimizer}")
    
    def setup_scheduler(self):
        """设置学习率调度器"""
        logging.info("正在设置学习率调度器...")
        
        self.scheduler = create_scheduler(
            optimizer=self.optimizer,
            scheduler_type=self.config.training.lr_scheduler,
            epochs=self.config.training.epochs,
            warmup_epochs=self.config.training.warmup_epochs,
            **{
                'step_size': self.config.training.lr_step_size,
                'gamma': self.config.training.lr_gamma,
                'patience': self.config.training.lr_patience
            }
        )
        
        logging.info(f"学习率调度器设置完成: {self.config.training.lr_scheduler}")
    
    def setup_loss_function(self):
        """设置损失函数"""
        logging.info("正在设置损失函数...")
        
        self.loss_function = create_loss_function(
            loss_type=self.config.training.loss_function,
            num_classes=self.config.model.num_classes,
            dice_weight=self.config.training.dice_weight,
            ce_weight=self.config.training.ce_weight,
            focal_weight=self.config.training.focal_weight,
            focal_alpha=self.config.training.focal_alpha,
            focal_gamma=self.config.training.focal_gamma
        )
        
        logging.info(f"损失函数设置完成: {self.config.training.loss_function}")
    
    def setup_mixed_precision(self):
        """设置混合精度训练"""
        if self.config.training.mixed_precision:
            self.scaler = GradScaler()
            logging.info("混合精度训练已启用")
        else:
            self.scaler = None
            logging.info("混合精度训练已禁用")
    
    def setup_pseudo_labeling(self):
        """设置伪标签训练"""
        if not self.config.training.pseudo_label.enable_pseudo_labeling:
            logging.info("伪标签训练已禁用")
            return

        logging.info("正在设置伪标签训练...")

        # 检查是否启用学术级精炼
        enable_academic_refinement = getattr(
            self.config.training.pseudo_label,
            'enable_academic_refinement',
            False
        )

        if enable_academic_refinement:
            logging.info("启用学术级伪标签精炼")

            # 创建增强的伪标签生成器
            enhanced_generator = AcademicEnhancedPseudoLabelGenerator(
                config=self.config.training.pseudo_label,
                num_classes=self.config.model.num_classes,
                enable_academic_refinement=True
            )

            # 使用标注数据初始化学术框架
            if hasattr(self, 'train_dataloader') and self.train_dataloader:
                try:
                    # 从训练数据加载器创建标注数据集
                    labeled_dataset = self._create_labeled_dataset_for_academic_framework()
                    enhanced_generator.initialize_academic_framework(labeled_dataset)
                    logging.info("学术框架初始化完成")
                except Exception as e:
                    logging.error(f"学术框架初始化失败: {e}")
                    logging.info("回退到标准伪标签生成")
                    enable_academic_refinement = False

            if enable_academic_refinement:
                # 创建使用增强生成器的伪标签管理器
                self.pseudo_label_manager = PseudoLabelManager(
                    model=self.model,
                    device=self.device,
                    config=self.config.training.pseudo_label,
                    custom_generator=enhanced_generator
                )
            else:
                # 回退到标准管理器
                self.pseudo_label_manager = PseudoLabelManager(
                    model=self.model,
                    device=self.device,
                    config=self.config.training.pseudo_label
                )
        else:
            logging.info("使用标准伪标签生成")
            # 创建标准伪标签管理器
            self.pseudo_label_manager = PseudoLabelManager(
                model=self.model,
                device=self.device,
                config=self.config.training.pseudo_label
            )
        
        # 创建无标签数据加载器
        if self.config.training.pseudo_label.unlabeled_data_dir:
            self.unlabeled_dataloader = create_unlabeled_dataloader(
                self.config.training.pseudo_label.unlabeled_data_dir,
                self.config.data
            )
            logging.info(f"无标签数据加载器创建完成: {len(self.unlabeled_dataloader)} batches")

        logging.info("伪标签训练设置完成")

    def _create_labeled_dataset_for_academic_framework(self):
        """为学术框架创建标注数据集"""
        class LabeledDatasetWrapper:
            def __init__(self, dataloader):
                self.dataloader = dataloader
                self.samples = []
                self._extract_samples()

            def _extract_samples(self):
                """提取样本用于学术框架初始化"""
                count = 0
                max_samples = 50  # 限制样本数量以加快初始化

                for batch in self.dataloader:
                    if count >= max_samples:
                        break

                    if isinstance(batch, dict):
                        images = batch.get('labeled_images', batch.get('images'))
                        targets = batch.get('labeled_targets', batch.get('targets'))
                    else:
                        images, targets = batch

                    # 转换为numpy格式
                    if isinstance(images, torch.Tensor):
                        images = images.cpu().numpy()
                    if isinstance(targets, torch.Tensor):
                        targets = targets.cpu().numpy()

                    # 添加样本
                    for i in range(min(images.shape[0], max_samples - count)):
                        image = images[i].transpose(1, 2, 0)  # CHW -> HWC
                        if image.dtype != np.uint8:
                            image = (image * 255).astype(np.uint8)

                        target = targets[i]

                        self.samples.append((image, target, f"sample_{count}"))
                        count += 1

                        if count >= max_samples:
                            break

            def __len__(self):
                return len(self.samples)

            def __getitem__(self, idx):
                return self.samples[idx]

        return LabeledDatasetWrapper(self.train_dataloader)
    
    def setup_all(self):
        """设置所有组件"""
        self.setup_model()
        self.setup_data()
        self.setup_optimizer()
        self.setup_scheduler()
        self.setup_loss_function()
        self.setup_mixed_precision()
        self.setup_pseudo_labeling()
        
        logging.info("所有组件设置完成，准备开始训练")
    
    def save_checkpoint(self, epoch: int, is_best: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'best_metric': self.best_metric,
            'config': self.config
        }
        
        # 保存最新检查点
        checkpoint_path = self.output_dir / f"checkpoint_epoch_{epoch}.pth"
        torch.save(checkpoint, checkpoint_path)
        
        # 保存最佳模型
        if is_best:
            best_path = self.output_dir / "best_model.pth"
            torch.save(checkpoint, best_path)
            logging.info(f"保存最佳模型: {best_path}")
        
        # 清理旧的检查点（保留最近的几个）
        self._cleanup_checkpoints()
    
    def _cleanup_checkpoints(self):
        """清理旧的检查点文件"""
        checkpoint_files = list(self.output_dir.glob("checkpoint_epoch_*.pth"))
        if len(checkpoint_files) > self.config.system.save_top_k:
            # 按修改时间排序，删除最旧的文件
            checkpoint_files.sort(key=lambda x: x.stat().st_mtime)
            for old_file in checkpoint_files[:-self.config.system.save_top_k]:
                old_file.unlink()
    
    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if self.scheduler and checkpoint['scheduler_state_dict']:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.best_metric = checkpoint['best_metric']
        
        logging.info(f"检查点加载完成: {checkpoint_path}")
        logging.info(f"恢复到epoch {self.current_epoch}, 最佳指标: {self.best_metric:.4f}")
    
    def get_current_lr(self) -> float:
        """获取当前学习率"""
        return self.optimizer.param_groups[0]['lr']

    def train_epoch(self) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()

        # 重置指标
        train_metrics = SegmentationMetrics(
            num_classes=self.config.model.num_classes,
            ignore_index=self.config.data.ignore_index
        )

        total_loss = 0.0
        num_batches = len(self.train_loader)

        # 使用混合数据加载器（如果启用伪标签）
        if self.mixed_dataloader is not None:
            dataloader = self.mixed_dataloader
        else:
            dataloader = self.train_loader

        progress_bar = tqdm(dataloader, desc=f"Epoch {self.current_epoch}")

        for batch_idx, batch in enumerate(progress_bar):
            # 处理批次数据
            if isinstance(batch, dict):
                # 混合数据加载器返回字典
                images = batch['images'].to(self.device)
                targets = batch['targets'].to(self.device)
            else:
                # 标准数据加载器返回元组
                images, targets = batch
                images = images.to(self.device)
                targets = targets.to(self.device)

            # 前向传播
            if self.scaler is not None:
                # 混合精度训练
                with autocast(device_type='cuda'):
                    outputs = self.model(images)
                    loss_dict = self.loss_function(outputs, targets)
                    loss = loss_dict['total_loss'] if isinstance(loss_dict, dict) else loss_dict

                # 反向传播
                self.scaler.scale(loss).backward()

                # 梯度裁剪
                if self.config.training.gradient_clip_norm > 0:
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(),
                        self.config.training.gradient_clip_norm
                    )

                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                # 标准训练
                outputs = self.model(images)
                loss_dict = self.loss_function(outputs, targets)
                loss = loss_dict['total_loss'] if isinstance(loss_dict, dict) else loss_dict

                # 反向传播
                loss.backward()

                # 梯度裁剪
                if self.config.training.gradient_clip_norm > 0:
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(),
                        self.config.training.gradient_clip_norm
                    )

                self.optimizer.step()

            self.optimizer.zero_grad()

            # 更新指标
            train_metrics.update(outputs.detach(), targets)
            total_loss += loss.item()

            # 更新进度条
            progress_bar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'lr': f"{self.get_current_lr():.6f}"
            })

        # 计算平均损失和指标
        avg_loss = total_loss / num_batches
        metrics = train_metrics.compute_all_metrics()
        metrics['loss'] = avg_loss

        return metrics

    def validate_epoch(self) -> Dict[str, float]:
        """验证一个epoch"""
        self.model.eval()

        # 重置指标
        val_metrics = SegmentationMetrics(
            num_classes=self.config.model.num_classes,
            ignore_index=self.config.data.ignore_index
        )

        total_loss = 0.0
        num_batches = len(self.val_loader)

        with torch.no_grad():
            progress_bar = tqdm(self.val_loader, desc="Validation")

            for batch_idx, (images, targets) in enumerate(progress_bar):
                images = images.to(self.device)
                targets = targets.to(self.device)

                # 前向传播
                start_time = time.time()
                outputs = self.model(images)
                inference_time = time.time() - start_time

                # 计算损失
                loss_dict = self.loss_function(outputs, targets)
                loss = loss_dict['total_loss'] if isinstance(loss_dict, dict) else loss_dict

                # 更新指标
                val_metrics.update(outputs, targets, inference_time)
                total_loss += loss.item()

                # 更新进度条
                progress_bar.set_postfix({
                    'loss': f"{loss.item():.4f}"
                })

        # 计算平均损失和指标
        avg_loss = total_loss / num_batches
        metrics = val_metrics.compute_all_metrics()
        metrics['loss'] = avg_loss

        return metrics

    def train(self):
        """主训练循环"""
        logging.info("开始训练...")

        for epoch in range(self.current_epoch, self.config.training.epochs):
            self.current_epoch = epoch

            # 训练阶段
            train_metrics = self.train_epoch()

            # 验证阶段
            if epoch % self.config.system.val_every_n_epochs == 0:
                val_metrics = self.validate_epoch()
            else:
                val_metrics = {}

            # 更新学习率
            if self.scheduler:
                if isinstance(self.scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_metrics.get('loss', train_metrics['loss']))
                else:
                    self.scheduler.step()

            # 记录指标
            self.metrics_tracker.update_train_metrics(train_metrics, epoch)
            if val_metrics:
                self.metrics_tracker.update_val_metrics(val_metrics, epoch)

            # 检查是否为最佳模型
            current_metric = val_metrics.get('mIoU', train_metrics.get('mIoU', 0.0))
            is_best = current_metric > self.best_metric
            if is_best:
                self.best_metric = current_metric

            # 保存检查点
            if epoch % self.config.system.save_every_n_epochs == 0 or is_best:
                self.save_checkpoint(epoch, is_best)

            # 打印训练信息
            self._log_epoch_results(epoch, train_metrics, val_metrics, is_best)

            # 更新伪标签（如果启用）
            if (self.pseudo_label_manager and
                epoch >= self.config.training.pseudo_label.start_epoch and
                epoch % self.config.training.pseudo_label.update_frequency == 0):
                self._update_pseudo_labels(epoch)

        logging.info("训练完成!")
        self._print_training_summary()

    def _log_epoch_results(self, epoch: int, train_metrics: Dict, val_metrics: Dict, is_best: bool):
        """记录epoch结果"""
        log_msg = f"Epoch {epoch:3d}/{self.config.training.epochs}"
        log_msg += f" | Train Loss: {train_metrics['loss']:.4f}"
        log_msg += f" | Train mIoU: {train_metrics.get('mIoU', 0.0):.4f}"

        if val_metrics:
            log_msg += f" | Val Loss: {val_metrics['loss']:.4f}"
            log_msg += f" | Val mIoU: {val_metrics.get('mIoU', 0.0):.4f}"

        log_msg += f" | LR: {self.get_current_lr():.6f}"

        if is_best:
            log_msg += " | *** BEST ***"

        logging.info(log_msg)

    def _update_pseudo_labels(self, epoch: int):
        """更新伪标签"""
        if not self.unlabeled_dataloader:
            return

        logging.info(f"Epoch {epoch}: 更新伪标签...")

        # 生成伪标签
        pseudo_labels = self.pseudo_label_manager.generate_pseudo_labels(
            self.unlabeled_dataloader
        )

        if len(pseudo_labels) > 0:
            # 创建混合数据加载器
            self.mixed_dataloader = self.pseudo_label_manager.create_mixed_dataloader(
                self.train_loader,
                pseudo_labels,
                self.config.training.pseudo_label.pseudo_label_batch_ratio
            )
            logging.info(f"伪标签更新完成，生成 {len(pseudo_labels)} 个伪标签")
        else:
            logging.warning("未生成有效的伪标签")

    def _print_training_summary(self):
        """打印训练总结"""
        best_epoch = self.metrics_tracker.get_best_epoch('mIoU')
        best_metrics = self.metrics_tracker.val_metrics.get(best_epoch, {})

        logging.info("=" * 60)
        logging.info("训练总结")
        logging.info("=" * 60)
        logging.info(f"最佳Epoch: {best_epoch}")
        logging.info(f"最佳mIoU: {best_metrics.get('mIoU', 0.0):.4f}")
        logging.info(f"最佳Dice: {best_metrics.get('mDice', 0.0):.4f}")
        logging.info(f"最佳准确率: {best_metrics.get('Accuracy', 0.0):.4f}")
        logging.info("=" * 60)
