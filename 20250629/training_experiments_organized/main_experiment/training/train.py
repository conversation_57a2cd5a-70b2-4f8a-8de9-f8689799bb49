#!/usr/bin/env python3
"""
训练入口脚本
支持标准训练和伪标签训练
"""

import argparse
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config import load_config
from training.trainer import SegmentationTrainer


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="蜂巢分割模型训练")
    
    parser.add_argument(
        "--config", 
        type=str, 
        required=True,
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--resume", 
        type=str, 
        default=None,
        help="恢复训练的检查点路径"
    )
    
    parser.add_argument(
        "--output-dir", 
        type=str, 
        default=None,
        help="输出目录（覆盖配置文件中的设置）"
    )
    
    parser.add_argument(
        "--experiment-name", 
        type=str, 
        default=None,
        help="实验名称（覆盖配置文件中的设置）"
    )
    
    parser.add_argument(
        "--epochs", 
        type=int, 
        default=None,
        help="训练轮数（覆盖配置文件中的设置）"
    )
    
    parser.add_argument(
        "--batch-size", 
        type=int, 
        default=None,
        help="批次大小（覆盖配置文件中的设置）"
    )
    
    parser.add_argument(
        "--learning-rate", 
        type=float, 
        default=None,
        help="学习率（覆盖配置文件中的设置）"
    )
    
    parser.add_argument(
        "--device", 
        type=str, 
        default=None,
        help="训练设备（覆盖配置文件中的设置）"
    )
    
    parser.add_argument(
        "--debug", 
        action="store_true",
        help="启用调试模式"
    )
    
    parser.add_argument(
        "--fast-dev-run", 
        action="store_true",
        help="快速开发运行（只训练几个batch）"
    )
    
    return parser.parse_args()


def override_config(config, args):
    """根据命令行参数覆盖配置"""
    if args.output_dir:
        config.system.output_dir = args.output_dir
    
    if args.experiment_name:
        config.system.experiment_name = args.experiment_name
    
    if args.epochs:
        config.training.epochs = args.epochs
    
    if args.batch_size:
        config.data.batch_size = args.batch_size
    
    if args.learning_rate:
        config.training.learning_rate = args.learning_rate
    
    if args.device:
        config.system.device = args.device
    
    if args.debug:
        config.system.debug_mode = True
        config.data.batch_size = min(config.data.batch_size, 2)
        config.training.epochs = min(config.training.epochs, 2)
    
    if args.fast_dev_run:
        config.system.fast_dev_run = True
        config.data.batch_size = 2
        config.training.epochs = 1
    
    return config


def validate_config(config):
    """验证配置"""
    errors = config.validate()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        sys.exit(1)
    
    print("配置验证通过")


def main():
    """主函数"""
    args = parse_args()
    
    # 加载配置
    print(f"加载配置文件: {args.config}")
    config = load_config(args.config)
    
    # 覆盖配置
    config = override_config(config, args)
    
    # 验证配置
    validate_config(config)
    
    # 创建训练器
    trainer = SegmentationTrainer(config)
    
    # 设置所有组件
    trainer.setup_all()
    
    # 恢复训练（如果指定）
    if args.resume:
        print(f"恢复训练: {args.resume}")
        trainer.load_checkpoint(args.resume)
    
    # 开始训练
    try:
        trainer.train()
    except KeyboardInterrupt:
        print("\n训练被用户中断")
        # 保存当前状态
        trainer.save_checkpoint(trainer.current_epoch, is_best=False)
        print("已保存当前训练状态")
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
