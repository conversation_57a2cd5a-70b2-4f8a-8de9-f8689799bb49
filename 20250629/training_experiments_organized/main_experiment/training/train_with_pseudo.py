#!/usr/bin/env python3
"""
伪标签训练脚本
专门用于伪标签训练的入口脚本
"""

import argparse
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config import load_config
from training.trainer import SegmentationTrainer


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="蜂巢分割模型伪标签训练")
    
    parser.add_argument(
        "--config", 
        type=str, 
        required=True,
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--unlabeled-data", 
        type=str, 
        required=True,
        help="无标签数据目录"
    )
    
    parser.add_argument(
        "--pretrained-model", 
        type=str, 
        default=None,
        help="预训练模型路径（用于生成伪标签）"
    )
    
    parser.add_argument(
        "--confidence-threshold", 
        type=float, 
        default=0.9,
        help="伪标签置信度阈值"
    )
    
    parser.add_argument(
        "--pseudo-label-ratio", 
        type=float, 
        default=0.5,
        help="伪标签数据在batch中的比例"
    )
    
    parser.add_argument(
        "--start-epoch", 
        type=int, 
        default=10,
        help="开始使用伪标签的epoch"
    )
    
    parser.add_argument(
        "--update-frequency", 
        type=int, 
        default=5,
        help="伪标签更新频率（每N个epoch）"
    )
    
    parser.add_argument(
        "--output-dir", 
        type=str, 
        default=None,
        help="输出目录"
    )
    
    parser.add_argument(
        "--experiment-name", 
        type=str, 
        default="pseudo_label_training",
        help="实验名称"
    )
    
    return parser.parse_args()


def setup_pseudo_label_config(config, args):
    """设置伪标签配置"""
    # 启用伪标签训练
    config.training.pseudo_label.enable_pseudo_labeling = True
    
    # 设置无标签数据目录
    config.training.pseudo_label.unlabeled_data_dir = args.unlabeled_data
    
    # 设置伪标签参数
    config.training.pseudo_label.confidence_threshold = args.confidence_threshold
    config.training.pseudo_label.pseudo_label_batch_ratio = args.pseudo_label_ratio
    config.training.pseudo_label.start_epoch = args.start_epoch
    config.training.pseudo_label.update_frequency = args.update_frequency
    
    # 设置输出目录
    if args.output_dir:
        config.system.output_dir = args.output_dir
    config.system.experiment_name = args.experiment_name
    
    return config


def validate_pseudo_label_setup(args):
    """验证伪标签设置"""
    # 检查无标签数据目录
    if not os.path.exists(args.unlabeled_data):
        print(f"错误: 无标签数据目录不存在: {args.unlabeled_data}")
        sys.exit(1)
    
    # 检查预训练模型（如果指定）
    if args.pretrained_model and not os.path.exists(args.pretrained_model):
        print(f"错误: 预训练模型文件不存在: {args.pretrained_model}")
        sys.exit(1)
    
    # 检查参数范围
    if not 0 < args.confidence_threshold <= 1:
        print(f"错误: 置信度阈值必须在(0, 1]范围内，当前值: {args.confidence_threshold}")
        sys.exit(1)
    
    if not 0 < args.pseudo_label_ratio < 1:
        print(f"错误: 伪标签比例必须在(0, 1)范围内，当前值: {args.pseudo_label_ratio}")
        sys.exit(1)
    
    print("伪标签设置验证通过")


def print_pseudo_label_info(config):
    """打印伪标签训练信息"""
    print("=" * 60)
    print("伪标签训练配置")
    print("=" * 60)
    print(f"无标签数据目录: {config.training.pseudo_label.unlabeled_data_dir}")
    print(f"置信度阈值: {config.training.pseudo_label.confidence_threshold}")
    print(f"伪标签比例: {config.training.pseudo_label.pseudo_label_batch_ratio}")
    print(f"开始epoch: {config.training.pseudo_label.start_epoch}")
    print(f"更新频率: {config.training.pseudo_label.update_frequency}")
    print(f"伪标签策略: {config.training.pseudo_label.pseudo_label_strategy}")
    print("=" * 60)


def main():
    """主函数"""
    args = parse_args()
    
    # 验证伪标签设置
    validate_pseudo_label_setup(args)
    
    # 加载配置
    print(f"加载配置文件: {args.config}")
    config = load_config(args.config)
    
    # 设置伪标签配置
    config = setup_pseudo_label_config(config, args)
    
    # 验证配置
    errors = config.validate()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        sys.exit(1)
    
    # 打印伪标签信息
    print_pseudo_label_info(config)
    
    # 创建训练器
    trainer = SegmentationTrainer(config)
    
    # 设置所有组件
    trainer.setup_all()
    
    # 加载预训练模型（如果指定）
    if args.pretrained_model:
        print(f"加载预训练模型: {args.pretrained_model}")
        trainer.load_checkpoint(args.pretrained_model)
    
    # 开始训练
    try:
        trainer.train()
    except KeyboardInterrupt:
        print("\n训练被用户中断")
        # 保存当前状态
        trainer.save_checkpoint(trainer.current_epoch, is_best=False)
        print("已保存当前训练状态")
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
