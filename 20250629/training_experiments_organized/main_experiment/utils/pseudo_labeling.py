#!/usr/bin/env python3
"""
伪标签模块
实现半监督学习中的伪标签生成、筛选和管理功能
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import numpy as np
from PIL import Image
import cv2

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms

try:
    from ..core.config.base_config import PseudoLabelConfig
except ImportError:
    from core.config.base_config import PseudoLabelConfig

# 尝试导入学术级精炼框架
try:
    from ..feature_guided_pseudo_labeling import (
        AcademicPseudoLabelRefinementFramework,
        create_default_config as create_academic_config
    )
    ACADEMIC_REFINEMENT_AVAILABLE = True
except ImportError:
    ACADEMIC_REFINEMENT_AVAILABLE = False
    logging.warning("Academic pseudo-label refinement framework not available")

logger = logging.getLogger(__name__)


class PseudoLabelGenerator:
    """伪标签生成器"""

    def __init__(self, config: PseudoLabelConfig, num_classes: int):
        self.config = config
        self.num_classes = num_classes
        self.pseudo_labels_cache = {}

        # 创建保存目录
        if config.save_pseudo_labels:
            os.makedirs(config.pseudo_label_save_dir, exist_ok=True)


class AcademicEnhancedPseudoLabelGenerator(PseudoLabelGenerator):
    """
    学术级增强伪标签生成器

    集成特征学习伪标签精炼功能到现有的伪标签生成器中
    """

    def __init__(self, config: PseudoLabelConfig, num_classes: int,
                 enable_academic_refinement: bool = True):
        super().__init__(config, num_classes)

        self.enable_academic_refinement = enable_academic_refinement and ACADEMIC_REFINEMENT_AVAILABLE
        self.academic_framework = None
        self.framework_initialized = False

        if self.enable_academic_refinement:
            logger.info("Academic pseudo-label refinement enabled")
            self._setup_academic_framework()
        else:
            logger.info("Using standard pseudo-label generation")

    def _setup_academic_framework(self):
        """设置学术框架"""
        if not ACADEMIC_REFINEMENT_AVAILABLE:
            logger.warning("Academic refinement framework not available")
            self.enable_academic_refinement = False
            return

        try:
            # 创建学术框架配置
            academic_config = create_academic_config()
            academic_config.num_classes = self.num_classes

            # 设置为使用MAE+ViT特征提取器
            academic_config.feature_extraction.deep_backbone = 'mae_vit'
            academic_config.feature_extraction.deep_freeze_backbone = True

            # 创建学术框架
            self.academic_framework = AcademicPseudoLabelRefinementFramework(academic_config)

            logger.info("Academic framework created successfully")

        except Exception as e:
            logger.error(f"Failed to setup academic framework: {e}")
            self.enable_academic_refinement = False

    def initialize_academic_framework(self, labeled_dataset):
        """
        初始化学术框架（从标注数据学习特征分布）

        Args:
            labeled_dataset: 标注数据集
        """
        if not self.enable_academic_refinement or not self.academic_framework:
            return

        try:
            logger.info("Initializing academic framework from labeled data...")
            self.academic_framework.setup_from_labeled_data(labeled_dataset)
            self.framework_initialized = True
            logger.info("Academic framework initialized successfully!")

        except Exception as e:
            logger.error(f"Failed to initialize academic framework: {e}")
            self.framework_initialized = False

    def generate_pseudo_labels(self,
                             model: nn.Module,
                             unlabeled_dataloader: DataLoader,
                             device: torch.device,
                             epoch: int) -> Dict[str, np.ndarray]:
        """
        生成增强的伪标签

        Args:
            model: 教师模型
            unlabeled_dataloader: 无标签数据加载器
            device: 设备
            epoch: 当前epoch

        Returns:
            refined_pseudo_labels: 精炼后的伪标签字典
        """
        logger.info(f"Generating enhanced pseudo labels for epoch {epoch}...")

        # 首先使用父类方法生成原始伪标签
        original_pseudo_labels = super().generate_pseudo_labels(model, unlabeled_dataloader, device, epoch)

        # 如果学术框架未启用或未初始化，返回原始伪标签
        if not self.enable_academic_refinement or not self.framework_initialized:
            logger.info("Academic refinement not available, returning original pseudo labels")
            return original_pseudo_labels

        # 使用学术框架精炼伪标签
        refined_pseudo_labels = {}
        refinement_stats = {'total': 0, 'refined': 0, 'failed': 0}

        for img_path, original_pred in original_pseudo_labels.items():
            try:
                # 加载原始图像
                original_image = self._load_original_image(img_path)

                # 生成置信度图（如果没有的话）
                confidence_map = self._generate_confidence_map(model, original_image, device)

                # 应用学术级精炼
                refined_pred, refined_conf, refinement_info = self.academic_framework.refine_pseudo_labels(
                    original_image,
                    original_pred,
                    confidence_map,
                    method=getattr(self.config, 'academic_refinement_method', 'uncertainty_aware')
                )

                # 质量检查
                if self._quality_check_refined_label(refined_pred, refined_conf, original_pred):
                    refined_pseudo_labels[img_path] = refined_pred
                    refinement_stats['refined'] += 1
                else:
                    # 质量不达标，使用原始预测
                    refined_pseudo_labels[img_path] = original_pred

                refinement_stats['total'] += 1

            except Exception as e:
                logger.warning(f"Failed to refine pseudo label for {img_path}: {e}")
                refined_pseudo_labels[img_path] = original_pred
                refinement_stats['failed'] += 1

        # 记录精炼统计
        refinement_rate = refinement_stats['refined'] / max(refinement_stats['total'], 1)
        logger.info(f"Pseudo-label refinement completed: "
                   f"{refinement_stats['refined']}/{refinement_stats['total']} refined "
                   f"({refinement_rate:.2%}), {refinement_stats['failed']} failed")

        return refined_pseudo_labels

    def _load_original_image(self, image_path: str) -> np.ndarray:
        """加载原始图像"""
        try:
            image = Image.open(image_path).convert('RGB')
            return np.array(image)
        except Exception as e:
            logger.error(f"Failed to load image {image_path}: {e}")
            # 返回一个默认图像
            return np.zeros((512, 512, 3), dtype=np.uint8)

    def _generate_confidence_map(self, model: nn.Module, image: np.ndarray, device: torch.device) -> np.ndarray:
        """生成置信度图"""
        try:
            # 预处理图像
            transform = transforms.Compose([
                transforms.ToPILImage(),
                transforms.Resize((512, 512)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])

            image_tensor = transform(image).unsqueeze(0).to(device)

            with torch.no_grad():
                outputs = model(image_tensor)
                if isinstance(outputs, dict):
                    logits = outputs.get('out', outputs.get('logits', list(outputs.values())[0]))
                else:
                    logits = outputs

                # 计算softmax概率
                probs = F.softmax(logits, dim=1)

                # 使用最大概率作为置信度
                confidence_map = torch.max(probs, dim=1)[0]

                # 调整尺寸并转换为numpy
                confidence_map = F.interpolate(
                    confidence_map.unsqueeze(1),
                    size=image.shape[:2],
                    mode='bilinear',
                    align_corners=False
                ).squeeze().cpu().numpy()

                return confidence_map

        except Exception as e:
            logger.error(f"Failed to generate confidence map: {e}")
            # 返回默认置信度图
            return np.ones(image.shape[:2], dtype=np.float32) * 0.5

    def _quality_check_refined_label(self, refined_pred: np.ndarray,
                                   refined_conf: np.ndarray,
                                   original_pred: np.ndarray) -> bool:
        """质量检查精炼后的标签"""
        try:
            # 基本检查
            if refined_pred.size == 0:
                return False

            # 检查是否有有效预测
            non_background_pixels = (refined_pred > 0).sum()
            if non_background_pixels < 10:  # 至少要有10个非背景像素
                return False

            # 检查变化是否合理（不应该变化太大）
            changed_pixels = (refined_pred != original_pred).sum()
            total_pixels = refined_pred.size
            change_rate = changed_pixels / total_pixels

            # 如果变化超过50%，可能有问题
            if change_rate > 0.5:
                logger.warning(f"Large change rate detected: {change_rate:.2%}")
                return False

            # 检查置信度
            if refined_conf is not None:
                mean_confidence = refined_conf.mean()
                if mean_confidence < 0.2:  # 置信度太低
                    return False

            return True

        except Exception as e:
            logger.error(f"Quality check failed: {e}")
            return False

    def get_refinement_statistics(self) -> Dict:
        """获取精炼统计信息"""
        if not self.enable_academic_refinement or not self.academic_framework:
            return {"academic_refinement_enabled": False}

        try:
            framework_metrics = self.academic_framework.get_performance_metrics()
            return {
                "academic_refinement_enabled": True,
                "framework_initialized": self.framework_initialized,
                "framework_metrics": framework_metrics
            }
        except Exception as e:
            logger.error(f"Failed to get refinement statistics: {e}")
            return {"academic_refinement_enabled": True, "error": str(e)}
    
    def generate_pseudo_labels(self, 
                             model: nn.Module, 
                             unlabeled_dataloader: DataLoader,
                             device: torch.device,
                             epoch: int) -> Dict[str, np.ndarray]:
        """
        生成伪标签
        
        Args:
            model: 训练好的模型
            unlabeled_dataloader: 无标签数据加载器
            device: 计算设备
            epoch: 当前epoch
            
        Returns:
            伪标签字典 {image_path: pseudo_label}
        """
        model.eval()
        pseudo_labels = {}
        confidence_scores = {}
        
        logger.info(f"开始生成伪标签 (Epoch {epoch})...")
        
        with torch.no_grad():
            for batch_idx, (images, image_paths) in enumerate(unlabeled_dataloader):
                images = images.to(device)
                
                # 前向传播
                outputs = model(images)
                if isinstance(outputs, dict):
                    logits = outputs.get('main', outputs.get('out', outputs))
                else:
                    logits = outputs
                
                # 计算概率和预测
                probs = F.softmax(logits, dim=1)  # (B, C, H, W)
                predictions = torch.argmax(probs, dim=1)  # (B, H, W)
                
                # 计算置信度
                max_probs = torch.max(probs, dim=1)[0]  # (B, H, W)
                
                # 处理每个样本
                for i, img_path in enumerate(image_paths):
                    pred_mask = predictions[i].cpu().numpy()
                    confidence_map = max_probs[i].cpu().numpy()
                    
                    # 根据策略筛选伪标签
                    if self._should_accept_pseudo_label(confidence_map, pred_mask):
                        pseudo_labels[img_path] = pred_mask
                        confidence_scores[img_path] = confidence_map.mean()
                        
                        # 保存伪标签
                        if self.config.save_pseudo_labels:
                            self._save_pseudo_label(img_path, pred_mask, epoch)
        
        logger.info(f"生成了 {len(pseudo_labels)} 个伪标签")
        
        # 质量控制
        pseudo_labels = self._quality_control(pseudo_labels, confidence_scores)
        
        return pseudo_labels
    
    def _should_accept_pseudo_label(self, confidence_map: np.ndarray, pred_mask: np.ndarray) -> bool:
        """判断是否接受伪标签"""
        
        if self.config.pseudo_label_strategy == "confidence_threshold":
            # 基于置信度阈值
            mean_confidence = confidence_map.mean()
            return mean_confidence >= self.config.confidence_threshold
            
        elif self.config.pseudo_label_strategy == "entropy_threshold":
            # 基于熵阈值
            entropy = -np.sum(confidence_map * np.log(confidence_map + 1e-8))
            normalized_entropy = entropy / np.log(self.num_classes)
            return normalized_entropy <= self.config.entropy_threshold
            
        elif self.config.pseudo_label_strategy == "top_k":
            # 基于top-k策略（需要在批次级别实现）
            return True  # 在质量控制阶段处理
            
        else:
            logger.warning(f"未知的伪标签策略: {self.config.pseudo_label_strategy}")
            return False
    
    def _quality_control(self, pseudo_labels: Dict[str, np.ndarray], 
                        confidence_scores: Dict[str, float]) -> Dict[str, np.ndarray]:
        """伪标签质量控制"""
        
        if self.config.pseudo_label_strategy == "top_k":
            # Top-k策略：选择置信度最高的k%样本
            sorted_items = sorted(confidence_scores.items(), key=lambda x: x[1], reverse=True)
            k = int(len(sorted_items) * self.config.top_k_ratio)
            selected_paths = [item[0] for item in sorted_items[:k]]
            pseudo_labels = {path: pseudo_labels[path] for path in selected_paths}
        
        # 数量控制
        if len(pseudo_labels) > self.config.max_pseudo_samples:
            # 选择置信度最高的样本
            sorted_items = sorted(confidence_scores.items(), key=lambda x: x[1], reverse=True)
            selected_paths = [item[0] for item in sorted_items[:self.config.max_pseudo_samples]]
            pseudo_labels = {path: pseudo_labels[path] for path in selected_paths}
        
        # 最少样本检查
        if len(pseudo_labels) < self.config.min_pseudo_samples:
            logger.warning(f"伪标签数量 ({len(pseudo_labels)}) 少于最小要求 ({self.config.min_pseudo_samples})")
        
        return pseudo_labels
    
    def _save_pseudo_label(self, image_path: str, pseudo_label: np.ndarray, epoch: int):
        """保存伪标签"""
        # 创建保存路径
        image_name = Path(image_path).stem
        save_path = Path(self.config.pseudo_label_save_dir) / f"epoch_{epoch}" / f"{image_name}.png"
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存为PNG图像
        Image.fromarray(pseudo_label.astype(np.uint8)).save(save_path)


class PseudoLabelDataset(Dataset):
    """伪标签数据集"""
    
    def __init__(self, pseudo_labels: Dict[str, np.ndarray], transform=None):
        self.pseudo_labels = pseudo_labels
        self.image_paths = list(pseudo_labels.keys())
        self.transform = transform
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        pseudo_label = self.pseudo_labels[image_path]
        
        # 加载图像
        image = Image.open(image_path).convert('RGB')
        
        if self.transform:
            # 注意：需要同时变换图像和标签
            image = self.transform(image)
            # 伪标签通常不需要变换，或者需要特殊处理
        
        return image, torch.from_numpy(pseudo_label).long(), image_path


class UnlabeledDataset(Dataset):
    """无标签数据集"""
    
    def __init__(self, data_dir: str, transform=None):
        self.data_dir = Path(data_dir)
        self.transform = transform
        
        # 支持的图像格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        # 收集所有图像文件
        self.image_paths = []
        for ext in image_extensions:
            self.image_paths.extend(list(self.data_dir.glob(f"**/*{ext}")))
            self.image_paths.extend(list(self.data_dir.glob(f"**/*{ext.upper()}")))
        
        logger.info(f"找到 {len(self.image_paths)} 个无标签图像")
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = str(self.image_paths[idx])
        image = Image.open(image_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
        
        return image, image_path


class PseudoLabelManager:
    """伪标签管理器"""
    
    def __init__(self, config: PseudoLabelConfig, num_classes: int):
        self.config = config
        self.generator = PseudoLabelGenerator(config, num_classes)
        self.current_pseudo_labels = {}
        self.pseudo_label_history = []
    
    def should_update_pseudo_labels(self, epoch: int) -> bool:
        """判断是否应该更新伪标签"""
        if not self.config.enable_pseudo_labeling:
            return False
        
        if epoch < self.config.start_epoch:
            return False
        
        if (epoch - self.config.start_epoch) % self.config.update_frequency == 0:
            return True
        
        return False
    
    def update_pseudo_labels(self, model: nn.Module, unlabeled_dataloader: DataLoader, 
                           device: torch.device, epoch: int) -> Optional[Dataset]:
        """更新伪标签并返回伪标签数据集"""
        if not self.should_update_pseudo_labels(epoch):
            return None
        
        # 生成新的伪标签
        new_pseudo_labels = self.generator.generate_pseudo_labels(
            model, unlabeled_dataloader, device, epoch
        )
        
        # 更新当前伪标签
        self.current_pseudo_labels = new_pseudo_labels
        self.pseudo_label_history.append({
            'epoch': epoch,
            'num_labels': len(new_pseudo_labels),
            'avg_confidence': np.mean([conf for conf in new_pseudo_labels.values()]) if new_pseudo_labels else 0
        })
        
        # 创建伪标签数据集
        if new_pseudo_labels:
            # 这里需要定义适当的变换
            transform = transforms.Compose([
                transforms.Resize((224, 224)),  # 根据实际需求调整
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
            
            pseudo_dataset = PseudoLabelDataset(new_pseudo_labels, transform=transform)
            return pseudo_dataset
        
        return None
    
    def get_statistics(self) -> Dict:
        """获取伪标签统计信息"""
        return {
            'current_num_labels': len(self.current_pseudo_labels),
            'history': self.pseudo_label_history,
            'config': self.config
        }


def create_unlabeled_dataloader(config: PseudoLabelConfig, 
                              batch_size: int = 32,
                              num_workers: int = 4) -> Optional[DataLoader]:
    """创建无标签数据加载器"""
    if not config.enable_pseudo_labeling or not config.unlabeled_data_dir:
        return None
    
    if not os.path.exists(config.unlabeled_data_dir):
        logger.warning(f"无标签数据目录不存在: {config.unlabeled_data_dir}")
        return None
    
    # 定义变换
    transform = transforms.Compose([
        transforms.Resize((224, 224)),  # 根据实际需求调整
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    dataset = UnlabeledDataset(config.unlabeled_data_dir, transform=transform)
    
    if len(dataset) == 0:
        logger.warning("无标签数据集为空")
        return None
    
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=False,  # 生成伪标签时不需要打乱
        num_workers=num_workers,
        pin_memory=True
    )
    
    return dataloader
