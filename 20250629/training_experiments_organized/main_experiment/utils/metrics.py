#!/usr/bin/env python3
"""
分割任务评估指标
包含IoU、<PERSON><PERSON>、Accuracy等常用分割指标的计算
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, List, Union
import numpy as np
from sklearn.metrics import confusion_matrix, accuracy_score, precision_recall_fscore_support
import time


class SegmentationMetrics:
    """分割任务评估指标"""
    
    def __init__(self, num_classes: int, ignore_index: int = -100, class_names: Optional[List[str]] = None):
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        self.class_names = class_names or self._get_default_class_names()
        self.reset()
    
    def _get_default_class_names(self) -> List[str]:
        """获取默认类别名称"""
        if self.num_classes == 8:
            return ['background', 'eggs', 'larvae', 'capped_brood', 'pollen', 'nectar', 'honey', 'other']
        elif self.num_classes == 2:
            return ['background', 'honeycomb']
        else:
            return [f'class_{i}' for i in range(self.num_classes)]
    
    def reset(self):
        """重置指标"""
        self.total_samples = 0
        self.confusion_matrix = np.zeros((self.num_classes, self.num_classes))
        self.inference_times = []
    
    def update(self, pred: torch.Tensor, target: torch.Tensor, inference_time: Optional[float] = None):
        """
        更新指标
        
        Args:
            pred: 预测结果 [B, C, H, W] 或 [B, H, W]
            target: 真实标签 [B, H, W]
            inference_time: 推理时间（秒）
        """
        # 如果pred是logits，转换为类别预测
        if pred.dim() == 4:
            pred = torch.argmax(pred, dim=1)
        
        # 转换为numpy
        pred_np = pred.cpu().numpy().flatten()
        target_np = target.cpu().numpy().flatten()
        
        # 移除ignore_index
        if self.ignore_index >= 0:
            mask = target_np != self.ignore_index
            pred_np = pred_np[mask]
            target_np = target_np[mask]
        
        # 更新混淆矩阵
        if len(pred_np) > 0:  # 确保有有效样本
            cm = confusion_matrix(
                target_np, pred_np,
                labels=list(range(self.num_classes))
            )
            self.confusion_matrix += cm
            self.total_samples += len(target_np)
        
        # 记录推理时间
        if inference_time is not None:
            self.inference_times.append(inference_time)
    
    def compute_iou(self) -> Dict[str, float]:
        """计算IoU指标"""
        # 计算每个类别的IoU
        intersection = np.diag(self.confusion_matrix)
        union = (
            self.confusion_matrix.sum(axis=1) +
            self.confusion_matrix.sum(axis=0) -
            intersection
        )
        
        # 避免除零
        iou = intersection / (union + 1e-8)
        
        # 计算平均IoU（忽略背景类）
        if self.num_classes > 1:
            miou = np.mean(iou[1:])  # 忽略背景类
        else:
            miou = iou[0]
        
        results = {
            'mIoU': miou,
            'IoU_mean': np.mean(iou),
        }
        
        # 添加每个类别的IoU
        for i, name in enumerate(self.class_names):
            results[f'IoU_{name}'] = iou[i]
        
        return results
    
    def compute_dice(self) -> Dict[str, float]:
        """计算Dice系数"""
        # 计算每个类别的Dice系数
        intersection = np.diag(self.confusion_matrix)
        dice_denominator = (
            self.confusion_matrix.sum(axis=1) +
            self.confusion_matrix.sum(axis=0)
        )
        
        # 避免除零
        dice = (2.0 * intersection) / (dice_denominator + 1e-8)
        
        # 计算平均Dice（忽略背景类）
        if self.num_classes > 1:
            mdice = np.mean(dice[1:])  # 忽略背景类
        else:
            mdice = dice[0]
        
        results = {
            'mDice': mdice,
            'Dice_mean': np.mean(dice),
        }
        
        # 添加每个类别的Dice
        for i, name in enumerate(self.class_names):
            results[f'Dice_{name}'] = dice[i]
        
        return results
    
    def compute_accuracy(self) -> Dict[str, float]:
        """计算准确率"""
        # 总体准确率
        total_correct = np.diag(self.confusion_matrix).sum()
        total_samples = self.confusion_matrix.sum()
        overall_accuracy = total_correct / (total_samples + 1e-8)
        
        # 每个类别的准确率（召回率）
        class_accuracy = np.diag(self.confusion_matrix) / (self.confusion_matrix.sum(axis=1) + 1e-8)
        
        results = {
            'Accuracy': overall_accuracy,
            'mAccuracy': np.mean(class_accuracy)
        }
        
        # 添加每个类别的准确率
        for i, name in enumerate(self.class_names):
            results[f'Accuracy_{name}'] = class_accuracy[i]
        
        return results
    
    def compute_precision_recall(self) -> Dict[str, float]:
        """计算精确率和召回率"""
        # 精确率 = TP / (TP + FP)
        precision = np.diag(self.confusion_matrix) / (self.confusion_matrix.sum(axis=0) + 1e-8)
        
        # 召回率 = TP / (TP + FN)
        recall = np.diag(self.confusion_matrix) / (self.confusion_matrix.sum(axis=1) + 1e-8)
        
        # F1分数
        f1 = 2 * (precision * recall) / (precision + recall + 1e-8)
        
        results = {
            'mPrecision': np.mean(precision),
            'mRecall': np.mean(recall),
            'mF1': np.mean(f1)
        }
        
        # 添加每个类别的指标
        for i, name in enumerate(self.class_names):
            results[f'Precision_{name}'] = precision[i]
            results[f'Recall_{name}'] = recall[i]
            results[f'F1_{name}'] = f1[i]
        
        return results
    
    def compute_speed_metrics(self) -> Dict[str, float]:
        """计算速度指标"""
        if not self.inference_times:
            return {
                'avg_inference_time': 0.0,
                'fps': 0.0,
                'total_inference_time': 0.0
            }
        
        avg_time = np.mean(self.inference_times)
        total_time = np.sum(self.inference_times)
        fps = 1.0 / avg_time if avg_time > 0 else 0.0
        
        return {
            'avg_inference_time': avg_time,
            'fps': fps,
            'total_inference_time': total_time,
            'num_inferences': len(self.inference_times)
        }
    
    def compute_all_metrics(self) -> Dict[str, float]:
        """计算所有指标"""
        results = {}
        results.update(self.compute_iou())
        results.update(self.compute_dice())
        results.update(self.compute_accuracy())
        results.update(self.compute_precision_recall())
        results.update(self.compute_speed_metrics())

        return results

    def compute(self) -> Dict[str, float]:
        """计算主要指标（兼容训练脚本）"""
        all_metrics = self.compute_all_metrics()

        # 创建兼容的键名映射
        result = {
            'mean_iou': all_metrics.get('mIoU', 0.0),
            'pixel_accuracy': all_metrics.get('Accuracy', 0.0),
            'dice_score': all_metrics.get('mDice', 0.0),
            'precision': all_metrics.get('mPrecision', 0.0),
            'recall': all_metrics.get('mRecall', 0.0),
            'f1_score': all_metrics.get('mF1', 0.0),
            # 保留原始键名
            **all_metrics
        }

        return result

    def save_detailed_metrics(self, save_path: str, epoch: int = None):
        """保存详细指标到文件"""
        import json
        import csv
        from pathlib import Path

        save_path = Path(save_path)
        save_path.parent.mkdir(parents=True, exist_ok=True)

        # 计算所有指标
        all_metrics = self.compute_all_metrics()

        # 准备详细数据
        detailed_data = {
            'epoch': epoch,
            'timestamp': time.time(),
            'summary_metrics': {
                'mIoU': all_metrics.get('mIoU', 0.0),
                'mDice': all_metrics.get('mDice', 0.0),
                'Accuracy': all_metrics.get('Accuracy', 0.0),
                'mPrecision': all_metrics.get('mPrecision', 0.0),
                'mRecall': all_metrics.get('mRecall', 0.0),
                'mF1': all_metrics.get('mF1', 0.0)
            },
            'per_class_metrics': {}
        }

        # 每个类别的详细指标
        for i, class_name in enumerate(self.class_names):
            detailed_data['per_class_metrics'][class_name] = {
                'IoU': all_metrics.get(f'IoU_{class_name}', 0.0),
                'Dice': all_metrics.get(f'Dice_{class_name}', 0.0),
                'Accuracy': all_metrics.get(f'Accuracy_{class_name}', 0.0),
                'Precision': all_metrics.get(f'Precision_{class_name}', 0.0),
                'Recall': all_metrics.get(f'Recall_{class_name}', 0.0),
                'F1': all_metrics.get(f'F1_{class_name}', 0.0)
            }

        # 保存为JSON
        json_path = save_path.with_suffix('.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(detailed_data, f, indent=2, ensure_ascii=False)

        # 保存为CSV（便于绘图）
        csv_path = save_path.with_suffix('.csv')
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # 写入表头
            header = ['epoch', 'class_name', 'IoU', 'Dice', 'Accuracy', 'Precision', 'Recall', 'F1']
            writer.writerow(header)

            # 写入每个类别的数据
            for class_name in self.class_names:
                row = [
                    epoch if epoch is not None else '',
                    class_name,
                    detailed_data['per_class_metrics'][class_name]['IoU'],
                    detailed_data['per_class_metrics'][class_name]['Dice'],
                    detailed_data['per_class_metrics'][class_name]['Accuracy'],
                    detailed_data['per_class_metrics'][class_name]['Precision'],
                    detailed_data['per_class_metrics'][class_name]['Recall'],
                    detailed_data['per_class_metrics'][class_name]['F1']
                ]
                writer.writerow(row)

        return json_path, csv_path

    def save_confusion_matrix(self, save_path: str, epoch: int = None):
        """保存混淆矩阵"""
        import numpy as np
        from pathlib import Path

        save_path = Path(save_path)
        save_path.parent.mkdir(parents=True, exist_ok=True)

        # 保存混淆矩阵为numpy文件
        np_path = save_path.with_suffix('.npy')
        np.save(np_path, self.confusion_matrix)

        # 保存为CSV格式
        csv_path = save_path.with_suffix('.csv')
        np.savetxt(csv_path, self.confusion_matrix, delimiter=',', fmt='%d')

        # 保存带标签的CSV
        labeled_csv_path = save_path.with_name(save_path.stem + '_labeled.csv')
        with open(labeled_csv_path, 'w', newline='', encoding='utf-8') as f:
            import csv
            writer = csv.writer(f)

            # 写入表头
            header = [''] + self.class_names
            writer.writerow(header)

            # 写入数据
            for i, class_name in enumerate(self.class_names):
                row = [class_name] + self.confusion_matrix[i].tolist()
                writer.writerow(row)

        return np_path, csv_path, labeled_csv_path
    
    def get_confusion_matrix(self) -> np.ndarray:
        """获取混淆矩阵"""
        return self.confusion_matrix.copy()
    
    def print_summary(self):
        """打印指标摘要"""
        metrics = self.compute_all_metrics()
        
        print("=" * 60)
        print("分割指标摘要")
        print("=" * 60)
        print(f"总样本数: {self.total_samples:,}")
        print(f"类别数量: {self.num_classes}")
        print()
        
        print("主要指标:")
        print(f"  mIoU:      {metrics['mIoU']:.4f}")
        print(f"  mDice:     {metrics['mDice']:.4f}")
        print(f"  Accuracy:  {metrics['Accuracy']:.4f}")
        print(f"  mPrecision: {metrics['mPrecision']:.4f}")
        print(f"  mRecall:   {metrics['mRecall']:.4f}")
        print(f"  mF1:       {metrics['mF1']:.4f}")
        print()
        
        if self.inference_times:
            print("速度指标:")
            print(f"  平均推理时间: {metrics['avg_inference_time']:.4f}s")
            print(f"  FPS:         {metrics['fps']:.2f}")
            print()
        
        print("各类别IoU:")
        for name in self.class_names:
            iou_key = f'IoU_{name}'
            if iou_key in metrics:
                print(f"  {name:12}: {metrics[iou_key]:.4f}")


class MetricsTracker:
    """指标跟踪器，用于训练过程中的指标记录"""
    
    def __init__(self):
        self.train_metrics = {}
        self.val_metrics = {}
        self.epoch_history = []
    
    def update_train_metrics(self, metrics: Dict[str, float], epoch: int):
        """更新训练指标"""
        if epoch not in self.train_metrics:
            self.train_metrics[epoch] = {}
        self.train_metrics[epoch].update(metrics)
    
    def update_val_metrics(self, metrics: Dict[str, float], epoch: int):
        """更新验证指标"""
        if epoch not in self.val_metrics:
            self.val_metrics[epoch] = {}
        self.val_metrics[epoch].update(metrics)
        
        # 记录epoch历史
        epoch_data = {
            'epoch': epoch,
            'train': self.train_metrics.get(epoch, {}),
            'val': metrics
        }
        self.epoch_history.append(epoch_data)
    
    def get_best_epoch(self, metric_name: str = 'mIoU', mode: str = 'max') -> int:
        """获取最佳epoch"""
        if not self.val_metrics:
            return 0
        
        best_epoch = 0
        best_value = float('-inf') if mode == 'max' else float('inf')
        
        for epoch, metrics in self.val_metrics.items():
            if metric_name in metrics:
                value = metrics[metric_name]
                if (mode == 'max' and value > best_value) or (mode == 'min' and value < best_value):
                    best_value = value
                    best_epoch = epoch
        
        return best_epoch
    
    def get_metric_history(self, metric_name: str) -> Dict[str, List]:
        """获取指标历史"""
        train_history = []
        val_history = []
        epochs = []
        
        for epoch_data in self.epoch_history:
            epochs.append(epoch_data['epoch'])
            train_history.append(epoch_data['train'].get(metric_name, 0.0))
            val_history.append(epoch_data['val'].get(metric_name, 0.0))
        
        return {
            'epochs': epochs,
            'train': train_history,
            'val': val_history
        }
