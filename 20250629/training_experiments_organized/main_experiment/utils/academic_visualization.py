#!/usr/bin/env python3
"""
学术论文可视化工具
生成高质量的学术图表用于论文发表
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from pathlib import Path
import json
from typing import List, Dict, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置学术风格
plt.style.use('default')
sns.set_palette("husl")

# 学术论文标准配置
ACADEMIC_CONFIG = {
    'figure_size': (10, 6),
    'dpi': 300,
    'font_size': 12,
    'title_size': 14,
    'label_size': 12,
    'legend_size': 10,
    'line_width': 2,
    'marker_size': 6,
    'colors': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
}

class AcademicVisualizer:
    """学术可视化器"""
    
    def __init__(self, output_dir: str = "academic_figures"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置matplotlib参数
        plt.rcParams.update({
            'font.size': ACADEMIC_CONFIG['font_size'],
            'axes.titlesize': ACADEMIC_CONFIG['title_size'],
            'axes.labelsize': ACADEMIC_CONFIG['label_size'],
            'legend.fontsize': ACADEMIC_CONFIG['legend_size'],
            'lines.linewidth': ACADEMIC_CONFIG['line_width'],
            'lines.markersize': ACADEMIC_CONFIG['marker_size'],
            'figure.dpi': ACADEMIC_CONFIG['dpi'],
            'savefig.dpi': ACADEMIC_CONFIG['dpi'],
            'savefig.bbox': 'tight',
            'axes.spines.top': True,
            'axes.spines.right': True,
            'axes.spines.bottom': True,
            'axes.spines.left': True,
        })
    
    def plot_training_curves(self, metrics_dir: str, save_name: str = "training_curves"):
        """绘制训练曲线"""
        metrics_dir = Path(metrics_dir)
        
        # 收集所有epoch的指标
        epochs = []
        train_losses = []
        val_losses = []
        val_mious = []
        val_accuracies = []
        
        # 读取所有指标文件
        for json_file in sorted(metrics_dir.glob("epoch_*_metrics.json")):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                epoch = data.get('epoch', 0)
                epochs.append(epoch)
                val_mious.append(data['summary_metrics']['mIoU'])
                val_accuracies.append(data['summary_metrics']['Accuracy'])
                
            except Exception as e:
                print(f"读取 {json_file} 失败: {e}")
        
        if not epochs:
            print("未找到指标数据")
            return
        
        # 创建子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. mIoU曲线
        ax1.plot(epochs, val_mious, 'b-', linewidth=2, marker='o', markersize=4)
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Mean IoU')
        ax1.set_title('Validation Mean IoU')
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1)
        
        # 2. 准确率曲线
        ax2.plot(epochs, val_accuracies, 'g-', linewidth=2, marker='s', markersize=4)
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Pixel Accuracy')
        ax2.set_title('Validation Pixel Accuracy')
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 1)
        
        # 3. 学习率曲线（如果有数据）
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.set_title('Learning Rate Schedule')
        ax3.grid(True, alpha=0.3)
        ax3.text(0.5, 0.5, 'Learning Rate Data\nNot Available', 
                ha='center', va='center', transform=ax3.transAxes)
        
        # 4. 综合指标对比
        ax4.plot(epochs, val_mious, 'b-', label='mIoU', linewidth=2)
        ax4.plot(epochs, val_accuracies, 'g-', label='Accuracy', linewidth=2)
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('Score')
        ax4.set_title('Validation Metrics Comparison')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.set_ylim(0, 1)
        
        plt.tight_layout()
        save_path = self.output_dir / f"{save_name}.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"训练曲线已保存: {save_path}")
        return save_path
    
    def plot_class_performance(self, metrics_dir: str, save_name: str = "class_performance"):
        """绘制各类别性能对比"""
        metrics_dir = Path(metrics_dir)
        
        # 找到最新的指标文件
        latest_metrics = None
        for json_file in sorted(metrics_dir.glob("epoch_*_metrics.json"), reverse=True):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    latest_metrics = json.load(f)
                break
            except:
                continue
        
        if not latest_metrics:
            print("未找到指标数据")
            return
        
        # 准备数据
        class_names = list(latest_metrics['per_class_metrics'].keys())
        metrics_names = ['IoU', 'Dice', 'Precision', 'Recall', 'F1']
        
        # 创建数据矩阵
        data_matrix = []
        for metric in metrics_names:
            row = [latest_metrics['per_class_metrics'][cls][metric] for cls in class_names]
            data_matrix.append(row)
        
        # 创建热力图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
        
        # 1. 热力图
        im = ax1.imshow(data_matrix, cmap='RdYlBu_r', aspect='auto', vmin=0, vmax=1)
        ax1.set_xticks(range(len(class_names)))
        ax1.set_yticks(range(len(metrics_names)))
        ax1.set_xticklabels(class_names, rotation=45, ha='right')
        ax1.set_yticklabels(metrics_names)
        ax1.set_title('Per-Class Performance Heatmap')
        
        # 添加数值标注
        for i in range(len(metrics_names)):
            for j in range(len(class_names)):
                text = ax1.text(j, i, f'{data_matrix[i][j]:.3f}',
                               ha="center", va="center", color="black", fontsize=9)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax1)
        cbar.set_label('Score')
        
        # 2. 柱状图对比
        x = np.arange(len(class_names))
        width = 0.15
        
        for i, metric in enumerate(metrics_names):
            values = [latest_metrics['per_class_metrics'][cls][metric] for cls in class_names]
            ax2.bar(x + i * width, values, width, label=metric, alpha=0.8)
        
        ax2.set_xlabel('Class')
        ax2.set_ylabel('Score')
        ax2.set_title('Per-Class Performance Comparison')
        ax2.set_xticks(x + width * 2)
        ax2.set_xticklabels(class_names, rotation=45, ha='right')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 1)
        
        plt.tight_layout()
        save_path = self.output_dir / f"{save_name}.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"类别性能图已保存: {save_path}")
        return save_path
    
    def plot_confusion_matrix(self, confusion_matrix_path: str, class_names: List[str], 
                            save_name: str = "confusion_matrix"):
        """绘制混淆矩阵"""
        # 读取混淆矩阵
        if confusion_matrix_path.endswith('.npy'):
            cm = np.load(confusion_matrix_path)
        else:
            cm = np.loadtxt(confusion_matrix_path, delimiter=',')
        
        # 归一化
        cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
        
        # 1. 原始计数
        im1 = ax1.imshow(cm, interpolation='nearest', cmap='Blues')
        ax1.set_title('Confusion Matrix (Counts)')
        ax1.set_xlabel('Predicted Label')
        ax1.set_ylabel('True Label')
        
        # 设置刻度
        tick_marks = np.arange(len(class_names))
        ax1.set_xticks(tick_marks)
        ax1.set_yticks(tick_marks)
        ax1.set_xticklabels(class_names, rotation=45, ha='right')
        ax1.set_yticklabels(class_names)
        
        # 添加数值标注
        thresh = cm.max() / 2.
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                ax1.text(j, i, format(cm[i, j], 'd'),
                        ha="center", va="center",
                        color="white" if cm[i, j] > thresh else "black")
        
        plt.colorbar(im1, ax=ax1)
        
        # 2. 归一化百分比
        im2 = ax2.imshow(cm_normalized, interpolation='nearest', cmap='Blues')
        ax2.set_title('Confusion Matrix (Normalized)')
        ax2.set_xlabel('Predicted Label')
        ax2.set_ylabel('True Label')
        
        ax2.set_xticks(tick_marks)
        ax2.set_yticks(tick_marks)
        ax2.set_xticklabels(class_names, rotation=45, ha='right')
        ax2.set_yticklabels(class_names)
        
        # 添加百分比标注
        thresh = cm_normalized.max() / 2.
        for i in range(cm_normalized.shape[0]):
            for j in range(cm_normalized.shape[1]):
                ax2.text(j, i, format(cm_normalized[i, j], '.2f'),
                        ha="center", va="center",
                        color="white" if cm_normalized[i, j] > thresh else "black")
        
        plt.colorbar(im2, ax=ax2)
        
        plt.tight_layout()
        save_path = self.output_dir / f"{save_name}.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"混淆矩阵已保存: {save_path}")
        return save_path
    
    def generate_all_figures(self, metrics_dir: str, class_names: List[str]):
        """生成所有学术图表"""
        print("🎨 生成学术论文图表...")
        
        figures = {}
        
        # 1. 训练曲线
        try:
            figures['training_curves'] = self.plot_training_curves(metrics_dir)
        except Exception as e:
            print(f"生成训练曲线失败: {e}")
        
        # 2. 类别性能
        try:
            figures['class_performance'] = self.plot_class_performance(metrics_dir)
        except Exception as e:
            print(f"生成类别性能图失败: {e}")
        
        # 3. 混淆矩阵
        try:
            metrics_dir_path = Path(metrics_dir)
            cm_files = list(metrics_dir_path.glob("*confusion_matrix.npy"))
            if cm_files:
                latest_cm = max(cm_files, key=lambda x: x.stat().st_mtime)
                figures['confusion_matrix'] = self.plot_confusion_matrix(
                    str(latest_cm), class_names
                )
        except Exception as e:
            print(f"生成混淆矩阵失败: {e}")
        
        print(f"✅ 学术图表生成完成，保存在: {self.output_dir}")
        return figures

def main():
    """测试函数"""
    # 示例用法
    class_names = ['background', 'cell_wall', 'cell_interior', 'honey', 
                   'pollen', 'larvae', 'capped_honey', 'empty_cell']
    
    visualizer = AcademicVisualizer("test_academic_figures")
    
    # 假设有指标目录
    metrics_dir = "outputs/semantic_segmentation/metrics"
    
    if Path(metrics_dir).exists():
        figures = visualizer.generate_all_figures(metrics_dir, class_names)
        print("生成的图表:", figures)
    else:
        print(f"指标目录不存在: {metrics_dir}")

if __name__ == "__main__":
    main()
