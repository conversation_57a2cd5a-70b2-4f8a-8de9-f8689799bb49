#!/usr/bin/env python3
"""
滑动窗口处理器
专门为蜂巢图像语义分割设计的滑动窗口数据预处理工具

功能：
1. 大图像滑动窗口分块提取
2. 重叠区域加权融合重组
3. 边界语义连续性保证
4. 质量控制和有效性检查

Author: AI Assistant
Date: 2024
"""

import numpy as np
import cv2
from PIL import Image
import torch
import torch.nn.functional as F
from typing import List, Tuple, Optional, Dict, Union
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SlidingWindowProcessor:
    """
    滑动窗口处理器
    
    专门为蜂巢图像语义分割设计，支持大图像的patch分块和重组
    """
    
    def __init__(
        self,
        patch_size: int = 512,
        overlap_ratio: float = 0.25,
        min_valid_pixels: float = 0.1,
        padding_mode: str = 'reflect'
    ):
        """
        Args:
            patch_size: patch大小，推荐512或1024
            overlap_ratio: 重叠比例，0.25表示25%重叠
            min_valid_pixels: 最小有效像素比例，过滤空白patch
            padding_mode: 边界填充模式 ('reflect', 'constant', 'edge')
        """
        self.patch_size = patch_size
        self.overlap_ratio = overlap_ratio
        self.stride = int(patch_size * (1 - overlap_ratio))
        self.min_valid_pixels = min_valid_pixels
        self.padding_mode = padding_mode
        
        logger.info(f"滑动窗口配置: patch_size={patch_size}, overlap_ratio={overlap_ratio}, stride={self.stride}")
    
    def extract_patches(
        self, 
        image: np.ndarray, 
        mask: Optional[np.ndarray] = None
    ) -> Dict[str, Union[List[np.ndarray], List[Tuple[int, int]]]]:
        """
        从大图像中提取重叠的patch
        
        Args:
            image: 输入图像 (H, W, C) 或 (H, W)
            mask: 对应的mask (H, W)，可选
            
        Returns:
            包含patches、positions等信息的字典
        """
        if len(image.shape) == 2:
            image = np.expand_dims(image, axis=-1)
        
        h, w = image.shape[:2]
        
        # 计算需要的padding
        pad_h = self._calculate_padding(h)
        pad_w = self._calculate_padding(w)
        
        # 填充图像
        if pad_h > 0 or pad_w > 0:
            if len(image.shape) == 3:
                image = np.pad(image, ((0, pad_h), (0, pad_w), (0, 0)), mode=self.padding_mode)
            else:
                image = np.pad(image, ((0, pad_h), (0, pad_w)), mode=self.padding_mode)
            
            if mask is not None:
                mask = np.pad(mask, ((0, pad_h), (0, pad_w)), mode='constant', constant_values=0)
        
        # 提取patches
        patches = []
        mask_patches = []
        positions = []
        
        new_h, new_w = image.shape[:2]
        
        for y in range(0, new_h - self.patch_size + 1, self.stride):
            for x in range(0, new_w - self.patch_size + 1, self.stride):
                # 提取patch
                patch = image[y:y+self.patch_size, x:x+self.patch_size]
                
                # 质量检查
                if self._is_valid_patch(patch):
                    patches.append(patch)
                    positions.append((y, x))
                    
                    if mask is not None:
                        mask_patch = mask[y:y+self.patch_size, x:x+self.patch_size]
                        mask_patches.append(mask_patch)
        
        result = {
            'patches': patches,
            'positions': positions,
            'original_shape': (h, w),
            'padded_shape': (new_h, new_w),
            'padding': (pad_h, pad_w)
        }
        
        if mask is not None:
            result['mask_patches'] = mask_patches
        
        logger.info(f"提取了 {len(patches)} 个有效patches，原始尺寸: {(h, w)}")
        
        return result
    
    def reconstruct_from_patches(
        self,
        patches: List[np.ndarray],
        positions: List[Tuple[int, int]],
        padded_shape: Tuple[int, int],
        original_shape: Tuple[int, int],
        padding: Tuple[int, int]
    ) -> np.ndarray:
        """
        从patches重组完整图像，处理重叠区域
        
        Args:
            patches: patch列表
            positions: 每个patch的位置
            padded_shape: 填充后的图像尺寸
            original_shape: 原始图像尺寸
            padding: 填充大小
            
        Returns:
            重组后的图像
        """
        if not patches:
            raise ValueError("patches列表为空")
        
        # 确定输出维度
        if len(patches[0].shape) == 3:
            output_shape = padded_shape + (patches[0].shape[-1],)
        else:
            output_shape = padded_shape
        
        # 初始化输出数组和权重数组
        reconstructed = np.zeros(output_shape, dtype=np.float32)
        weights = np.zeros(padded_shape, dtype=np.float32)
        
        # 创建权重模板（中心权重高，边缘权重低）
        weight_template = self._create_weight_template()
        
        # 累积patches
        for patch, (y, x) in zip(patches, positions):
            if len(patch.shape) == 3:
                reconstructed[y:y+self.patch_size, x:x+self.patch_size] += patch * weight_template[:, :, np.newaxis]
            else:
                reconstructed[y:y+self.patch_size, x:x+self.patch_size] += patch * weight_template
            
            weights[y:y+self.patch_size, x:x+self.patch_size] += weight_template
        
        # 归一化
        weights[weights == 0] = 1  # 避免除零
        if len(reconstructed.shape) == 3:
            reconstructed = reconstructed / weights[:, :, np.newaxis]
        else:
            reconstructed = reconstructed / weights
        
        # 移除padding，恢复原始尺寸
        pad_h, pad_w = padding
        if pad_h > 0:
            reconstructed = reconstructed[:-pad_h]
        if pad_w > 0:
            reconstructed = reconstructed[:, :-pad_w]
        
        return reconstructed.astype(patches[0].dtype)
    
    def _calculate_padding(self, size: int) -> int:
        """计算需要的padding大小"""
        if size <= self.patch_size:
            return self.patch_size - size
        
        # 计算需要多少个stride才能覆盖整个图像
        num_strides = (size - self.patch_size + self.stride - 1) // self.stride
        covered_size = num_strides * self.stride + self.patch_size
        
        return max(0, covered_size - size)
    
    def _is_valid_patch(self, patch: np.ndarray) -> bool:
        """检查patch是否有效（不是空白区域）"""
        # 计算非零像素比例
        if len(patch.shape) == 3:
            non_zero_ratio = np.count_nonzero(patch.sum(axis=-1)) / (patch.shape[0] * patch.shape[1])
        else:
            non_zero_ratio = np.count_nonzero(patch) / patch.size
        
        return non_zero_ratio >= self.min_valid_pixels
    
    def _create_weight_template(self) -> np.ndarray:
        """创建权重模板，中心权重高，边缘权重低"""
        # 使用高斯权重
        y, x = np.ogrid[:self.patch_size, :self.patch_size]
        center_y, center_x = self.patch_size // 2, self.patch_size // 2
        
        # 计算到中心的距离
        dist_sq = (y - center_y) ** 2 + (x - center_x) ** 2
        max_dist_sq = (self.patch_size // 2) ** 2
        
        # 高斯权重
        weights = np.exp(-dist_sq / (2 * (max_dist_sq / 4)))
        
        # 确保边界权重不为零
        weights = np.maximum(weights, 0.1)
        
        return weights


class HoneycombPatchDataset:
    """
    蜂巢patch数据集
    
    专门用于处理蜂巢图像的patch-based训练数据
    """
    
    def __init__(
        self,
        image_patches: List[np.ndarray],
        mask_patches: List[np.ndarray],
        transform=None
    ):
        """
        Args:
            image_patches: 图像patch列表
            mask_patches: mask patch列表
            transform: 数据增强变换
        """
        assert len(image_patches) == len(mask_patches), "图像和mask数量不匹配"
        
        self.image_patches = image_patches
        self.mask_patches = mask_patches
        self.transform = transform
    
    def __len__(self):
        return len(self.image_patches)
    
    def __getitem__(self, idx):
        image = self.image_patches[idx]
        mask = self.mask_patches[idx]
        
        if self.transform:
            # 应用数据增强
            transformed = self.transform(image=image, mask=mask)
            image = transformed['image']
            mask = transformed['mask']
        
        # 转换为tensor
        if len(image.shape) == 3:
            image = torch.from_numpy(image).permute(2, 0, 1).float() / 255.0
        else:
            image = torch.from_numpy(image).unsqueeze(0).float() / 255.0
        
        mask = torch.from_numpy(mask).long()
        
        return image, mask


def create_sliding_window_dataset(
    image_dir: str,
    mask_dir: str,
    patch_size: int = 512,
    overlap_ratio: float = 0.25,
    min_valid_pixels: float = 0.1
) -> HoneycombPatchDataset:
    """
    创建滑动窗口数据集
    
    Args:
        image_dir: 图像目录
        mask_dir: mask目录
        patch_size: patch大小
        overlap_ratio: 重叠比例
        min_valid_pixels: 最小有效像素比例
        
    Returns:
        HoneycombPatchDataset实例
    """
    processor = SlidingWindowProcessor(
        patch_size=patch_size,
        overlap_ratio=overlap_ratio,
        min_valid_pixels=min_valid_pixels
    )
    
    image_dir = Path(image_dir)
    mask_dir = Path(mask_dir)
    
    all_image_patches = []
    all_mask_patches = []
    
    # 处理每个图像文件
    for image_path in list(image_dir.glob('*.png')) + list(image_dir.glob('*.jpg')) + list(image_dir.glob('*.JPG')):
        # 构建对应的mask文件路径
        mask_filename = image_path.stem + '_mask.png'
        mask_path = mask_dir / mask_filename

        if not mask_path.exists():
            logger.warning(f"找不到对应的mask文件: {mask_path}")
            continue
        
        # 加载图像和mask
        image = np.array(Image.open(image_path))
        mask = np.array(Image.open(mask_path))
        
        # 提取patches
        result = processor.extract_patches(image, mask)
        
        all_image_patches.extend(result['patches'])
        all_mask_patches.extend(result['mask_patches'])
    
    logger.info(f"总共创建了 {len(all_image_patches)} 个训练patches")
    
    return HoneycombPatchDataset(all_image_patches, all_mask_patches)


if __name__ == "__main__":
    # 测试代码
    processor = SlidingWindowProcessor(patch_size=512, overlap_ratio=0.25)
    
    # 创建测试图像
    test_image = np.random.randint(0, 255, (1200, 1600, 3), dtype=np.uint8)
    test_mask = np.random.randint(0, 8, (1200, 1600), dtype=np.uint8)
    
    # 提取patches
    result = processor.extract_patches(test_image, test_mask)
    print(f"提取了 {len(result['patches'])} 个patches")
    
    # 重组图像
    reconstructed = processor.reconstruct_from_patches(
        result['patches'],
        result['positions'],
        result['padded_shape'],
        result['original_shape'],
        result['padding']
    )
    
    print(f"重组图像尺寸: {reconstructed.shape}")
    print(f"原始图像尺寸: {test_image.shape}")
