#!/usr/bin/env python3
"""
数据集划分脚本
将outMask目录下的图像和掩码数据划分为训练集、验证集和测试集
支持多种划分策略和数据组织方式
"""

import os
import shutil
import random
import argparse
from pathlib import Path
from typing import List, Tuple, Dict
import logging
from collections import defaultdict

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatasetSplitter:
    """数据集划分器"""
    
    def __init__(
        self,
        source_images_dir: str,
        source_masks_dir: str,
        output_dir: str,
        train_ratio: float = 0.7,
        val_ratio: float = 0.2,
        test_ratio: float = 0.1,
        random_seed: int = 42
    ):
        """
        Args:
            source_images_dir: 源图像目录
            source_masks_dir: 源掩码目录
            output_dir: 输出目录
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            test_ratio: 测试集比例
            random_seed: 随机种子
        """
        self.source_images_dir = Path(source_images_dir)
        self.source_masks_dir = Path(source_masks_dir)
        self.output_dir = Path(output_dir)
        
        # 验证比例
        total_ratio = train_ratio + val_ratio + test_ratio
        if abs(total_ratio - 1.0) > 1e-6:
            raise ValueError(f"比例总和必须为1.0，当前为{total_ratio}")
        
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
        
        # 设置随机种子
        random.seed(random_seed)
        
        # 验证源目录
        self._validate_source_dirs()
        
        # 获取所有有效的图像-掩码对
        self.valid_pairs = self._get_valid_pairs()
        
        logger.info(f"找到 {len(self.valid_pairs)} 个有效的图像-掩码对")
    
    def _validate_source_dirs(self):
        """验证源目录"""
        if not self.source_images_dir.exists():
            raise FileNotFoundError(f"图像目录不存在: {self.source_images_dir}")
        
        if not self.source_masks_dir.exists():
            raise FileNotFoundError(f"掩码目录不存在: {self.source_masks_dir}")
    
    def _get_valid_pairs(self) -> List[Tuple[str, str, str]]:
        """获取所有有效的图像-掩码对"""
        valid_pairs = []
        
        # 支持的图像格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        
        # 遍历图像目录
        for image_path in self.source_images_dir.iterdir():
            if image_path.suffix.lower() not in image_extensions:
                continue
            
            # 查找对应的掩码文件
            image_stem = image_path.stem
            mask_path = None
            
            # 尝试不同的掩码命名模式
            mask_patterns = [
                f"{image_stem}_mask.png",
                f"{image_stem}_mask.jpg",
                f"{image_stem}.png",
                f"{image_stem}.jpg"
            ]
            
            for pattern in mask_patterns:
                potential_mask = self.source_masks_dir / pattern
                if potential_mask.exists():
                    mask_path = potential_mask
                    break
            
            if mask_path:
                valid_pairs.append((image_stem, str(image_path), str(mask_path)))
            else:
                logger.warning(f"未找到图像 {image_path.name} 对应的掩码")
        
        return valid_pairs
    
    def _create_output_structure(self):
        """创建输出目录结构"""
        splits = ['train', 'val', 'test']
        data_types = ['images', 'masks']
        
        for split in splits:
            for data_type in data_types:
                output_path = self.output_dir / split / data_type
                output_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"创建输出目录结构: {self.output_dir}")
    
    def _split_data(self) -> Dict[str, List[Tuple[str, str, str]]]:
        """划分数据"""
        # 随机打乱数据
        shuffled_pairs = self.valid_pairs.copy()
        random.shuffle(shuffled_pairs)
        
        total_samples = len(shuffled_pairs)
        train_count = int(total_samples * self.train_ratio)
        val_count = int(total_samples * self.val_ratio)
        
        # 划分数据
        splits = {
            'train': shuffled_pairs[:train_count],
            'val': shuffled_pairs[train_count:train_count + val_count],
            'test': shuffled_pairs[train_count + val_count:]
        }
        
        # 打印划分统计
        for split_name, split_data in splits.items():
            logger.info(f"{split_name}: {len(split_data)} 样本")
        
        return splits
    
    def _copy_files(self, splits: Dict[str, List[Tuple[str, str, str]]]):
        """复制文件到目标目录"""
        for split_name, split_data in splits.items():
            logger.info(f"复制 {split_name} 数据...")
            
            for image_stem, image_path, mask_path in split_data:
                # 复制图像
                image_src = Path(image_path)
                image_dst = self.output_dir / split_name / 'images' / image_src.name
                shutil.copy2(image_src, image_dst)
                
                # 复制掩码
                mask_src = Path(mask_path)
                mask_dst = self.output_dir / split_name / 'masks' / mask_src.name
                shutil.copy2(mask_src, mask_dst)
            
            logger.info(f"{split_name} 数据复制完成")
    
    def _generate_file_lists(self, splits: Dict[str, List[Tuple[str, str, str]]]):
        """生成文件列表"""
        for split_name, split_data in splits.items():
            list_file = self.output_dir / f"{split_name}_list.txt"
            
            with open(list_file, 'w', encoding='utf-8') as f:
                for image_stem, image_path, mask_path in split_data:
                    image_name = Path(image_path).name
                    mask_name = Path(mask_path).name
                    f.write(f"{image_name}\t{mask_name}\n")
            
            logger.info(f"生成文件列表: {list_file}")
    
    def _generate_statistics(self, splits: Dict[str, List[Tuple[str, str, str]]]):
        """生成统计信息"""
        stats = {
            'total_samples': len(self.valid_pairs),
            'train_samples': len(splits['train']),
            'val_samples': len(splits['val']),
            'test_samples': len(splits['test']),
            'train_ratio': len(splits['train']) / len(self.valid_pairs),
            'val_ratio': len(splits['val']) / len(self.valid_pairs),
            'test_ratio': len(splits['test']) / len(self.valid_pairs)
        }
        
        stats_file = self.output_dir / 'split_statistics.txt'
        with open(stats_file, 'w', encoding='utf-8') as f:
            f.write("数据集划分统计\n")
            f.write("=" * 30 + "\n")
            f.write(f"总样本数: {stats['total_samples']}\n")
            f.write(f"训练集: {stats['train_samples']} ({stats['train_ratio']:.2%})\n")
            f.write(f"验证集: {stats['val_samples']} ({stats['val_ratio']:.2%})\n")
            f.write(f"测试集: {stats['test_samples']} ({stats['test_ratio']:.2%})\n")
            f.write("\n")
            f.write("目录结构:\n")
            f.write(f"├── train/\n")
            f.write(f"│   ├── images/ ({stats['train_samples']} files)\n")
            f.write(f"│   └── masks/ ({stats['train_samples']} files)\n")
            f.write(f"├── val/\n")
            f.write(f"│   ├── images/ ({stats['val_samples']} files)\n")
            f.write(f"│   └── masks/ ({stats['val_samples']} files)\n")
            f.write(f"└── test/\n")
            f.write(f"    ├── images/ ({stats['test_samples']} files)\n")
            f.write(f"    └── masks/ ({stats['test_samples']} files)\n")
        
        logger.info(f"生成统计信息: {stats_file}")
        return stats
    
    def split(self) -> Dict:
        """执行数据集划分"""
        logger.info("开始数据集划分...")
        
        # 创建输出目录结构
        self._create_output_structure()
        
        # 划分数据
        splits = self._split_data()
        
        # 复制文件
        self._copy_files(splits)
        
        # 生成文件列表
        self._generate_file_lists(splits)
        
        # 生成统计信息
        stats = self._generate_statistics(splits)
        
        logger.info("数据集划分完成！")
        return stats

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='蜂巢图像分割数据集划分工具')
    
    parser.add_argument(
        '--source_images',
        type=str,
        default='/home/<USER>/PycharmProjects/20250629/outMask/patches/images',
        help='源图像目录路径'
    )
    
    parser.add_argument(
        '--source_masks',
        type=str,
        default='/home/<USER>/PycharmProjects/20250629/outMask/patches/masks',
        help='源掩码目录路径'
    )
    
    parser.add_argument(
        '--output_dir',
        type=str,
        default='/home/<USER>/PycharmProjects/20250629/training/data',
        help='输出目录路径'
    )
    
    parser.add_argument(
        '--train_ratio',
        type=float,
        default=0.7,
        help='训练集比例 (默认: 0.7)'
    )
    
    parser.add_argument(
        '--val_ratio',
        type=float,
        default=0.2,
        help='验证集比例 (默认: 0.2)'
    )
    
    parser.add_argument(
        '--test_ratio',
        type=float,
        default=0.1,
        help='测试集比例 (默认: 0.1)'
    )
    
    parser.add_argument(
        '--random_seed',
        type=int,
        default=42,
        help='随机种子 (默认: 42)'
    )
    
    args = parser.parse_args()
    
    # 创建数据集划分器
    splitter = DatasetSplitter(
        source_images_dir=args.source_images,
        source_masks_dir=args.source_masks,
        output_dir=args.output_dir,
        train_ratio=args.train_ratio,
        val_ratio=args.val_ratio,
        test_ratio=args.test_ratio,
        random_seed=args.random_seed
    )
    
    # 执行划分
    stats = splitter.split()
    
    # 打印结果
    print("\n" + "=" * 50)
    print("数据集划分完成！")
    print("=" * 50)
    print(f"总样本数: {stats['total_samples']}")
    print(f"训练集: {stats['train_samples']} ({stats['train_ratio']:.2%})")
    print(f"验证集: {stats['val_samples']} ({stats['val_ratio']:.2%})")
    print(f"测试集: {stats['test_samples']} ({stats['test_ratio']:.2%})")
    print(f"\n输出目录: {args.output_dir}")
    print("\n可以使用以下命令查看结果:")
    print(f"ls -la {args.output_dir}")

if __name__ == "__main__":
    main()