#!/usr/bin/env python3
"""
日志合并工具
将多个进程的日志文件合并到主日志文件
"""

import os
import time
import threading
from pathlib import Path
from datetime import datetime
import re

class LogMerger:
    """日志合并器"""
    
    def __init__(self, output_dir: str, main_log_file: str = "training.log"):
        self.output_dir = Path(output_dir)
        self.main_log_file = self.output_dir / main_log_file
        self.running = False
        self.merge_thread = None
        
    def start_monitoring(self):
        """开始监控和合并日志"""
        if self.running:
            return
            
        self.running = True
        self.merge_thread = threading.Thread(target=self._monitor_logs, daemon=True)
        self.merge_thread.start()
        
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.merge_thread:
            self.merge_thread.join()
    
    def _monitor_logs(self):
        """监控日志文件变化"""
        processed_files = {}
        
        while self.running:
            try:
                # 查找所有进程日志文件
                pid_log_files = list(self.output_dir.glob("*_pid*.log"))
                
                for log_file in pid_log_files:
                    if log_file.name not in processed_files:
                        processed_files[log_file.name] = 0
                    
                    # 检查文件是否有新内容
                    current_size = log_file.stat().st_size
                    last_size = processed_files[log_file.name]
                    
                    if current_size > last_size:
                        # 读取新内容并追加到主日志文件
                        self._append_new_content(log_file, last_size, current_size)
                        processed_files[log_file.name] = current_size
                
                time.sleep(1)  # 每秒检查一次
                
            except Exception as e:
                print(f"日志合并错误: {e}")
                time.sleep(5)
    
    def _append_new_content(self, log_file: Path, start_pos: int, end_pos: int):
        """将新内容追加到主日志文件"""
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                f.seek(start_pos)
                new_content = f.read(end_pos - start_pos)
            
            # 添加进程标识
            pid_match = re.search(r'_pid(\d+)\.log', log_file.name)
            pid = pid_match.group(1) if pid_match else "unknown"
            
            # 为每行添加进程标识
            lines = new_content.strip().split('\n')
            tagged_lines = []
            for line in lines:
                if line.strip():
                    tagged_lines.append(f"[PID:{pid}] {line}")
            
            if tagged_lines:
                with open(self.main_log_file, 'a', encoding='utf-8') as f:
                    f.write('\n'.join(tagged_lines) + '\n')
                    f.flush()
                    
        except Exception as e:
            print(f"追加日志内容失败: {e}")
    
    def merge_existing_logs(self):
        """合并现有的所有日志文件"""
        print("🔄 合并现有日志文件...")
        
        # 清空主日志文件
        with open(self.main_log_file, 'w', encoding='utf-8') as f:
            f.write(f"# 训练日志合并 - {datetime.now()}\n")
            f.write("# " + "="*60 + "\n\n")
        
        # 查找所有进程日志文件
        pid_log_files = list(self.output_dir.glob("*_pid*.log"))
        
        if not pid_log_files:
            print("❌ 未找到进程日志文件")
            return
        
        # 按修改时间排序
        pid_log_files.sort(key=lambda x: x.stat().st_mtime)
        
        for log_file in pid_log_files:
            print(f"  合并: {log_file.name}")
            
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if content.strip():
                    # 添加进程标识
                    pid_match = re.search(r'_pid(\d+)\.log', log_file.name)
                    pid = pid_match.group(1) if pid_match else "unknown"
                    
                    with open(self.main_log_file, 'a', encoding='utf-8') as f:
                        f.write(f"\n# --- 进程 {pid} 日志开始 ---\n")
                        
                        lines = content.strip().split('\n')
                        for line in lines:
                            if line.strip():
                                f.write(f"[PID:{pid}] {line}\n")
                        
                        f.write(f"# --- 进程 {pid} 日志结束 ---\n\n")
                        f.flush()
                        
            except Exception as e:
                print(f"  ❌ 合并 {log_file.name} 失败: {e}")
        
        print(f"✅ 日志合并完成: {self.main_log_file}")

def main():
    """主函数 - 用于手动合并日志"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python log_merger.py <output_dir> [--monitor]")
        sys.exit(1)
    
    output_dir = sys.argv[1]
    monitor = len(sys.argv) > 2 and sys.argv[2] == '--monitor'
    
    merger = LogMerger(output_dir)
    
    # 先合并现有日志
    merger.merge_existing_logs()
    
    if monitor:
        print("🔄 开始监控模式...")
        print("按 Ctrl+C 停止")
        try:
            merger.start_monitoring()
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n停止监控")
            merger.stop_monitoring()

if __name__ == "__main__":
    main()
