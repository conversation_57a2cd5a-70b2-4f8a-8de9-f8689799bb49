#!/usr/bin/env python3
"""
MAE预训练权重优化加载模块

功能：
1. 实现保持预训练特征完整性的权重加载策略
2. 处理多层特征提取的权重映射
3. 验证预训练特征的有效传递
4. 测试完整模型的前向传播

Author: AI Assistant
Date: 2024
"""

import torch
import torch.nn as nn
from pathlib import Path
from typing import Dict, Optional, Tuple, List
import logging
from collections import OrderedDict

# 导入我们的定制化模块
try:
    from ..core.models.feature_extractor import MultiLayerViTFeatureExtractor
    from ..core.models.upernet_head import ViTUPerNetHead
except ImportError:
    from core.models.feature_extractor import MultiLayerViTFeatureExtractor
    from core.models.upernet_head import ViTUPerNetHead

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MAEWeightLoader:
    """
    MAE预训练权重加载器
    
    负责将MAE预训练权重正确加载到定制化的ViT特征提取器和UPerNet分割头中
    """
    
    def __init__(self, checkpoint_path: str):
        """
        Args:
            checkpoint_path: MAE预训练权重文件路径
        """
        self.checkpoint_path = Path(checkpoint_path)
        if not self.checkpoint_path.exists():
            raise FileNotFoundError(f"MAE权重文件不存在: {checkpoint_path}")
            
        self.checkpoint = None
        self.encoder_state_dict = None
        self._load_checkpoint()
        
    def _load_checkpoint(self):
        """
        加载并解析MAE checkpoint文件
        """
        logger.info(f"加载MAE checkpoint: {self.checkpoint_path}")
        
        try:
            self.checkpoint = torch.load(self.checkpoint_path, map_location='cpu', weights_only=False)
            logger.info(f"✓ 成功加载checkpoint文件")
            
            # 解析checkpoint结构
            self._parse_checkpoint_structure()
            
        except Exception as e:
            logger.error(f"✗ 加载checkpoint失败: {e}")
            raise
            
    def _parse_checkpoint_structure(self):
        """
        解析checkpoint结构，提取编码器权重
        """
        logger.info("解析checkpoint结构...")
        
        # 检查checkpoint格式
        if 'model' in self.checkpoint:
            state_dict = self.checkpoint['model']
            logger.info("检测到标准MAE checkpoint格式 (包含'model'键)")
        else:
            state_dict = self.checkpoint
            logger.info("检测到直接state_dict格式")
            
        # 分析权重键名
        encoder_keys = []
        decoder_keys = []
        other_keys = []
        
        for key in state_dict.keys():
            if key.startswith('encoder.'):
                encoder_keys.append(key)
            elif key.startswith('decoder.'):
                decoder_keys.append(key)
            else:
                other_keys.append(key)
                
        logger.info(f"权重分析结果:")
        logger.info(f"  编码器权重: {len(encoder_keys)} 个")
        logger.info(f"  解码器权重: {len(decoder_keys)} 个")
        logger.info(f"  其他权重: {len(other_keys)} 个")
        
        # 提取编码器权重
        self.encoder_state_dict = {}
        for key, value in state_dict.items():
            if key.startswith('encoder.'):
                # 移除'encoder.'前缀
                new_key = key[8:]
                self.encoder_state_dict[new_key] = value
                
        logger.info(f"✓ 提取编码器权重: {len(self.encoder_state_dict)} 个")
        
        # 显示关键权重信息
        self._show_key_weights_info()
        
    def _show_key_weights_info(self):
        """
        显示关键权重的信息
        """
        key_weights = [
            'patch_embed.proj.weight',
            'pos_embed', 
            'cls_token',
            'blocks.0.norm1.weight',
            'blocks.11.norm2.bias',
            'norm.weight'
        ]
        
        logger.info("关键权重信息:")
        for key in key_weights:
            if key in self.encoder_state_dict:
                shape = self.encoder_state_dict[key].shape
                logger.info(f"  {key}: {shape}")
            else:
                logger.warning(f"  {key}: 未找到")
                
    def load_to_feature_extractor(self, feature_extractor: MultiLayerViTFeatureExtractor) -> Tuple[List[str], List[str]]:
        """
        将MAE权重加载到ViT特征提取器
        
        Args:
            feature_extractor: 多层ViT特征提取器实例
            
        Returns:
            missing_keys: 缺失的权重键
            unexpected_keys: 意外的权重键
        """
        logger.info("加载MAE权重到ViT特征提取器...")
        
        # 获取特征提取器的编码器
        encoder = feature_extractor.encoder
        
        # 处理位置编码的尺寸匹配
        processed_state_dict = self._process_position_embeddings(
            self.encoder_state_dict.copy(), 
            encoder
        )
        
        # 加载权重
        missing_keys, unexpected_keys = encoder.load_state_dict(
            processed_state_dict, strict=False
        )
        
        # 报告加载结果
        if missing_keys:
            logger.warning(f"缺失的权重键 ({len(missing_keys)} 个):")
            for key in missing_keys[:5]:  # 只显示前5个
                logger.warning(f"  - {key}")
            if len(missing_keys) > 5:
                logger.warning(f"  ... 还有 {len(missing_keys) - 5} 个")
                
        if unexpected_keys:
            logger.warning(f"意外的权重键 ({len(unexpected_keys)} 个):")
            for key in unexpected_keys[:5]:  # 只显示前5个
                logger.warning(f"  - {key}")
            if len(unexpected_keys) > 5:
                logger.warning(f"  ... 还有 {len(unexpected_keys) - 5} 个")
                
        if not missing_keys and not unexpected_keys:
            logger.info("✓ 权重加载完美匹配!")
        else:
            logger.info("✓ 权重加载完成 (存在部分不匹配)")
            
        return missing_keys, unexpected_keys
        
    def _process_position_embeddings(self, state_dict: Dict[str, torch.Tensor], encoder) -> Dict[str, torch.Tensor]:
        """
        处理位置编码的尺寸匹配
        
        Args:
            state_dict: 原始权重字典
            encoder: ViT编码器实例
            
        Returns:
            处理后的权重字典
        """
        logger.info("处理位置编码尺寸匹配...")
        
        if 'pos_embed' in state_dict:
            pos_embed_checkpoint = state_dict['pos_embed']
            pos_embed_model = encoder.pos_embed
            
            logger.info(f"位置编码尺寸:")
            logger.info(f"  checkpoint: {pos_embed_checkpoint.shape}")
            logger.info(f"  model: {pos_embed_model.shape}")
            
            if pos_embed_checkpoint.shape != pos_embed_model.shape:
                logger.info("位置编码尺寸不匹配，进行插值调整...")
                
                # 提取类别token和位置编码
                cls_token_checkpoint = pos_embed_checkpoint[:, 0:1, :]
                pos_embed_checkpoint_no_cls = pos_embed_checkpoint[:, 1:, :]
                
                # 计算原始和目标的网格尺寸
                num_patches_checkpoint = pos_embed_checkpoint_no_cls.shape[1]
                num_patches_model = pos_embed_model.shape[1] - 1  # 减去cls token
                
                grid_size_checkpoint = int(num_patches_checkpoint ** 0.5)
                grid_size_model = int(num_patches_model ** 0.5)
                
                logger.info(f"网格尺寸: {grid_size_checkpoint} -> {grid_size_model}")
                
                if grid_size_checkpoint != grid_size_model:
                    # 重塑为2D网格
                    pos_embed_2d = pos_embed_checkpoint_no_cls.reshape(
                        1, grid_size_checkpoint, grid_size_checkpoint, -1
                    ).permute(0, 3, 1, 2)  # (1, embed_dim, H, W)
                    
                    # 双线性插值
                    pos_embed_2d_resized = torch.nn.functional.interpolate(
                        pos_embed_2d,
                        size=(grid_size_model, grid_size_model),
                        mode='bicubic',
                        align_corners=False
                    )
                    
                    # 重塑回序列格式
                    pos_embed_resized = pos_embed_2d_resized.permute(0, 2, 3, 1).reshape(
                        1, grid_size_model * grid_size_model, -1
                    )
                    
                    # 重新组合类别token和位置编码
                    state_dict['pos_embed'] = torch.cat([
                        cls_token_checkpoint, pos_embed_resized
                    ], dim=1)
                    
                    logger.info(f"✓ 位置编码插值完成: {state_dict['pos_embed'].shape}")
                else:
                    logger.info("✓ 位置编码尺寸匹配，无需调整")
            else:
                logger.info("✓ 位置编码尺寸完全匹配")
                
        return state_dict
        
    def verify_feature_transfer(self, feature_extractor: MultiLayerViTFeatureExtractor, 
                               test_input: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        验证预训练特征的有效传递
        
        Args:
            feature_extractor: 加载了权重的特征提取器
            test_input: 测试输入 (B, 3, H, W)
            
        Returns:
            提取的特征字典
        """
        logger.info("验证预训练特征的有效传递...")
        
        try:
            with torch.no_grad():
                features = feature_extractor(test_input)
                
            logger.info(f"✓ 特征提取成功!")
            logger.info(f"  输入尺寸: {test_input.shape}")
            
            for layer_name, layer_features in features.items():
                spatial_shape = layer_features['spatial'].shape
                global_shape = layer_features['global'].shape
                
                # 计算特征统计信息
                spatial_mean = layer_features['spatial'].mean().item()
                spatial_std = layer_features['spatial'].std().item()
                global_mean = layer_features['global'].mean().item()
                global_std = layer_features['global'].std().item()
                
                logger.info(f"  {layer_name}:")
                logger.info(f"    空间特征: {spatial_shape}, 均值={spatial_mean:.4f}, 标准差={spatial_std:.4f}")
                logger.info(f"    全局特征: {global_shape}, 均值={global_mean:.4f}, 标准差={global_std:.4f}")
                
            return features
            
        except Exception as e:
            logger.error(f"✗ 特征提取失败: {e}")
            raise
            
    def create_complete_model(self, img_size: int = 512, num_classes: int = 8) -> nn.Module:
        """
        创建完整的MAE+UPerNet分割模型
        
        Args:
            img_size: 输入图像尺寸
            num_classes: 分割类别数
            
        Returns:
            完整的分割模型
        """
        logger.info("创建完整的MAE+UPerNet分割模型...")
        
        # 创建特征提取器
        feature_extractor = MultiLayerViTFeatureExtractor(
            model_name='vit_base_patch16_224',
            img_size=img_size,
            pretrained_path=None,  # 我们手动加载权重
            external_weight_loading=True  # 标明权重由外部加载器处理
        )
        
        # 加载MAE权重
        missing_keys, unexpected_keys = self.load_to_feature_extractor(feature_extractor)
        
        # 创建UPerNet分割头
        upernet_head = ViTUPerNetHead(
            feature_channels=768,
            num_classes=num_classes,
            fpn_dim=256
        )
        
        # 创建完整模型
        complete_model = MAEUPerNetComplete(
            feature_extractor=feature_extractor,
            segmentation_head=upernet_head
        )
        
        logger.info(f"✓ 完整模型创建成功")
        logger.info(f"  特征提取器权重加载: 缺失{len(missing_keys)}个, 意外{len(unexpected_keys)}个")
        
        return complete_model
        

class MAEUPerNetComplete(nn.Module):
    """
    完整的MAE+UPerNet分割模型
    
    结合MAE预训练的ViT特征提取器和定制化的UPerNet分割头
    """
    
    def __init__(self, feature_extractor: MultiLayerViTFeatureExtractor, 
                 segmentation_head: ViTUPerNetHead):
        super().__init__()
        self.feature_extractor = feature_extractor
        self.segmentation_head = segmentation_head
        
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入图像 (B, 3, H, W)
            
        Returns:
            分割结果字典
        """
        # 提取多层ViT特征
        vit_features = self.feature_extractor(x)
        
        # 转换为UPerNet期望的格式
        spatial_features = {
            layer_name: layer_data['spatial'] 
            for layer_name, layer_data in vit_features.items()
        }
        
        # 获取目标输出尺寸（与输入图像相同）
        target_size = (x.shape[2], x.shape[3])
        
        # 通过UPerNet分割头
        segmentation_output = self.segmentation_head(spatial_features, target_size)
        
        return segmentation_output
        
    def cleanup(self):
        """
        清理资源
        """
        self.feature_extractor.cleanup()
        

def test_mae_weight_loading():
    """
    测试MAE权重加载功能
    """
    print("=== MAE预训练权重优化加载测试 ===")
    
    # MAE权重文件路径
    mae_checkpoint = "/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/checkpoint-0315.pth"
    
    try:
        # 创建权重加载器
        print("\n1. 创建MAE权重加载器...")
        weight_loader = MAEWeightLoader(mae_checkpoint)
        
        # 创建完整模型
        print("\n2. 创建完整的MAE+UPerNet模型...")
        complete_model = weight_loader.create_complete_model(
            img_size=512, 
            num_classes=8
        )
        
        # 测试前向传播
        print("\n3. 测试完整模型前向传播...")
        batch_size = 2
        test_input = torch.randn(batch_size, 3, 512, 512)
        
        with torch.no_grad():
            output = complete_model(test_input)
            
        print(f"✓ 完整模型前向传播成功!")
        print(f"  输入尺寸: {test_input.shape}")
        print(f"  主要输出尺寸: {output['main'].shape}")
        print(f"  辅助输出尺寸: {output['aux'].shape}")
        
        # 计算模型参数
        total_params = sum(p.numel() for p in complete_model.parameters())
        trainable_params = sum(p.numel() for p in complete_model.parameters() if p.requires_grad)
        
        print(f"\n4. 模型参数统计:")
        print(f"  总参数数: {total_params:,}")
        print(f"  可训练参数数: {trainable_params:,}")
        
        # 验证特征传递
        print("\n5. 验证预训练特征传递...")
        features = weight_loader.verify_feature_transfer(
            complete_model.feature_extractor, 
            test_input
        )
        
        print("\n✓ 所有测试通过!")
        
        # 清理资源
        complete_model.cleanup()
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    print("\n=== 测试完成 ===")


if __name__ == '__main__':
    test_mae_weight_loading()