#!/usr/bin/env python3
"""
学习率调度器模块
实现余弦退火和预热学习率调度
"""

import math
import torch
from torch.optim.lr_scheduler import _LRScheduler
from typing import List, Optional, Union

class CosineAnnealingWarmupRestarts(_LRScheduler):
    """
    带预热的余弦退火学习率调度器
    
    Args:
        optimizer: 优化器
        first_cycle_steps: 第一个周期的步数
        cycle_mult: 周期倍数（每次重启后周期长度的倍数）
        max_lr: 最大学习率
        min_lr: 最小学习率
        warmup_steps: 预热步数
        gamma: 每次重启后最大学习率的衰减因子
    """
    
    def __init__(self,
                 optimizer,
                 first_cycle_steps: int,
                 cycle_mult: float = 1.0,
                 max_lr: float = 0.1,
                 min_lr: float = 0.001,
                 warmup_steps: int = 0,
                 gamma: float = 1.0,
                 last_epoch: int = -1):
        
        assert warmup_steps < first_cycle_steps
        
        self.first_cycle_steps = first_cycle_steps  # 第一个周期步数
        self.cycle_mult = cycle_mult  # 周期倍数
        self.base_max_lr = max_lr  # 基础最大学习率
        self.max_lr = max_lr  # 当前最大学习率
        self.min_lr = min_lr  # 最小学习率
        self.warmup_steps = warmup_steps  # 预热步数
        self.gamma = gamma  # 衰减因子
        
        self.cur_cycle_steps = first_cycle_steps  # 当前周期步数
        self.cycle = 0  # 当前周期
        self.step_in_cycle = last_epoch  # 当前周期内的步数
        
        super(CosineAnnealingWarmupRestarts, self).__init__(optimizer, last_epoch)
        
        # 初始化学习率
        self.init_lr()
    
    def init_lr(self):
        """初始化学习率"""
        self.base_lrs = []
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = self.min_lr
            self.base_lrs.append(self.min_lr)
    
    def get_lr(self):
        """计算当前学习率"""
        if self.step_in_cycle == -1:
            return self.base_lrs
        elif self.step_in_cycle < self.warmup_steps:
            # 预热阶段：线性增长
            return [(self.max_lr - base_lr) * self.step_in_cycle / self.warmup_steps + base_lr
                    for base_lr in self.base_lrs]
        else:
            # 余弦退火阶段
            return [base_lr + (self.max_lr - base_lr) * 
                    (1 + math.cos(math.pi * (self.step_in_cycle - self.warmup_steps) / 
                                  (self.cur_cycle_steps - self.warmup_steps))) / 2
                    for base_lr in self.base_lrs]
    
    def step(self, epoch=None):
        """更新学习率"""
        if epoch is None:
            epoch = self.last_epoch + 1
            self.step_in_cycle = self.step_in_cycle + 1
            if self.step_in_cycle >= self.cur_cycle_steps:
                self.cycle += 1
                self.step_in_cycle = self.step_in_cycle - self.cur_cycle_steps
                self.cur_cycle_steps = int((self.cur_cycle_steps - self.warmup_steps) * self.cycle_mult) + self.warmup_steps
        else:
            if epoch >= self.first_cycle_steps:
                if self.cycle_mult == 1.0:
                    self.step_in_cycle = epoch % self.first_cycle_steps
                    self.cycle = epoch // self.first_cycle_steps
                else:
                    n = int(math.log((epoch / self.first_cycle_steps * (self.cycle_mult - 1) + 1), self.cycle_mult))
                    self.cycle = n
                    self.step_in_cycle = epoch - int(self.first_cycle_steps * (self.cycle_mult ** n - 1) / (self.cycle_mult - 1))
                    self.cur_cycle_steps = self.first_cycle_steps * self.cycle_mult ** (n)
            else:
                self.cur_cycle_steps = self.first_cycle_steps
                self.step_in_cycle = epoch
                
        self.max_lr = self.base_max_lr * (self.gamma ** self.cycle)
        self.last_epoch = math.floor(epoch)
        
        for param_group, lr in zip(self.optimizer.param_groups, self.get_lr()):
            param_group['lr'] = lr

class LinearWarmupCosineAnnealingLR(_LRScheduler):
    """
    线性预热 + 余弦退火学习率调度器
    
    Args:
        optimizer: 优化器
        warmup_epochs: 预热轮数
        max_epochs: 总轮数
        warmup_start_lr: 预热起始学习率
        eta_min: 最小学习率
    """
    
    def __init__(self,
                 optimizer,
                 warmup_epochs: int,
                 max_epochs: int,
                 warmup_start_lr: float = 0.0,
                 eta_min: float = 0.0,
                 last_epoch: int = -1):
        
        self.warmup_epochs = warmup_epochs
        self.max_epochs = max_epochs
        self.warmup_start_lr = warmup_start_lr
        self.eta_min = eta_min
        
        super(LinearWarmupCosineAnnealingLR, self).__init__(optimizer, last_epoch)
    
    def get_lr(self):
        """计算当前学习率"""
        if not self._get_lr_called_within_step:
            import warnings
            warnings.warn("To get the last learning rate computed by the scheduler, "
                         "please use `get_last_lr()`.")
        
        if self.last_epoch == 0:
            return [self.warmup_start_lr] * len(self.base_lrs)
        elif self.last_epoch < self.warmup_epochs:
            # 预热阶段：线性增长
            return [self.warmup_start_lr + (base_lr - self.warmup_start_lr) * self.last_epoch / self.warmup_epochs
                    for base_lr in self.base_lrs]
        elif self.last_epoch == self.warmup_epochs:
            return self.base_lrs
        else:
            # 余弦退火阶段
            return [self.eta_min + (base_lr - self.eta_min) *
                    (1 + math.cos(math.pi * (self.last_epoch - self.warmup_epochs) / 
                                  (self.max_epochs - self.warmup_epochs))) / 2
                    for base_lr in self.base_lrs]

def create_scheduler(optimizer, scheduler_type: str, **kwargs):
    """
    创建学习率调度器
    
    Args:
        optimizer: 优化器
        scheduler_type: 调度器类型
        **kwargs: 调度器参数
    
    Returns:
        学习率调度器
    """
    
    if scheduler_type.lower() == 'cosine_warmup':
        return LinearWarmupCosineAnnealingLR(
            optimizer,
            warmup_epochs=kwargs.get('warmup_epochs', 5),
            max_epochs=kwargs.get('max_epochs', 100),
            warmup_start_lr=kwargs.get('warmup_start_lr', 0.0),
            eta_min=kwargs.get('eta_min', 0.0)
        )
    
    elif scheduler_type.lower() == 'cosine_warmup_restarts':
        return CosineAnnealingWarmupRestarts(
            optimizer,
            first_cycle_steps=kwargs.get('first_cycle_steps', 50),
            cycle_mult=kwargs.get('cycle_mult', 1.0),
            max_lr=kwargs.get('max_lr', 0.1),
            min_lr=kwargs.get('min_lr', 0.001),
            warmup_steps=kwargs.get('warmup_steps', 5),
            gamma=kwargs.get('gamma', 1.0)
        )
    
    elif scheduler_type.lower() == 'cosine':
        return torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=kwargs.get('T_max', 100),
            eta_min=kwargs.get('eta_min', 0.0)
        )
    
    elif scheduler_type.lower() == 'step':
        return torch.optim.lr_scheduler.StepLR(
            optimizer,
            step_size=kwargs.get('step_size', 30),
            gamma=kwargs.get('gamma', 0.1)
        )
    
    elif scheduler_type.lower() == 'plateau':
        return torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode=kwargs.get('mode', 'max'),
            patience=kwargs.get('patience', 10),
            factor=kwargs.get('factor', 0.1)
        )
    
    else:
        raise ValueError(f"不支持的学习率调度器类型: {scheduler_type}")

def adjust_learning_rate(optimizer, epoch: int, warmup_epochs: int, total_epochs: int, 
                        base_lr: float, min_lr: float = 0.0):
    """
    手动调整学习率（预热 + 余弦退火）
    
    Args:
        optimizer: 优化器
        epoch: 当前轮数
        warmup_epochs: 预热轮数
        total_epochs: 总轮数
        base_lr: 基础学习率
        min_lr: 最小学习率
    """
    
    if epoch < warmup_epochs:
        # 预热阶段：线性增长
        lr = base_lr * epoch / warmup_epochs
    else:
        # 余弦退火阶段
        lr = min_lr + (base_lr - min_lr) * 0.5 * (
            1.0 + math.cos(math.pi * (epoch - warmup_epochs) / (total_epochs - warmup_epochs))
        )
    
    for param_group in optimizer.param_groups:
        param_group['lr'] = lr
    
    return lr

if __name__ == "__main__":
    # 测试学习率调度器
    import torch.nn as nn
    import matplotlib.pyplot as plt
    
    # 创建简单模型和优化器
    model = nn.Linear(10, 1)
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
    
    # 测试不同的调度器
    schedulers = {
        'cosine_warmup': create_scheduler(
            optimizer, 'cosine_warmup',
            warmup_epochs=10, max_epochs=100, eta_min=1e-6
        ),
        'cosine_warmup_restarts': create_scheduler(
            optimizer, 'cosine_warmup_restarts',
            first_cycle_steps=50, warmup_steps=5, max_lr=1e-3, min_lr=1e-6
        )
    }
    
    # 绘制学习率曲线
    epochs = 100
    fig, axes = plt.subplots(1, 2, figsize=(15, 5))
    
    for idx, (name, scheduler) in enumerate(schedulers.items()):
        lrs = []
        for epoch in range(epochs):
            lrs.append(optimizer.param_groups[0]['lr'])
            scheduler.step()
        
        axes[idx].plot(lrs)
        axes[idx].set_title(f'{name} Learning Rate Schedule')
        axes[idx].set_xlabel('Epoch')
        axes[idx].set_ylabel('Learning Rate')
        axes[idx].grid(True)
    
    plt.tight_layout()
    plt.savefig('lr_schedule_comparison.png', dpi=300, bbox_inches='tight')
    print("学习率调度器测试完成，结果保存到 lr_schedule_comparison.png")


class WarmupCosineAnnealingLR(_LRScheduler):
    """
    带预热的余弦退火学习率调度器（简化版）

    Args:
        optimizer: 优化器
        warmup_epochs: 预热epoch数
        max_epochs: 总epoch数
        eta_min: 最小学习率
        warmup_start_lr: 预热起始学习率
        last_epoch: 上次epoch（用于恢复训练）
    """

    def __init__(
        self,
        optimizer,
        warmup_epochs: int = 10,
        max_epochs: int = 200,
        eta_min: float = 1e-7,
        warmup_start_lr: float = 1e-7,
        last_epoch: int = -1
    ):
        self.warmup_epochs = warmup_epochs
        self.max_epochs = max_epochs
        self.eta_min = eta_min
        self.warmup_start_lr = warmup_start_lr

        # 保存初始学习率
        if last_epoch == -1:
            for group in optimizer.param_groups:
                group.setdefault('initial_lr', group['lr'])

        super().__init__(optimizer, last_epoch)

    def get_lr(self):
        """计算当前epoch的学习率"""
        if self.last_epoch < self.warmup_epochs:
            # 预热阶段：线性增长
            return self._get_warmup_lr()
        else:
            # 余弦退火阶段
            return self._get_cosine_lr()

    def _get_warmup_lr(self):
        """计算预热阶段的学习率"""
        lrs = []
        for base_lr in self.base_lrs:
            # 线性插值从warmup_start_lr到base_lr
            lr = self.warmup_start_lr + (base_lr - self.warmup_start_lr) * (
                self.last_epoch / self.warmup_epochs
            )
            lrs.append(lr)
        return lrs

    def _get_cosine_lr(self):
        """计算余弦退火阶段的学习率"""
        lrs = []
        # 调整epoch计数，从预热结束开始计算
        cosine_epoch = self.last_epoch - self.warmup_epochs
        cosine_max_epochs = self.max_epochs - self.warmup_epochs

        for base_lr in self.base_lrs:
            # 余弦退火公式
            lr = self.eta_min + (base_lr - self.eta_min) * (
                1 + math.cos(math.pi * cosine_epoch / cosine_max_epochs)
            ) / 2
            lrs.append(lr)
        return lrs

    def get_lr_info(self) -> dict:
        """获取当前学习率信息"""
        current_lrs = self.get_lr()

        info = {
            'epoch': self.last_epoch + 1,
            'phase': 'warmup' if self.last_epoch < self.warmup_epochs else 'cosine',
            'learning_rates': current_lrs,
            'warmup_progress': min(1.0, (self.last_epoch + 1) / self.warmup_epochs),
            'total_progress': (self.last_epoch + 1) / self.max_epochs
        }

        return info


def create_scheduler(optimizer, config: dict, logger=None):
    """
    创建学习率调度器

    Args:
        optimizer: 优化器
        config: 配置字典
        logger: 日志器

    Returns:
        学习率调度器
    """
    scheduler_config = config.get('scheduler', {})
    scheduler_type = scheduler_config.get('type', 'WarmupCosineAnnealingLR')

    if logger:
        logger.info(f"创建学习率调度器: {scheduler_type}")

    if scheduler_type == 'WarmupCosineAnnealingLR':
        scheduler = WarmupCosineAnnealingLR(
            optimizer=optimizer,
            warmup_epochs=scheduler_config.get('warmup_epochs', 10),
            max_epochs=scheduler_config.get('max_epochs', 200),
            eta_min=scheduler_config.get('eta_min', 1e-7),
            warmup_start_lr=scheduler_config.get('warmup_start_lr', 1e-7)
        )
    elif scheduler_type == 'CosineAnnealingLR':
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer=optimizer,
            T_max=scheduler_config.get('max_epochs', 200),
            eta_min=scheduler_config.get('eta_min', 1e-7)
        )
    else:
        if logger:
            logger.warning(f"未知的调度器类型: {scheduler_type}，使用默认WarmupCosineAnnealingLR")
        scheduler = WarmupCosineAnnealingLR(
            optimizer=optimizer,
            warmup_epochs=scheduler_config.get('warmup_epochs', 10),
            max_epochs=scheduler_config.get('max_epochs', 200),
            eta_min=scheduler_config.get('eta_min', 1e-7),
            warmup_start_lr=scheduler_config.get('warmup_start_lr', 1e-7)
        )

    if logger:
        logger.info(f"✓ 学习率调度器创建完成")
        if hasattr(scheduler, 'get_lr_info'):
            logger.info(f"  预热epochs: {scheduler_config.get('warmup_epochs', 10)}")
            logger.info(f"  总epochs: {scheduler_config.get('max_epochs', 200)}")
            logger.info(f"  最小学习率: {scheduler_config.get('eta_min', 1e-7)}")

    return scheduler