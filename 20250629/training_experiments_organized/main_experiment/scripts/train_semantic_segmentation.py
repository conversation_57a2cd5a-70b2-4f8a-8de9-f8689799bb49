#!/usr/bin/env python3
"""
蜂巢语义分割训练脚本
基于MAE ViT-Base预训练模型

Author: AI Assistant
Date: 2024
"""

import os
import sys
import argparse
import yaml
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, random_split
from pathlib import Path
import logging
from datetime import datetime
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from core.models.feature_extractor import MultiLayerViTFeatureExtractor
from core.models.upernet_head import ViTUPerNetHead
from core.losses.combined_loss import CombinedLoss
from utils.weight_loader import MAEWeightLoader
from utils.sliding_window_processor import create_sliding_window_dataset
from utils.metrics import SegmentationMetrics
from utils.lr_scheduler import create_scheduler
import albumentations as A
from albumentations.pytorch import ToTensorV2


def setup_logging(log_file: str, log_level: str = "INFO"):
    """设置日志 - 修复多进程冲突问题"""
    log_level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR
    }

    # 创建logger
    logger = logging.getLogger(__name__)

    # 避免重复添加handler
    if logger.handlers:
        logger.handlers.clear()

    logger.setLevel(log_level_map.get(log_level, logging.INFO))

    # 创建formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 文件handler - 使用追加模式，添加进程ID避免冲突
    import os
    pid = os.getpid()
    log_file_with_pid = str(log_file).replace('.log', f'_pid{pid}.log')

    file_handler = logging.FileHandler(log_file_with_pid, mode='a', encoding='utf-8')
    file_handler.setLevel(log_level_map.get(log_level, logging.INFO))
    file_handler.setFormatter(formatter)

    # 控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level_map.get(log_level, logging.INFO))
    console_handler.setFormatter(formatter)

    # 添加handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # 防止日志传播到root logger
    logger.propagate = False

    return logger


def load_config(config_path: str) -> dict:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def create_data_transforms(config: dict):
    """创建数据增强变换"""
    aug_config = config['data']['augmentation']
    norm_config = config['data']['normalize']
    
    if aug_config['enable']:
        train_transform = A.Compose([
            A.RandomRotate90(p=aug_config['random_rotate90']),
            A.HorizontalFlip(p=aug_config['horizontal_flip']),
            A.VerticalFlip(p=aug_config['vertical_flip']),
            A.ColorJitter(
                brightness=aug_config['color_jitter']['brightness'],
                contrast=aug_config['color_jitter']['contrast'],
                saturation=aug_config['color_jitter']['saturation'],
                hue=aug_config['color_jitter']['hue'],
                p=0.5
            ),
            A.ElasticTransform(p=aug_config['elastic_transform']),
            A.GaussNoise(p=aug_config['gaussian_noise']),
            A.Normalize(mean=norm_config['mean'], std=norm_config['std']),
            ToTensorV2()
        ])
    else:
        train_transform = A.Compose([
            A.Normalize(mean=norm_config['mean'], std=norm_config['std']),
            ToTensorV2()
        ])
    
    val_transform = A.Compose([
        A.Normalize(mean=norm_config['mean'], std=norm_config['std']),
        ToTensorV2()
    ])
    
    return train_transform, val_transform


def create_model(config: dict, logger):
    """创建完整的分割模型"""
    logger.info("创建模型...")
    
    # 创建特征提取器
    feature_extractor = MultiLayerViTFeatureExtractor()
    logger.info("✓ 特征提取器创建完成")
    
    # 加载MAE预训练权重
    mae_checkpoint = config['model']['feature_extractor']['mae_checkpoint']
    if mae_checkpoint and os.path.exists(mae_checkpoint):
        weight_loader = MAEWeightLoader(mae_checkpoint)
        missing_keys, unexpected_keys = weight_loader.load_to_feature_extractor(feature_extractor)

        # 计算成功加载的权重数量
        total_encoder_params = len(weight_loader.encoder_state_dict)
        loaded_params = total_encoder_params - len(missing_keys)

        logger.info(f"✓ MAE权重加载完成: {loaded_params} 个权重已加载")
        if missing_keys:
            logger.warning(f"缺失权重: {len(missing_keys)} 个")
        if unexpected_keys:
            logger.warning(f"意外权重: {len(unexpected_keys)} 个")
    else:
        logger.warning(f"MAE权重文件不存在或未配置: {mae_checkpoint}")
    
    # 创建分割头
    segmentation_head = ViTUPerNetHead(
        feature_channels=config['model']['feature_extractor']['embed_dim'],  # 768
        fpn_dim=config['model']['segmentation_head']['fpn_dim'],  # 256
        num_classes=config['model']['segmentation_head']['num_classes'],  # 8
        pool_scales=config['model']['segmentation_head']['pool_scales']  # [1, 2, 3, 6]
    )
    logger.info("✓ 分割头创建完成")
    
    # 组合完整模型
    class SegmentationModel(nn.Module):
        def __init__(self, feature_extractor, segmentation_head):
            super().__init__()
            self.feature_extractor = feature_extractor
            self.segmentation_head = segmentation_head

        def forward(self, x):
            # 特征提取
            features = self.feature_extractor(x)

            # 准备UPerNet输入格式
            upernet_features = {k: v['spatial'] for k, v in features.items()}

            # 检查特征尺寸，避免BatchNorm问题
            for layer_name, feat in upernet_features.items():
                if feat.shape[2] <= 1 or feat.shape[3] <= 1:
                    logger.warning(f"检测到小尺寸特征图 {layer_name}: {feat.shape}，可能导致BatchNorm问题")

            # 分割预测
            target_size = (x.shape[2], x.shape[3])
            outputs = self.segmentation_head(upernet_features, target_size=target_size)

            # 返回主要输出用于损失计算，同时保存辅助输出用于深度监督
            # 在训练模式下返回字典以支持辅助损失，在推理模式下只返回主要输出
            if self.training:
                return outputs  # {'main': main_logits, 'aux': aux_logits}
            else:
                return outputs['main']  # 只返回主要输出
    
    model = SegmentationModel(feature_extractor, segmentation_head)
    logger.info("✓ 完整模型创建完成")
    
    return model


def create_loss_function(config: dict):
    """创建损失函数"""
    loss_config = config['loss']
    
    # 类别权重
    class_weights = None
    if 'class_weights' in config['classes']:
        class_weights = torch.FloatTensor(config['classes']['class_weights'])
    
    loss_fn = CombinedLoss(
        dice_weight=loss_config['dice_weight'],
        ce_weight=loss_config['ce_weight'],
        focal_weight=loss_config['focal_weight'],
        focal_alpha=loss_config.get('focal_alpha', 1.0),
        focal_gamma=loss_config.get('focal_gamma', 2.0),
        class_weights=class_weights,
        ignore_index=config['classes'].get('ignore_index', -100)
    )
    
    return loss_fn


def create_optimizer(model: nn.Module, config: dict):
    """创建优化器"""
    opt_config = config['optimizer']
    
    # 分层学习率
    feature_extractor_params = []
    segmentation_head_params = []
    
    for name, param in model.named_parameters():
        if 'feature_extractor' in name:
            feature_extractor_params.append(param)
        else:
            segmentation_head_params.append(param)
    
    param_groups = [
        {'params': feature_extractor_params, 'lr': opt_config['feature_extractor_lr']},
        {'params': segmentation_head_params, 'lr': opt_config['segmentation_head_lr']}
    ]
    
    optimizer = torch.optim.AdamW(
        param_groups,
        weight_decay=opt_config['weight_decay'],
        betas=opt_config['betas'],
        eps=opt_config['eps']
    )
    
    return optimizer


def create_datasets(config: dict, train_transform, val_transform, logger):
    """创建训练和验证数据集"""
    logger.info("创建数据集...")

    # 检查是否使用预分块数据
    train_dir = config['data'].get('train_dir')
    val_dir = config['data'].get('val_dir')

    if train_dir and val_dir and os.path.exists(train_dir) and os.path.exists(val_dir):
        # 使用预分块数据
        logger.info("使用预分块数据...")

        from core.datasets.honeycomb_dataset import HoneycombDataset

        # 创建训练数据集
        train_images_dir = os.path.join(train_dir, 'images')
        train_masks_dir = os.path.join(train_dir, 'masks')

        train_dataset = HoneycombDataset(
            images_dir=train_images_dir,
            masks_dir=train_masks_dir,
            image_size=config['data']['image_size'][0],
            normalize_mean=config['data']['normalize']['mean'],
            normalize_std=config['data']['normalize']['std'],
            use_augmentation=config['data']['augmentation']['enable'],
            augmentation_config=config['data']['augmentation']
        )

        # 创建验证数据集
        val_images_dir = os.path.join(val_dir, 'images')
        val_masks_dir = os.path.join(val_dir, 'masks')

        val_dataset = HoneycombDataset(
            images_dir=val_images_dir,
            masks_dir=val_masks_dir,
            image_size=config['data']['image_size'][0],
            normalize_mean=config['data']['normalize']['mean'],
            normalize_std=config['data']['normalize']['std'],
            use_augmentation=False,  # 验证集不使用数据增强
            augmentation_config=None
        )

        logger.info(f"✓ 预分块数据集加载完成: 训练集 {len(train_dataset)}, 验证集 {len(val_dataset)}")

    else:
        # 使用动态滑动窗口（原有逻辑）
        logger.info("使用动态滑动窗口...")

        full_dataset = create_sliding_window_dataset(
            image_dir=config['data']['image_dir'],
            mask_dir=config['data']['mask_dir'],
            patch_size=config['data']['patch_size'],
            overlap_ratio=config['data']['overlap_ratio'],
            min_valid_pixels=config['data']['min_valid_pixels']
        )

        logger.info(f"✓ 完整数据集创建完成: {len(full_dataset)} 个样本")

        # 分割训练和验证集
        train_size = int(config['data']['train_ratio'] * len(full_dataset))
        val_size = len(full_dataset) - train_size

        generator = torch.Generator().manual_seed(config['data']['random_seed'])
        train_dataset, val_dataset = random_split(full_dataset, [train_size, val_size], generator=generator)

        logger.info(f"✓ 数据集分割完成: 训练集 {len(train_dataset)}, 验证集 {len(val_dataset)}")

    return train_dataset, val_dataset


def main():
    parser = argparse.ArgumentParser(description='蜂巢语义分割训练')
    parser.add_argument('--config', type=str, default='configs/semantic_segmentation_config.yaml',
                        help='配置文件路径')
    parser.add_argument('--output_dir', type=str, default=None,
                        help='输出目录（覆盖配置文件）')
    parser.add_argument('--resume', type=str, default=None,
                        help='恢复训练的检查点路径')
    parser.add_argument('--batch_size', type=int, default=None,
                        help='批次大小（覆盖配置文件）')
    parser.add_argument('--learning_rate', type=float, default=None,
                        help='学习率（覆盖配置文件）')
    parser.add_argument('--epochs', type=int, default=None,
                        help='训练轮数（覆盖配置文件）')
    parser.add_argument('--debug', action='store_true',
                        help='启用调试模式')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 调试模式
    if args.debug:
        config['debug']['enable'] = True

    # 输出目录
    if args.output_dir:
        config['output']['output_dir'] = args.output_dir

    # 批次大小
    if args.batch_size:
        config['data']['batch_size'] = args.batch_size
        print(f"🔧 批次大小设置为: {args.batch_size}")

    # 学习率
    if args.learning_rate:
        config['optimizer']['feature_extractor_lr'] = args.learning_rate * 0.1  # 编码器用较小学习率
        config['optimizer']['segmentation_head_lr'] = args.learning_rate
        print(f"🔧 学习率设置为: {args.learning_rate}")

    # 训练轮数
    if args.epochs:
        config['training']['epochs'] = args.epochs
        config['scheduler']['max_epochs'] = args.epochs
        print(f"🔧 训练轮数设置为: {args.epochs}")
    
    # 创建输出目录
    output_dir = Path(config['output']['output_dir'])
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    log_file = output_dir / config['output']['log_file']
    logger = setup_logging(str(log_file), config['output']['log_level'])
    
    logger.info("=" * 60)
    logger.info("蜂巢语义分割训练开始")
    logger.info("=" * 60)
    logger.info(f"配置文件: {args.config}")
    logger.info(f"输出目录: {output_dir}")
    logger.info(f"调试模式: {config['debug']['enable']}")
    
    # 设备配置
    device = torch.device(config['hardware']['device'] if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    try:
        # 创建数据变换
        train_transform, val_transform = create_data_transforms(config)
        logger.info("✓ 数据变换创建完成")
        
        # 创建数据集
        train_dataset, val_dataset = create_datasets(config, train_transform, val_transform, logger)
        
        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=config['data']['batch_size'],
            shuffle=True,
            num_workers=config['data']['num_workers'],
            pin_memory=config['data']['pin_memory']
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=config['data']['batch_size'],
            shuffle=False,
            num_workers=config['data']['num_workers'],
            pin_memory=config['data']['pin_memory']
        )
        
        logger.info("✓ 数据加载器创建完成")
        
        # 创建模型
        model = create_model(config, logger)
        model = model.to(device)
        
        # 创建损失函数
        loss_fn = create_loss_function(config)
        loss_fn = loss_fn.to(device)
        logger.info("✓ 损失函数创建完成")
        
        # 创建优化器
        optimizer = create_optimizer(model, config)
        logger.info("✓ 优化器创建完成")

        # 创建学习率调度器
        scheduler = create_scheduler(optimizer, config, logger)

        # 创建评估指标
        metrics = SegmentationMetrics(
            num_classes=config['classes']['num_classes'],
            ignore_index=config['classes'].get('ignore_index', -100),
            class_names=config['classes']['class_names']
        )
        logger.info("✓ 评估指标创建完成")
        
        logger.info("🚀 开始训练...")

        # 创建输出目录
        output_dir = Path(config['output']['output_dir'])
        output_dir.mkdir(parents=True, exist_ok=True)

        # 训练配置
        num_epochs = config['training']['epochs']
        val_interval = config['training']['val_interval']
        save_interval = config['training']['save_interval']

        # 混合精度训练
        scaler = torch.amp.GradScaler('cuda') if config['training']['mixed_precision'] else None

        # 开始训练
        logger.info("✅ 所有组件创建成功，开始训练！")

        best_miou = 0.0

        for epoch in range(num_epochs):
            logger.info(f"Epoch {epoch+1}/{num_epochs}")

            # 训练阶段
            model.train()
            train_loss = 0.0
            train_steps = 0

            progress_bar = tqdm(train_loader, desc=f"Training Epoch {epoch+1}")

            for batch_idx, batch_data in enumerate(progress_bar):
                # 处理不同数据加载器的返回格式
                if len(batch_data) == 2:
                    images, masks = batch_data
                elif len(batch_data) == 3:
                    images, masks, metadata = batch_data
                else:
                    raise ValueError(f"Unexpected batch data format: {len(batch_data)} items")
                images = images.to(device)
                masks = masks.to(device)

                optimizer.zero_grad()

                if scaler:
                    with torch.amp.autocast('cuda'):
                        outputs = model(images)

                        # 验证输入维度
                        if isinstance(outputs, dict):
                            main_pred = outputs['main']
                            aux_pred = outputs['aux']

                            # 验证预测张量的形状
                            if main_pred.shape[1] != 8:
                                raise ValueError(f"Main prediction has {main_pred.shape[1]} classes, expected 8")
                            if aux_pred.shape[1] != 8:
                                raise ValueError(f"Aux prediction has {aux_pred.shape[1]} classes, expected 8")

                            # 验证标签值范围
                            if masks.min() < 0 or masks.max() >= 8:
                                raise ValueError(f"Target labels out of range [0, 7]: [{masks.min()}, {masks.max()}]")

                            main_loss_dict = loss_fn(main_pred, masks)
                            aux_loss_dict = loss_fn(aux_pred, masks)

                            # 提取标量损失值
                            main_loss = main_loss_dict['total_loss']
                            aux_loss = aux_loss_dict['total_loss']
                            loss = main_loss + 0.4 * aux_loss  # 辅助损失权重0.4
                        else:
                            # 验证预测张量的形状
                            if outputs.shape[1] != 8:
                                raise ValueError(f"Prediction has {outputs.shape[1]} classes, expected 8")

                            # 验证标签值范围
                            if masks.min() < 0 or masks.max() >= 8:
                                raise ValueError(f"Target labels out of range [0, 7]: [{masks.min()}, {masks.max()}]")

                            loss_dict = loss_fn(outputs, masks)
                            loss = loss_dict['total_loss']

                    scaler.scale(loss).backward()
                    scaler.step(optimizer)
                    scaler.update()
                else:
                    outputs = model(images)

                    # 验证输入维度
                    if isinstance(outputs, dict):
                        main_pred = outputs['main']
                        aux_pred = outputs['aux']

                        # 验证预测张量的形状
                        if main_pred.shape[1] != 8:
                            raise ValueError(f"Main prediction has {main_pred.shape[1]} classes, expected 8")
                        if aux_pred.shape[1] != 8:
                            raise ValueError(f"Aux prediction has {aux_pred.shape[1]} classes, expected 8")

                        # 验证标签值范围
                        if masks.min() < 0 or masks.max() >= 8:
                            raise ValueError(f"Target labels out of range [0, 7]: [{masks.min()}, {masks.max()}]")

                        main_loss_dict = loss_fn(main_pred, masks)
                        aux_loss_dict = loss_fn(aux_pred, masks)

                        # 提取标量损失值
                        main_loss = main_loss_dict['total_loss']
                        aux_loss = aux_loss_dict['total_loss']
                        loss = main_loss + 0.4 * aux_loss  # 辅助损失权重0.4
                    else:
                        # 验证预测张量的形状
                        if outputs.shape[1] != 8:
                            raise ValueError(f"Prediction has {outputs.shape[1]} classes, expected 8")

                        # 验证标签值范围
                        if masks.min() < 0 or masks.max() >= 8:
                            raise ValueError(f"Target labels out of range [0, 7]: [{masks.min()}, {masks.max()}]")

                        loss_dict = loss_fn(outputs, masks)
                        loss = loss_dict['total_loss']
                    loss.backward()
                    optimizer.step()

                train_loss += loss.item()
                train_steps += 1

                # 更新进度条
                progress_bar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Avg Loss': f'{train_loss/train_steps:.4f}'
                })

                # 每100步打印一次
                if (batch_idx + 1) % 100 == 0:
                    logger.info(f"Epoch {epoch+1}, Step {batch_idx+1}, Loss: {loss.item():.4f}")

            avg_train_loss = train_loss / train_steps
            logger.info(f"Epoch {epoch+1} - Average Training Loss: {avg_train_loss:.4f}")

            # 更新学习率调度器
            scheduler.step()

            # 记录学习率信息
            if hasattr(scheduler, 'get_lr_info'):
                lr_info = scheduler.get_lr_info()
                logger.info(f"学习率调度 - 阶段: {lr_info['phase']}, LR: {[f'{lr:.2e}' for lr in lr_info['learning_rates']]}")
            else:
                current_lrs = scheduler.get_last_lr()
                logger.info(f"当前学习率: {[f'{lr:.2e}' for lr in current_lrs]}")

            # 验证阶段
            if (epoch + 1) % val_interval == 0:
                logger.info("开始验证...")
                model.eval()
                val_loss = 0.0
                val_steps = 0

                metrics.reset()

                with torch.no_grad():
                    for batch_data in tqdm(val_loader, desc="Validation"):
                        # 处理不同数据加载器的返回格式
                        if len(batch_data) == 2:
                            images, masks = batch_data
                        elif len(batch_data) == 3:
                            images, masks, _ = batch_data
                        else:
                            raise ValueError(f"Unexpected batch data format: {len(batch_data)} items")
                        images = images.to(device)
                        masks = masks.to(device)

                        outputs = model(images)

                        # 验证输入维度和处理模型输出
                        if isinstance(outputs, dict):
                            main_pred = outputs['main']
                            aux_pred = outputs['aux']

                            # 验证预测张量的形状
                            if main_pred.shape[1] != 8:
                                raise ValueError(f"Main prediction has {main_pred.shape[1]} classes, expected 8")
                            if aux_pred.shape[1] != 8:
                                raise ValueError(f"Aux prediction has {aux_pred.shape[1]} classes, expected 8")

                            # 验证标签值范围
                            if masks.min() < 0 or masks.max() >= 8:
                                raise ValueError(f"Target labels out of range [0, 7]: [{masks.min()}, {masks.max()}]")

                            main_loss_dict = loss_fn(main_pred, masks)
                            aux_loss_dict = loss_fn(aux_pred, masks)

                            # 提取标量损失值
                            main_loss = main_loss_dict['total_loss']
                            aux_loss = aux_loss_dict['total_loss']
                            loss = main_loss + 0.4 * aux_loss  # 辅助损失权重0.4
                            # 使用主要输出计算指标
                            main_outputs = main_pred
                        else:
                            # 验证预测张量的形状
                            if outputs.shape[1] != 8:
                                raise ValueError(f"Prediction has {outputs.shape[1]} classes, expected 8")

                            # 验证标签值范围
                            if masks.min() < 0 or masks.max() >= 8:
                                raise ValueError(f"Target labels out of range [0, 7]: [{masks.min()}, {masks.max()}]")

                            loss_dict = loss_fn(outputs, masks)
                            loss = loss_dict['total_loss']
                            main_outputs = outputs

                        val_loss += loss.item()
                        val_steps += 1

                        # 计算指标（使用主要输出）
                        preds = torch.argmax(main_outputs, dim=1)
                        metrics.update(preds, masks)

                avg_val_loss = val_loss / val_steps
                val_metrics = metrics.compute()

                logger.info(f"Validation - Loss: {avg_val_loss:.4f}")
                logger.info(f"Validation - mIoU: {val_metrics['mean_iou']:.4f}")
                logger.info(f"Validation - Accuracy: {val_metrics['pixel_accuracy']:.4f}")
                logger.info(f"Validation - Dice: {val_metrics['dice_score']:.4f}")
                logger.info(f"Validation - Precision: {val_metrics['precision']:.4f}")
                logger.info(f"Validation - Recall: {val_metrics['recall']:.4f}")

                # 保存详细指标
                try:
                    metrics_dir = output_dir / "metrics"
                    json_path, csv_path = metrics.save_detailed_metrics(
                        metrics_dir / f"epoch_{epoch+1}_metrics",
                        epoch=epoch+1
                    )

                    # 保存混淆矩阵
                    cm_paths = metrics.save_confusion_matrix(
                        metrics_dir / f"epoch_{epoch+1}_confusion_matrix",
                        epoch=epoch+1
                    )

                    logger.info(f"详细指标已保存: {json_path.name}, {csv_path.name}")
                except Exception as e:
                    logger.warning(f"保存详细指标失败: {e}")

                # 保存最佳模型
                if val_metrics['mean_iou'] > best_miou:
                    best_miou = val_metrics['mean_iou']
                    best_model_path = output_dir / 'best_model.pth'
                    torch.save({
                        'epoch': epoch + 1,
                        'model_state_dict': model.state_dict(),
                        'optimizer_state_dict': optimizer.state_dict(),
                        'best_miou': best_miou,
                        'config': config
                    }, best_model_path)
                    logger.info(f"保存最佳模型: {best_model_path}")

            # 定期保存检查点
            if (epoch + 1) % save_interval == 0:
                checkpoint_path = output_dir / f'checkpoint_epoch_{epoch+1}.pth'
                torch.save({
                    'epoch': epoch + 1,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'train_loss': avg_train_loss,
                    'config': config
                }, checkpoint_path)
                logger.info(f"保存检查点: {checkpoint_path}")

        logger.info("🎉 训练完成！")
        logger.info(f"最佳 mIoU: {best_miou:.4f}")
        
    except Exception as e:
        logger.error(f"训练过程中出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
