#!/usr/bin/env python3
"""
推理执行脚本
支持单张图像、批量图像和目录推理
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config import load_config
from evaluation.inference import InferenceEngine


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='蜂巢分割模型推理')
    
    # 必需参数
    parser.add_argument(
        '--config', 
        type=str, 
        required=True,
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--model', 
        type=str, 
        required=True,
        help='模型权重文件路径'
    )
    
    # 输入参数（互斥）
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        '--image', 
        type=str,
        help='单张图像路径'
    )
    
    input_group.add_argument(
        '--input-dir', 
        type=str,
        help='输入图像目录'
    )
    
    # 输出参数
    parser.add_argument(
        '--output-dir', 
        type=str, 
        default='./inference_results',
        help='输出目录'
    )
    
    # 推理参数
    parser.add_argument(
        '--batch-size', 
        type=int, 
        default=8,
        help='批次大小（仅用于目录推理）'
    )
    
    parser.add_argument(
        '--save-visualization', 
        action='store_true',
        help='保存可视化结果'
    )
    
    parser.add_argument(
        '--save-probabilities', 
        action='store_true',
        help='保存概率图'
    )
    
    parser.add_argument(
        '--device', 
        type=str, 
        default='auto',
        help='推理设备'
    )
    
    return parser.parse_args()


def validate_args(args):
    """验证参数"""
    # 检查配置文件
    if not os.path.exists(args.config):
        print(f"错误: 配置文件不存在: {args.config}")
        sys.exit(1)
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"错误: 模型文件不存在: {args.model}")
        sys.exit(1)
    
    # 检查输入
    if args.image and not os.path.exists(args.image):
        print(f"错误: 图像文件不存在: {args.image}")
        sys.exit(1)
    
    if args.input_dir and not os.path.exists(args.input_dir):
        print(f"错误: 输入目录不存在: {args.input_dir}")
        sys.exit(1)
    
    print("参数验证通过")


def run_single_image_inference(engine, args):
    """运行单张图像推理"""
    print(f"对单张图像进行推理: {args.image}")
    
    # 推理
    result = engine.predict_single(
        args.image, 
        return_probs=args.save_probabilities
    )
    
    # 保存结果
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    image_name = Path(args.image).stem
    
    # 保存预测掩码
    import cv2
    pred_mask = result['prediction']
    mask_file = output_dir / f"{image_name}_pred.png"
    cv2.imwrite(str(mask_file), pred_mask.astype(np.uint8) * 32)
    
    # 保存概率图
    if args.save_probabilities and 'probabilities' in result:
        import numpy as np
        probs = result['probabilities']
        for class_idx in range(probs.shape[0]):
            prob_file = output_dir / f"{image_name}_prob_class_{class_idx}.png"
            prob_img = (probs[class_idx] * 255).astype(np.uint8)
            cv2.imwrite(str(prob_file), prob_img)
    
    # 保存可视化结果
    if args.save_visualization:
        engine._save_visualization(
            args.image,
            pred_mask,
            output_dir / f"{image_name}_vis.png"
        )
    
    # 打印结果
    print(f"推理完成!")
    print(f"置信度: {result['confidence']:.4f}")
    print(f"推理时间: {result['inference_time']:.4f}s")
    print(f"结果保存到: {output_dir}")


def run_directory_inference(engine, args):
    """运行目录推理"""
    print(f"对目录进行批量推理: {args.input_dir}")
    
    # 推理
    summary = engine.predict_directory(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        save_visualization=args.save_visualization,
        batch_size=args.batch_size
    )
    
    # 打印摘要
    print(f"\n推理摘要:")
    print(f"总图像数: {summary['total_images']}")
    print(f"总时间: {summary['total_time']:.2f}s")
    print(f"平均推理时间: {summary['avg_inference_time']:.4f}s")
    print(f"FPS: {summary['fps']:.2f}")
    print(f"结果保存到: {summary['output_directory']}")


def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 设置日志
    setup_logging()
    
    # 验证参数
    validate_args(args)
    
    try:
        # 加载配置
        print(f"加载配置文件: {args.config}")
        config = load_config(args.config)
        
        # 覆盖设备设置
        if args.device != 'auto':
            config.system.device = args.device
        
        # 创建推理引擎
        print(f"创建推理引擎...")
        engine = InferenceEngine(config, args.model)
        
        # 运行推理
        if args.image:
            run_single_image_inference(engine, args)
        elif args.input_dir:
            run_directory_inference(engine, args)
        
        print("推理完成!")
        
    except Exception as e:
        print(f"推理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
