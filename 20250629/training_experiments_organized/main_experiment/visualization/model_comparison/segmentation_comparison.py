#!/usr/bin/env python3
"""
分割模型对比可视化脚本
用于生成所有已训练模型在同一张测试图像上的分割效果对比图
"""

import os
import sys
import json
import numpy as np
import cv2
import torch
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
from typing import Dict, List, Tuple, Optional
try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False
    print("警告: seaborn未安装，将使用默认颜色")

from PIL import Image, ImageDraw
import argparse

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 导入重构框架
from core.config import create_default_config
from evaluation import InferenceEngine
from utils import SegmentationMetrics

class SegmentationComparison:
    """分割模型对比可视化器"""
    
    def __init__(self, 
                 test_image_path: str,
                 annotation_path: str,
                 models_dir: str,
                 output_dir: str = "./comparison_results"):
        """
        初始化对比可视化器
        
        Args:
            test_image_path: 测试图像路径
            annotation_path: 标注文件路径（JSON格式）
            models_dir: 模型目录路径
            output_dir: 输出目录
        """
        self.test_image_path = test_image_path
        self.annotation_path = annotation_path
        self.models_dir = models_dir
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 蜂巢分割类别映射 - 简化为2类
        self.class_names = ['background', 'honeycomb']
        self.num_classes = len(self.class_names)
        
        # 颜色映射
        self.colors = self._create_color_map()
        
        # 加载测试图像和标注
        self.test_image = self._load_test_image()
        self.ground_truth = self._load_ground_truth()
        
        # 发现可用模型
        self.available_models = self._discover_models()
        
        print(f"发现 {len(self.available_models)} 个可用模型")
        print(f"测试图像尺寸: {self.test_image.shape}")
        
    def _create_color_map(self) -> np.ndarray:
        """创建类别颜色映射"""
        if HAS_SEABORN:
            # 使用seaborn调色板
            colors = sns.color_palette("Set2", self.num_classes)
            colors = np.array(colors) * 255
        else:
            # 使用默认颜色 - 简化为2类
            colors = [
                [0, 0, 0],       # background - black
                [255, 0, 0],     # honeycomb - red
            ]
            colors = np.array(colors)

        return colors.astype(np.uint8)

    def _clean_model_name(self, model_name: str) -> str:
        """
        清理模型名称，移除_Optimized后缀

        Args:
            model_name: 原始模型名称

        Returns:
            清理后的模型名称
        """
        # 移除_Optimized后缀（不区分大小写）
        import re
        cleaned_name = re.sub(r'_optimized$', '', model_name, flags=re.IGNORECASE)
        return cleaned_name
    
    def _load_test_image(self) -> np.ndarray:
        """加载测试图像"""
        image = cv2.imread(self.test_image_path)
        if image is None:
            raise ValueError(f"无法加载图像: {self.test_image_path}")
        # 转换为RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        return image
    
    def _load_ground_truth(self) -> np.ndarray:
        """从JSON标注文件加载真实标签"""
        with open(self.annotation_path, 'r') as f:
            annotations = json.load(f)
        
        # 创建掩码
        height, width = self.test_image.shape[:2]
        mask = np.zeros((height, width), dtype=np.uint8)
        
        # 创建PIL图像用于绘制多边形
        pil_mask = Image.new('L', (width, height), 0)
        draw = ImageDraw.Draw(pil_mask)
        
        for annotation in annotations:
            class_name = annotation['class']
            points = annotation['points']
            
            # 获取类别索引
            if class_name in self.class_names:
                class_idx = self.class_names.index(class_name)
            else:
                class_idx = 1  # 默认为honeycomb
            
            # 转换点格式
            polygon_points = [(point['x'], point['y']) for point in points]
            
            # 绘制多边形
            draw.polygon(polygon_points, fill=class_idx)
        
        # 转换回numpy数组
        mask = np.array(pil_mask)
        return mask
    
    def _discover_models(self) -> List[Dict[str, str]]:
        """发现可用的模型"""
        models = []
        models_path = Path(self.models_dir)
        configs_path = models_path.parent / "configs"

        # 获取所有配置文件
        configs = {}
        if configs_path.exists():
            for config_file in configs_path.glob("*.yaml"):
                configs[config_file.stem] = str(config_file)

        print(f"📁 发现配置文件: {list(configs.keys())}")

        for model_dir in models_path.iterdir():
            if model_dir.is_dir():
                model_file = model_dir / "best_model.pth"
                if model_file.exists():
                    # 查找对应的配置文件 - 改进匹配逻辑
                    config_path = None
                    model_name = model_dir.name  # 原始名称: FPN_resnet50_Optimized

                    # 转换为配置文件格式: fpn_resnet50_optimized
                    config_name_expected = model_name.lower().replace('-', '_')

                    # 尝试精确匹配
                    if config_name_expected in configs:
                        config_path = configs[config_name_expected]
                        print(f"✅ 精确匹配: {model_name} -> {config_name_expected}")
                    else:
                        # 尝试模糊匹配
                        # 提取架构和backbone
                        parts = model_name.split('_')
                        if len(parts) >= 3:
                            arch = parts[0].lower()  # fpn
                            backbone = '_'.join(parts[1:-1]).lower()  # resnet50 或 efficientnet-b4

                            # 构建可能的配置名称
                            possible_names = [
                                f"{arch}_{backbone}_optimized",
                                f"{arch}_{backbone.replace('-', '_')}_optimized",
                                f"{arch}_{backbone}_optimized".replace('-', '_')
                            ]

                            for possible_name in possible_names:
                                if possible_name in configs:
                                    config_path = configs[possible_name]
                                    print(f"✅ 模糊匹配: {model_name} -> {possible_name}")
                                    break

                    if config_path:
                        models.append({
                            'name': model_dir.name,
                            'path': str(model_file),
                            'config_path': config_path,
                            'architecture': model_dir.name.split('_')[0],
                            'backbone': '_'.join(model_dir.name.split('_')[1:-1])
                        })
                    else:
                        print(f"❌ 未找到与{model_dir.name}匹配的config文件")
                        print(f"   期望的配置名称: {config_name_expected}")
                        print(f"   可用的配置: {list(configs.keys())[:5]}...")

        return models
    
    def _predict_with_model(self, model_info: Dict[str, str]) -> Tuple[np.ndarray, Dict[str, float]]:
        """
        使用指定模型进行预测 - 完全按照原始predict.py的大图滑动窗口方式

        Args:
            model_info: 模型信息字典

        Returns:
            prediction: 预测掩码
            metrics: 评估指标
        """
        try:
            # 加载模型
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

            # 加载配置文件
            import yaml
            with open(model_info['config_path'], 'r') as f:
                config = yaml.safe_load(f)

            # 使用配置创建模型 - 完全按照predict.py的方式
            import segmentation_models_pytorch as smp
            model = smp.create_model(**config['model']).to(device)

            # 加载权重
            state_dict = torch.load(model_info['path'], map_location=device)
            model.load_state_dict(state_dict)
            model.eval()

            print(f"   ✅ 模型加载成功: {config['model']['arch']} + {config['model']['encoder_name']}")

            # 大图滑动窗口预测 - 完全按照predict.py的predict_large_image_sliding_window函数
            image = self.test_image
            H, W, _ = image.shape
            patch_size = 224
            overlap = 0.1
            threshold = 0.5

            stride = int(patch_size * (1 - overlap))
            prob_map = np.zeros((H, W), dtype=np.float32)
            count_map = np.zeros((H, W), dtype=np.float32)

            # ImageNet标准化参数 - 完全按照predict.py
            mean = np.array([0.485, 0.456, 0.406])
            std = np.array([0.229, 0.224, 0.225])

            print(f"   🔍 开始滑动窗口预测: {H}x{W} -> patch_size={patch_size}, stride={stride}")

            # 滑动窗口预测
            for y in range(0, H, stride):
                for x in range(0, W, stride):
                    y1 = y
                    x1 = x
                    y2 = min(y1 + patch_size, H)
                    x2 = min(x1 + patch_size, W)

                    # 提取patch
                    patch = image[y1:y2, x1:x2, :]

                    # 填充到标准尺寸
                    pad_bottom = patch_size - (y2 - y1)
                    pad_right = patch_size - (x2 - x1)
                    if pad_bottom > 0 or pad_right > 0:
                        patch = np.pad(patch, ((0, pad_bottom), (0, pad_right), (0, 0)), mode='constant')

                    # 预处理 - 完全按照predict.py
                    patch_norm = patch.astype(np.float32) / 255.0
                    patch_norm = (patch_norm - mean) / std
                    patch_norm = patch_norm.astype(np.float32)  # 强制float32

                    # 转换为tensor
                    patch_tensor = torch.from_numpy(patch_norm).permute(2, 0, 1).unsqueeze(0).to(device)

                    # 预测
                    with torch.no_grad():
                        pred = model(patch_tensor)
                        pred_prob = torch.sigmoid(pred).squeeze().cpu().numpy()

                    # 去除填充部分
                    pred_prob = pred_prob[:y2 - y1, :x2 - x1]

                    # 累积到概率图
                    prob_map[y1:y2, x1:x2] += pred_prob
                    count_map[y1:y2, x1:x2] += 1

            # 平均化概率图
            prob_map = prob_map / np.maximum(count_map, 1e-6)

            # 二值化
            prediction = (prob_map > threshold).astype(np.uint8)

            print(f"   ✅ 滑动窗口预测完成，前景像素比例: {np.sum(prediction)/prediction.size*100:.1f}%")

            # 计算详细指标
            metrics = self._calculate_detailed_metrics(prediction, self.ground_truth)

            return prediction, metrics

        except Exception as e:
            print(f"模型 {model_info['name']} 预测失败: {e}")
            import traceback
            traceback.print_exc()

            # 返回空预测
            height, width = self.test_image.shape[:2]
            prediction = np.zeros((height, width), dtype=np.uint8)
            metrics = {'iou': 0.0, 'dice': 0.0, 'accuracy': 0.0}
            return prediction, metrics



    def _preprocess_image(self, image: np.ndarray) -> torch.Tensor:
        """预处理图像（简单归一化）"""
        # 调整大小到标准尺寸
        target_size = (512, 512)
        image_resized = cv2.resize(image, target_size)

        # 归一化到[0,1]
        image_normalized = image_resized.astype(np.float32) / 255.0

        # 转换为CHW格式
        image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1)

        # 添加batch维度
        image_tensor = image_tensor.unsqueeze(0)

        return image_tensor

    def _preprocess_image_imagenet(self, image: np.ndarray, target_size=(224, 224)) -> torch.Tensor:
        """使用ImageNet标准化预处理图像"""
        # 调整大小
        image_resized = cv2.resize(image, target_size)

        # 归一化到[0,1]
        image_normalized = image_resized.astype(np.float32) / 255.0

        # ImageNet标准化
        mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        image_normalized = (image_normalized - mean) / std

        # 确保数据类型为float32
        image_normalized = image_normalized.astype(np.float32)

        # 转换为CHW格式
        image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1)

        # 添加batch维度并确保为float32
        image_tensor = image_tensor.unsqueeze(0).float()

        return image_tensor
    
    def _calculate_metrics(self, prediction: np.ndarray, ground_truth: np.ndarray) -> Dict[str, float]:
        """计算评估指标"""
        # 确保尺寸匹配
        if prediction.shape != ground_truth.shape:
            # 将ground_truth调整到与prediction相同的尺寸
            ground_truth = cv2.resize(ground_truth,
                                    (prediction.shape[1], prediction.shape[0]),
                                    interpolation=cv2.INTER_NEAREST)

        # 计算IoU（针对前景类别）
        pred_fg = prediction > 0
        gt_fg = ground_truth > 0
        intersection = np.logical_and(pred_fg, gt_fg).sum()
        union = np.logical_or(pred_fg, gt_fg).sum()
        iou = intersection / (union + 1e-8)

        # 计算Dice
        dice = 2 * intersection / (pred_fg.sum() + gt_fg.sum() + 1e-8)

        # 计算准确率
        accuracy = (prediction == ground_truth).mean()

        return {
            'iou': float(iou),
            'dice': float(dice),
            'accuracy': float(accuracy)
        }
    
    def _apply_color_map(self, mask: np.ndarray) -> np.ndarray:
        """应用颜色映射到掩码"""
        colored_mask = np.zeros((*mask.shape, 3), dtype=np.uint8)
        for class_idx in range(self.num_classes):
            colored_mask[mask == class_idx] = self.colors[class_idx]
        return colored_mask

    def _create_overlay_comparison(self, prediction: np.ndarray, ground_truth: np.ndarray) -> np.ndarray:
        """
        创建学术级别的叠加对比图

        Args:
            prediction: 模型预测掩码 (0=背景, 1=前景)
            ground_truth: 真实标注掩码 (0=背景, 1=前景)

        Returns:
            叠加对比图 (RGB)

        颜色编码:
        - 黑色 [0, 0, 0]: 真阴性 (TN) - 背景预测正确
        - 红色 [255, 0, 0]: 假阳性 (FP) - 预测为前景但实际为背景
        - 绿色 [0, 255, 0]: 假阴性 (FN) - 预测为背景但实际为前景
        - 黄色 [255, 255, 0]: 真阳性 (TP) - 前景预测正确
        """
        # 确保输入为二值掩码
        pred_binary = (prediction > 0).astype(np.uint8)
        gt_binary = (ground_truth > 0).astype(np.uint8)

        # 创建RGB输出
        overlay = np.zeros((*pred_binary.shape, 3), dtype=np.uint8)

        # 真阴性 (TN): 背景预测正确 - 黑色
        tn_mask = (pred_binary == 0) & (gt_binary == 0)
        overlay[tn_mask] = [0, 0, 0]

        # 假阳性 (FP): 预测为前景但实际为背景 - 红色
        fp_mask = (pred_binary == 1) & (gt_binary == 0)
        overlay[fp_mask] = [255, 0, 0]

        # 假阴性 (FN): 预测为背景但实际为前景 - 绿色
        fn_mask = (pred_binary == 0) & (gt_binary == 1)
        overlay[fn_mask] = [0, 255, 0]

        # 真阳性 (TP): 前景预测正确 - 黄色
        tp_mask = (pred_binary == 1) & (gt_binary == 1)
        overlay[tp_mask] = [255, 255, 0]

        return overlay

    def _calculate_detailed_metrics(self, prediction: np.ndarray, ground_truth: np.ndarray) -> Dict[str, float]:
        """计算详细的评估指标"""
        # 确保尺寸匹配
        if prediction.shape != ground_truth.shape:
            ground_truth = cv2.resize(ground_truth,
                                    (prediction.shape[1], prediction.shape[0]),
                                    interpolation=cv2.INTER_NEAREST)

        # 二值化
        pred_binary = (prediction > 0).astype(np.uint8)
        gt_binary = (ground_truth > 0).astype(np.uint8)

        # 计算混淆矩阵元素
        tp = np.sum((pred_binary == 1) & (gt_binary == 1))
        tn = np.sum((pred_binary == 0) & (gt_binary == 0))
        fp = np.sum((pred_binary == 1) & (gt_binary == 0))
        fn = np.sum((pred_binary == 0) & (gt_binary == 1))

        # 计算指标
        iou = tp / (tp + fp + fn + 1e-8)
        dice = 2 * tp / (2 * tp + fp + fn + 1e-8)
        precision = tp / (tp + fp + 1e-8)
        recall = tp / (tp + fn + 1e-8)
        accuracy = (tp + tn) / (tp + tn + fp + fn + 1e-8)
        f1_score = 2 * precision * recall / (precision + recall + 1e-8)

        return {
            'iou': float(iou),
            'dice': float(dice),
            'precision': float(precision),
            'recall': float(recall),
            'accuracy': float(accuracy),
            'f1_score': float(f1_score),
            'tp': int(tp),
            'tn': int(tn),
            'fp': int(fp),
            'fn': int(fn)
        }
    
    def generate_comparison(self) -> str:
        """
        生成对比可视化图
        
        Returns:
            输出文件路径
        """
        print("开始生成模型对比可视化...")
        
        # 预测所有模型
        predictions = {}
        metrics_data = {}
        
        for model_info in self.available_models:
            print(f"正在预测模型: {model_info['name']}")
            pred, metrics = self._predict_with_model(model_info)
            predictions[model_info['name']] = pred
            metrics_data[model_info['name']] = metrics
        
        # 创建对比图
        return self._create_comparison_plot(predictions, metrics_data)
    
    def _create_comparison_plot(self, predictions: Dict[str, np.ndarray],
                              metrics_data: Dict[str, Dict[str, float]]) -> str:
        """创建学术级别的对比可视化图 - 论文友好的横向布局设计"""
        # 设计新的布局：4行5列，第一行2张图居中，后三行每行5张图
        n_rows = 4
        n_cols = 5

        # 创建图形 - 论文友好的横向布局
        fig_width = n_cols * 7.0   # 横向增大宽度
        fig_height = n_rows * 5.5  # 减少高度以适合论文页面
        fig = plt.figure(figsize=(fig_width, fig_height))

        # 设置学术级别的字体 - 适应新布局的字体尺寸
        plt.rcParams.update({
            'font.size': 16,           # 适中的基础字体
            'font.family': 'serif',
            'font.serif': ['Times New Roman'],
            'axes.titlesize': 18,      # 适中的标题字体
            'axes.labelsize': 16,      # 适中的标签字体
            'legend.fontsize': 14      # 适中的图例字体
        })

        # 第一行：原始图像和真实标注（在5列中居中显示）

        # 原始图像 - 第一行第2列位置
        ax1 = plt.subplot2grid((n_rows, n_cols), (0, 1), colspan=1)
        ax1.imshow(self.test_image)
        ax1.set_title('(a) Original Image', fontsize=20, fontweight='bold', pad=20)
        ax1.axis('off')

        # 真实标注 - 第一行第4列位置
        ax2 = plt.subplot2grid((n_rows, n_cols), (0, 3), colspan=1)
        gt_overlay = np.zeros((*self.ground_truth.shape, 3), dtype=np.uint8)
        gt_overlay[self.ground_truth > 0] = [0, 255, 0]  # 绿色表示真实标注
        ax2.imshow(gt_overlay)
        ax2.set_title('(b) Ground Truth', fontsize=20, fontweight='bold', pad=20)
        ax2.axis('off')

        # 模型预测结果：第2-4行，每行5个
        model_items = list(predictions.items())
        for idx, (model_name, prediction) in enumerate(model_items):
            if idx >= 15:  # 最多显示15个模型
                break

            # 计算行列位置
            row = (idx // 5) + 1  # 从第2行开始（索引1）
            col = idx % 5

            # 创建子图
            ax = plt.subplot2grid((n_rows, n_cols), (row, col), colspan=1)

            # 创建叠加对比图
            overlay_img = self._create_overlay_comparison(prediction, self.ground_truth)
            ax.imshow(overlay_img)

            # 添加简洁的学术级别标题 - 适中字体
            clean_name = self._clean_model_name(model_name)
            title = f"({chr(ord('c') + idx)}) {clean_name}"

            ax.set_title(title, fontsize=16, fontweight='bold', pad=15)
            ax.axis('off')

        # 添加学术级别的颜色图例
        self._add_academic_legend(fig)

        # 调整布局 - 优化间距以适应横向布局
        plt.tight_layout()
        plt.subplots_adjust(
            top=0.92,      # 顶部留白
            bottom=0.12,   # 底部为图例留出空间
            left=0.02,     # 左侧留白
            right=0.98,    # 右侧留白
            hspace=0.25,   # 行间距 - 适应4行布局
            wspace=0.05    # 列间距 - 适应5列布局
        )

        # 保存图像 - 学术标准高分辨率
        output_path = self.output_dir / "segmentation_comparison.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()

        # 生成性能统计表格
        self._create_performance_table(metrics_data)

        print(f"论文友好的横向布局学术级别对比图已保存到: {output_path}")
        print(f"图像尺寸: {fig_width:.1f} x {fig_height:.1f} 英寸")
        print(f"布局: 4行5列 (第1行2张图居中，第2-4行每行5张图)")
        print(f"子图尺寸: 7.0 x 5.5 英寸每个")
        print(f"标题字体: 20pt (第一行), 16pt (模型)")
        print(f"图例字体: 13pt (内容), 15pt (标题)")
        return str(output_path)

    def _create_performance_table(self, metrics_data: Dict[str, Dict[str, float]]):
        """创建学术级别的性能统计表格"""
        try:
            import pandas as pd

            # 准备表格数据
            table_data = []
            for model_name, metrics in metrics_data.items():
                clean_name = self._clean_model_name(model_name)
                table_data.append({
                    'Model': clean_name,
                    'IoU': f"{metrics['iou']:.4f}",
                    'Dice': f"{metrics['dice']:.4f}",
                    'Precision': f"{metrics['precision']:.4f}",
                    'Recall': f"{metrics['recall']:.4f}",
                    'F1-Score': f"{metrics['f1_score']:.4f}",
                    'Accuracy': f"{metrics['accuracy']:.4f}"
                })

            # 创建DataFrame
            df = pd.DataFrame(table_data)

            # 保存为CSV
            csv_path = self.output_dir / "performance_metrics.csv"
            df.to_csv(csv_path, index=False)

            # 保存为LaTeX表格
            latex_path = self.output_dir / "performance_metrics.tex"
            latex_table = df.to_latex(index=False,
                                    caption="Quantitative comparison of segmentation models on honeycomb detection task.",
                                    label="tab:segmentation_results",
                                    column_format='l' + 'c' * (len(df.columns) - 1),
                                    escape=False)

            with open(latex_path, 'w') as f:
                f.write(latex_table)

            print(f"性能统计表格已保存:")
            print(f"  CSV格式: {csv_path}")
            print(f"  LaTeX格式: {latex_path}")

            # 打印表格预览
            print("\n性能统计表格预览:")
            print("=" * 80)
            print(df.to_string(index=False))
            print("=" * 80)

        except ImportError:
            print("警告: pandas未安装，跳过表格生成")
        except Exception as e:
            print(f"表格生成失败: {e}")
    
    def _add_color_legend(self, fig):
        """添加颜色图例"""
        # 创建图例
        legend_elements = []
        for idx, class_name in enumerate(self.class_names):
            color = self.colors[idx] / 255.0  # 归一化到0-1
            legend_elements.append(
                patches.Patch(color=color, label=class_name)
            )

        # 添加图例到图形
        fig.legend(handles=legend_elements,
                  loc='center',
                  bbox_to_anchor=(0.5, 0.02),
                  ncol=len(self.class_names),
                  fontsize=10)

    def _add_academic_legend(self, fig):
        """添加学术级别的详细图例 - 大尺寸版本"""
        # 创建详细的颜色图例 - 增大图标尺寸
        legend_elements = []
        colors = [[0, 0, 0], [1, 0, 0], [0, 1, 0], [1, 1, 0], [0, 1, 0]]
        labels = [
            'True Negative (TN): Correct background',
            'False Positive (FP): Predicted foreground, actual background',
            'False Negative (FN): Predicted background, actual foreground',
            'True Positive (TP): Correct foreground',
            'Ground Truth: Honeycomb regions'
        ]

        for color, label in zip(colors, labels):
            legend_elements.append(patches.Patch(color=color, label=label))

        # 添加图例到图形底部 - 适应横向布局的字体和图标尺寸
        legend = fig.legend(handles=legend_elements,
                           loc='lower center',
                           bbox_to_anchor=(0.5, -0.02),
                           ncol=3,             # 增加列数以适应横向布局
                           fontsize=13,        # 适中的图例字体
                           frameon=True,
                           fancybox=True,
                           shadow=True,
                           title='Color Coding for Segmentation Comparison',
                           title_fontsize=15,  # 适中的图例标题字体
                           markerscale=1.5,    # 适中的图例标记尺寸
                           columnspacing=1.2,  # 适中的列间距
                           handlelength=2.5,   # 适中的图例句柄长度
                           handletextpad=0.8)  # 适中的图例文字间距

        # 设置图例框的属性
        legend.get_frame().set_linewidth(2.0)  # 增大边框线宽
        legend.get_frame().set_edgecolor('black')
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_alpha(0.9)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分割模型对比可视化')
    parser.add_argument('--test_image', 
                       default='/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/static/uploads/IMG_0217.JPG',
                       help='测试图像路径')
    parser.add_argument('--annotation', 
                       default='/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/data/annotations/IMG_0217.json',
                       help='标注文件路径')
    parser.add_argument('--models_dir', 
                       default='/home/<USER>/PycharmProjects/20250629/segmentation_comparison/results/',
                       help='模型目录路径')
    parser.add_argument('--output_dir', 
                       default='./comparison_results',
                       help='输出目录')
    
    args = parser.parse_args()
    
    # 创建对比可视化器
    comparator = SegmentationComparison(
        test_image_path=args.test_image,
        annotation_path=args.annotation,
        models_dir=args.models_dir,
        output_dir=args.output_dir
    )
    
    # 生成对比图
    output_path = comparator.generate_comparison()
    print(f"✅ 对比可视化完成！输出文件: {output_path}")


if __name__ == "__main__":
    main()
