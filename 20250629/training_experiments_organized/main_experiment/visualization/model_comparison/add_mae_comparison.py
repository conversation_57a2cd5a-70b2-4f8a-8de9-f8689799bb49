#!/usr/bin/env python3
"""
添加MAE模型到分割对比可视化
"""

import os
import sys
import numpy as np
import cv2
import torch
import matplotlib.pyplot as plt
from pathlib import Path
from PIL import Image

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 导入重构框架
from core.config import create_default_config
from training import SegmentationTrainer

def create_mae_comparison():
    """创建包含MAE模型的对比图"""
    
    # 测试图像路径
    test_image_path = '/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/static/uploads/IMG_0217.JPG'
    mae_model_path = '/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/checkpoint-0315.pth'
    
    print("创建MAE模型对比...")
    
    # 加载测试图像
    image = cv2.imread(test_image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    print(f"测试图像尺寸: {image.shape}")
    
    # 创建MAE模型配置
    config = create_default_config()
    config.model.image_size = 512  # 匹配预训练模型
    config.model.mae_checkpoint_path = mae_model_path
    config.model.use_pretrained = True
    config.data.image_size = 512
    
    try:
        # 创建训练器并设置模型
        trainer = SegmentationTrainer(config)
        trainer.setup_model()
        trainer.model.eval()
        
        print("✅ MAE模型加载成功")
        
        # 大图滑动窗口预测 - 参考predict.py的方式
        device = config.get_device()

        H, W, _ = image.shape
        patch_size = 512  # MAE模型使用512x512
        overlap = 0.1
        threshold = 0.5

        stride = int(patch_size * (1 - overlap))
        prob_map = np.zeros((H, W), dtype=np.float32)
        count_map = np.zeros((H, W), dtype=np.float32)

        print(f"   🔍 MAE滑动窗口预测: {H}x{W} -> patch_size={patch_size}, stride={stride}")

        # 滑动窗口预测
        for y in range(0, H, stride):
            for x in range(0, W, stride):
                y1 = y
                x1 = x
                y2 = min(y1 + patch_size, H)
                x2 = min(x1 + patch_size, W)

                # 提取patch
                patch = image[y1:y2, x1:x2, :]

                # 填充到标准尺寸
                pad_bottom = patch_size - (y2 - y1)
                pad_right = patch_size - (x2 - x1)
                if pad_bottom > 0 or pad_right > 0:
                    patch = np.pad(patch, ((0, pad_bottom), (0, pad_right), (0, 0)), mode='constant')

                # 预处理
                patch_norm = patch.astype(np.float32) / 255.0
                patch_tensor = torch.from_numpy(patch_norm).permute(2, 0, 1).unsqueeze(0).to(device)

                # 预测
                with torch.no_grad():
                    output = trainer.model(patch_tensor)
                    # 对于多类别输出，取前景类别的概率
                    if output.shape[1] > 1:
                        # 多类别：取非背景类别的最大概率
                        pred_prob = torch.softmax(output, dim=1)
                        pred_prob = torch.max(pred_prob[:, 1:], dim=1)[0].cpu().numpy()
                    else:
                        # 二分类：使用sigmoid
                        pred_prob = torch.sigmoid(output).squeeze().cpu().numpy()

                # 去除填充部分
                pred_prob = pred_prob[:y2 - y1, :x2 - x1]

                # 累积到概率图
                prob_map[y1:y2, x1:x2] += pred_prob
                count_map[y1:y2, x1:x2] += 1

        # 平均化概率图
        prob_map = prob_map / np.maximum(count_map, 1e-6)

        # 二值化
        prediction = (prob_map > threshold).astype(np.uint8)
        
        print(f"✅ MAE模型预测完成，输出形状: {prediction.shape}")
        
        # 创建对比图
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 原始图像
        axes[0].imshow(image)
        axes[0].set_title('Original Image', fontsize=14, fontweight='bold')
        axes[0].axis('off')
        
        # 调整尺寸的图像
        axes[1].imshow(image_resized)
        axes[1].set_title('Resized (512x512)', fontsize=14, fontweight='bold')
        axes[1].axis('off')
        
        # MAE预测结果
        # 创建颜色映射
        colored_prediction = np.zeros((*prediction.shape, 3), dtype=np.uint8)
        colored_prediction[prediction == 0] = [0, 0, 0]      # 背景 - 黑色
        colored_prediction[prediction == 1] = [255, 0, 0]    # 蜂巢 - 红色
        colored_prediction[prediction > 1] = [0, 255, 0]     # 其他类别 - 绿色
        
        axes[2].imshow(colored_prediction)
        axes[2].set_title('MAE ViT-UPerNet Prediction', fontsize=14, fontweight='bold')
        axes[2].axis('off')
        
        # 添加统计信息
        unique_classes = np.unique(prediction)
        class_counts = {cls: np.sum(prediction == cls) for cls in unique_classes}
        stats_text = f"预测类别: {unique_classes.tolist()}\n"
        for cls, count in class_counts.items():
            percentage = count / prediction.size * 100
            stats_text += f"类别 {cls}: {percentage:.1f}%\n"
        
        plt.figtext(0.02, 0.02, stats_text, fontsize=10, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        
        plt.tight_layout()
        
        # 保存结果
        output_dir = Path("../../outputs/mae_comparison")
        output_dir.mkdir(parents=True, exist_ok=True)
        output_path = output_dir / "mae_segmentation_demo.png"
        
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ MAE对比图已保存到: {output_path}")
        
        # 创建详细的分析图
        create_detailed_analysis(image, image_resized, prediction, output_dir)
        
        return str(output_path)
        
    except Exception as e:
        print(f"❌ MAE模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_detailed_analysis(original_image, resized_image, prediction, output_dir):
    """创建详细的分析图"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 第一行：原始图像和预处理
    axes[0, 0].imshow(original_image)
    axes[0, 0].set_title(f'Original Image\n{original_image.shape[1]}x{original_image.shape[0]}', 
                        fontsize=12, fontweight='bold')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(resized_image)
    axes[0, 1].set_title('Resized for Model\n512x512', fontsize=12, fontweight='bold')
    axes[0, 1].axis('off')
    
    # 预测结果（原始）
    axes[0, 2].imshow(prediction, cmap='viridis')
    axes[0, 2].set_title('Raw Prediction\n(Class Indices)', fontsize=12, fontweight='bold')
    axes[0, 2].axis('off')
    
    # 第二行：分析结果
    # 二值化结果
    binary_mask = (prediction > 0).astype(np.uint8) * 255
    axes[1, 0].imshow(binary_mask, cmap='gray')
    axes[1, 0].set_title('Binary Mask\n(Foreground vs Background)', fontsize=12, fontweight='bold')
    axes[1, 0].axis('off')
    
    # 叠加结果
    overlay = resized_image.copy()
    # 将预测结果调整到与图像相同的尺寸
    prediction_resized = cv2.resize(prediction.astype(np.uint8), (512, 512), interpolation=cv2.INTER_NEAREST)
    overlay[prediction_resized > 0] = overlay[prediction_resized > 0] * 0.7 + np.array([255, 0, 0]) * 0.3
    axes[1, 1].imshow(overlay.astype(np.uint8))
    axes[1, 1].set_title('Overlay on Original\n(Red = Detected)', fontsize=12, fontweight='bold')
    axes[1, 1].axis('off')
    
    # 统计分析
    axes[1, 2].axis('off')
    
    # 计算统计信息
    total_pixels = prediction.size
    foreground_pixels = np.sum(prediction > 0)
    background_pixels = total_pixels - foreground_pixels
    
    unique_classes = np.unique(prediction)
    
    stats_text = "分割统计分析\n" + "="*20 + "\n\n"
    stats_text += f"图像尺寸: {prediction.shape[1]} x {prediction.shape[0]}\n"
    stats_text += f"总像素数: {total_pixels:,}\n\n"
    
    stats_text += f"背景像素: {background_pixels:,} ({background_pixels/total_pixels*100:.1f}%)\n"
    stats_text += f"前景像素: {foreground_pixels:,} ({foreground_pixels/total_pixels*100:.1f}%)\n\n"
    
    stats_text += f"检测到的类别: {unique_classes.tolist()}\n\n"
    
    for cls in unique_classes:
        count = np.sum(prediction == cls)
        percentage = count / total_pixels * 100
        stats_text += f"类别 {cls}: {count:,} 像素 ({percentage:.1f}%)\n"
    
    axes[1, 2].text(0.05, 0.95, stats_text, transform=axes[1, 2].transAxes,
                   fontsize=11, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    plt.tight_layout()
    
    # 保存详细分析
    analysis_path = output_dir / "mae_detailed_analysis.png"
    plt.savefig(analysis_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 详细分析图已保存到: {analysis_path}")

def main():
    """主函数"""
    print("MAE模型分割效果演示")
    print("=" * 50)
    
    result = create_mae_comparison()
    
    if result:
        print(f"\n🎉 MAE模型对比可视化完成！")
        print(f"输出文件: {result}")
        print("\n这个可视化展示了:")
        print("1. 原始测试图像")
        print("2. 预处理后的图像")
        print("3. MAE预训练ViT-UPerNet模型的分割预测")
        print("4. 详细的统计分析")
    else:
        print("\n❌ MAE模型对比可视化失败")

if __name__ == "__main__":
    main()
