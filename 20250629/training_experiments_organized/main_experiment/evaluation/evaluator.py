#!/usr/bin/env python3
"""
分割模型评估器
用于模型性能评估和指标计算
"""

import os
import sys
import time
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import cv2
from PIL import Image

# 导入重构后的模块
try:
    from ..core.config import BaseConfig
    from ..core.models import ViTBackbone, ViTUPerNetHead, MultiLayerViTFeatureExtractor
    from ..core.datasets import HoneycombDataset, create_dataloaders
    from ..core.losses import create_loss_function
    from ..utils import SegmentationMetrics, MAEWeightLoader
except ImportError:
    # 当作为脚本直接运行时的绝对导入
    from core.config import BaseConfig
    from core.models import ViTBackbone, ViTUPerNetHead, MultiLayerViTFeatureExtractor
    from core.datasets import HoneycombDataset, create_dataloaders
    from core.losses import create_loss_function
    from utils import SegmentationMetrics, MAEWeightLoader


class SegmentationEvaluator:
    """分割模型评估器"""
    
    def __init__(self, config: BaseConfig, model_path: str):
        self.config = config
        self.model_path = model_path
        self.device = config.get_device()
        
        # 设置输出目录
        self.output_dir = Path(config.system.output_dir) / "evaluation"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self._setup_logging()
        
        # 初始化组件
        self.model = None
        self.test_loader = None
        self.loss_function = None
        
        logging.info(f"评估器初始化完成，设备: {self.device}")
    
    def _setup_logging(self):
        """设置日志系统"""
        log_file = self.output_dir / "evaluation.log"
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def setup_model(self):
        """设置模型"""
        logging.info("正在设置模型...")
        
        # 创建ViT特征提取器
        # 使用标准的timm模型名称，图像尺寸通过img_size参数指定
        model_name = f"vit_{self.config.model.get_model_size()}_patch{self.config.model.patch_size}_224"
        feature_extractor = MultiLayerViTFeatureExtractor(
            model_name=model_name,
            img_size=self.config.model.image_size,
            pretrained_path=None  # 评估时不加载预训练权重
        )
        
        # 创建分割头
        segmentation_head = ViTUPerNetHead(
            feature_channels=self.config.model.embed_dim,
            fpn_dim=self.config.model.upernet_channels,
            num_classes=self.config.model.num_classes,
            pool_scales=self.config.model.upernet_pool_scales
        )
        
        # 创建组合模型包装器
        class SegmentationModel(nn.Module):
            def __init__(self, feature_extractor, segmentation_head, target_size=(128, 128)):
                super().__init__()
                self.feature_extractor = feature_extractor
                self.segmentation_head = segmentation_head
                self.target_size = target_size

            def forward(self, x):
                # 提取特征
                raw_features = self.feature_extractor(x)

                # 转换特征格式：从嵌套字典提取spatial特征
                features = {}
                for layer_name, layer_features in raw_features.items():
                    if isinstance(layer_features, dict) and 'spatial' in layer_features:
                        features[layer_name] = layer_features['spatial']
                    else:
                        features[layer_name] = layer_features

                # 分割预测，传递target_size
                output_dict = self.segmentation_head(features, self.target_size)

                # 返回主要输出
                if isinstance(output_dict, dict) and 'main' in output_dict:
                    return output_dict['main']
                else:
                    return output_dict

        # 计算目标尺寸（通常是输入尺寸的1/4）
        target_size = (self.config.model.image_size // 4, self.config.model.image_size // 4)

        self.model = SegmentationModel(
            feature_extractor,
            segmentation_head,
            target_size
        ).to(self.device)
        
        # 加载训练好的权重
        self.load_model_weights()
        
        # 设置为评估模式
        self.model.eval()
        
        logging.info(f"模型设置完成，参数量: {sum(p.numel() for p in self.model.parameters()):,}")
    
    def load_model_weights(self):
        """加载模型权重"""
        logging.info(f"加载模型权重: {self.model_path}")
        
        checkpoint = torch.load(self.model_path, map_location=self.device)
        
        # 处理不同的checkpoint格式
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
        elif 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
        else:
            state_dict = checkpoint
        
        # 加载权重
        self.model.load_state_dict(state_dict)
        
        logging.info("模型权重加载完成")
    
    def setup_data(self, data_split: str = "test"):
        """设置数据加载器"""
        logging.info(f"正在设置{data_split}数据加载器...")
        
        if data_split == "test":
            # 创建测试数据加载器
            test_images_dir, test_masks_dir = self.config.data.get_test_data_path()
            
            test_dataset = HoneycombDataset(
                images_dir=test_images_dir,
                masks_dir=test_masks_dir,
                image_size=self.config.data.image_size,
                normalize_mean=self.config.data.normalize_mean,
                normalize_std=self.config.data.normalize_std,
                use_augmentation=False  # 评估时不使用数据增强
            )
            
            self.test_loader = DataLoader(
                test_dataset,
                batch_size=self.config.data.batch_size,
                shuffle=False,
                num_workers=self.config.data.num_workers,
                pin_memory=self.config.data.pin_memory
            )
        else:
            # 使用验证集
            _, self.test_loader = create_dataloaders(self.config.data)
        
        logging.info(f"{data_split}数据加载器设置完成: {len(self.test_loader)} batches")
    
    def setup_loss_function(self):
        """设置损失函数"""
        logging.info("正在设置损失函数...")
        
        self.loss_function = create_loss_function(
            loss_type=self.config.training.loss_function,
            num_classes=self.config.model.num_classes,
            dice_weight=self.config.training.dice_weight,
            ce_weight=self.config.training.ce_weight,
            focal_weight=self.config.training.focal_weight,
            focal_alpha=self.config.training.focal_alpha,
            focal_gamma=self.config.training.focal_gamma
        )
        
        logging.info(f"损失函数设置完成: {self.config.training.loss_function}")
    
    def evaluate(self, save_predictions: bool = False) -> Dict[str, float]:
        """评估模型"""
        logging.info("开始模型评估...")
        
        # 重置指标
        metrics = SegmentationMetrics(
            num_classes=self.config.model.num_classes,
            ignore_index=self.config.data.ignore_index,
            class_names=self.config.data.class_names
        )
        
        total_loss = 0.0
        num_batches = len(self.test_loader)
        
        # 预测结果保存
        if save_predictions:
            pred_dir = self.output_dir / "predictions"
            pred_dir.mkdir(exist_ok=True)
        
        with torch.no_grad():
            progress_bar = tqdm(self.test_loader, desc="Evaluating")
            
            for batch_idx, (images, targets) in enumerate(progress_bar):
                images = images.to(self.device)
                targets = targets.to(self.device)
                
                # 前向传播
                start_time = time.time()
                outputs = self.model(images)
                inference_time = time.time() - start_time
                
                # 计算损失
                if self.loss_function:
                    loss_dict = self.loss_function(outputs, targets)
                    loss = loss_dict['total_loss'] if isinstance(loss_dict, dict) else loss_dict
                    total_loss += loss.item()
                
                # 更新指标
                metrics.update(outputs, targets, inference_time)
                
                # 保存预测结果
                if save_predictions:
                    self._save_batch_predictions(
                        images, targets, outputs, batch_idx, pred_dir
                    )
                
                # 更新进度条
                progress_bar.set_postfix({
                    'loss': f"{loss.item():.4f}" if self.loss_function else "N/A"
                })
        
        # 计算最终指标
        final_metrics = metrics.compute_all_metrics()
        if self.loss_function:
            final_metrics['loss'] = total_loss / num_batches
        
        # 打印评估结果
        self._print_evaluation_results(final_metrics)
        
        # 保存评估结果
        self._save_evaluation_results(final_metrics)
        
        logging.info("模型评估完成")
        return final_metrics
    
    def _save_batch_predictions(
        self, 
        images: torch.Tensor, 
        targets: torch.Tensor, 
        outputs: torch.Tensor, 
        batch_idx: int, 
        pred_dir: Path
    ):
        """保存批次预测结果"""
        # 获取预测类别
        pred_classes = torch.argmax(outputs, dim=1)
        
        # 转换为numpy
        images_np = images.cpu().numpy()
        targets_np = targets.cpu().numpy()
        pred_np = pred_classes.cpu().numpy()
        
        # 保存每个样本
        for i in range(images_np.shape[0]):
            sample_idx = batch_idx * self.config.data.batch_size + i
            
            # 反归一化图像
            image = self._denormalize_image(images_np[i])
            
            # 保存原图、真实标签和预测结果
            cv2.imwrite(str(pred_dir / f"sample_{sample_idx:04d}_image.png"), image)
            cv2.imwrite(str(pred_dir / f"sample_{sample_idx:04d}_target.png"), targets_np[i] * 32)  # 放大显示
            cv2.imwrite(str(pred_dir / f"sample_{sample_idx:04d}_pred.png"), pred_np[i] * 32)  # 放大显示
    
    def _denormalize_image(self, image: np.ndarray) -> np.ndarray:
        """反归一化图像"""
        # image shape: (C, H, W)
        mean = np.array(self.config.data.normalize_mean).reshape(3, 1, 1)
        std = np.array(self.config.data.normalize_std).reshape(3, 1, 1)
        
        # 反归一化
        image = image * std + mean
        image = np.clip(image, 0, 1)
        
        # 转换为BGR格式用于OpenCV
        image = (image * 255).astype(np.uint8)
        image = np.transpose(image, (1, 2, 0))  # (H, W, C)
        image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
        
        return image
    
    def _print_evaluation_results(self, metrics: Dict[str, float]):
        """打印评估结果"""
        print("\n" + "=" * 60)
        print("模型评估结果")
        print("=" * 60)
        
        # 主要指标
        print("主要指标:")
        print(f"  mIoU:      {metrics.get('mIoU', 0.0):.4f}")
        print(f"  mDice:     {metrics.get('mDice', 0.0):.4f}")
        print(f"  Accuracy:  {metrics.get('Accuracy', 0.0):.4f}")
        print(f"  mPrecision: {metrics.get('mPrecision', 0.0):.4f}")
        print(f"  mRecall:   {metrics.get('mRecall', 0.0):.4f}")
        print(f"  mF1:       {metrics.get('mF1', 0.0):.4f}")
        
        if 'loss' in metrics:
            print(f"  Loss:      {metrics['loss']:.4f}")
        
        # 速度指标
        if 'avg_inference_time' in metrics:
            print(f"\n速度指标:")
            print(f"  平均推理时间: {metrics['avg_inference_time']:.4f}s")
            print(f"  FPS:         {metrics['fps']:.2f}")
        
        # 各类别IoU
        print(f"\n各类别IoU:")
        for class_name in self.config.data.class_names:
            iou_key = f'IoU_{class_name}'
            if iou_key in metrics:
                print(f"  {class_name:12}: {metrics[iou_key]:.4f}")
        
        print("=" * 60)
    
    def _save_evaluation_results(self, metrics: Dict[str, float]):
        """保存评估结果到文件"""
        import json
        
        # 保存为JSON格式
        results_file = self.output_dir / "evaluation_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2, ensure_ascii=False)
        
        # 保存为CSV格式（主要指标）
        import csv
        csv_file = self.output_dir / "evaluation_summary.csv"
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Metric', 'Value'])
            
            # 主要指标
            main_metrics = ['mIoU', 'mDice', 'Accuracy', 'mPrecision', 'mRecall', 'mF1', 'loss']
            for metric in main_metrics:
                if metric in metrics:
                    writer.writerow([metric, f"{metrics[metric]:.4f}"])
        
        logging.info(f"评估结果已保存到: {results_file}")
        logging.info(f"评估摘要已保存到: {csv_file}")
    
    def setup_all(self):
        """设置所有组件"""
        self.setup_model()
        self.setup_data()
        self.setup_loss_function()
        
        logging.info("所有组件设置完成，准备开始评估")
