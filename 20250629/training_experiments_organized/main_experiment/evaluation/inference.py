#!/usr/bin/env python3
"""
推理引擎
用于单张图像或批量图像的推理
"""

import os
import sys
import time
from pathlib import Path
from typing import Dict, Any, Optional, List, Union

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import cv2
from PIL import Image
import torchvision.transforms as transforms

# 导入重构后的模块
try:
    from ..core.config import BaseConfig
    from ..core.models import ViTBackbone, ViTUPerNetHead, MultiLayerViTFeatureExtractor
    from ..utils import MAEWeightLoader
except ImportError:
    # 当作为脚本直接运行时的绝对导入
    from core.config import BaseConfig
    from core.models import ViTBackbone, ViTUPerNetHead, MultiLayerViTFeatureExtractor
    from utils import MAEWeightLoader


class InferenceEngine:
    """推理引擎"""
    
    def __init__(self, config: BaseConfig, model_path: str):
        self.config = config
        self.model_path = model_path
        self.device = config.get_device()
        
        # 初始化组件
        self.model = None
        self.transform = None
        
        # 设置模型和预处理
        self.setup_model()
        self.setup_transform()
        
        print(f"推理引擎初始化完成，设备: {self.device}")
    
    def setup_model(self):
        """设置模型"""
        print("正在设置模型...")
        
        # 创建ViT特征提取器
        # 使用标准的timm模型名称，图像尺寸通过img_size参数指定
        model_name = f"vit_{self.config.model.get_model_size()}_patch{self.config.model.patch_size}_224"
        feature_extractor = MultiLayerViTFeatureExtractor(
            model_name=model_name,
            img_size=self.config.model.image_size,
            pretrained_path=None  # 推理时不加载预训练权重
        )
        
        # 创建分割头
        segmentation_head = ViTUPerNetHead(
            feature_channels=self.config.model.embed_dim,
            fpn_dim=self.config.model.upernet_channels,
            num_classes=self.config.model.num_classes,
            pool_scales=self.config.model.upernet_pool_scales
        )
        
        # 创建组合模型包装器
        class SegmentationModel(nn.Module):
            def __init__(self, feature_extractor, segmentation_head, target_size=(128, 128)):
                super().__init__()
                self.feature_extractor = feature_extractor
                self.segmentation_head = segmentation_head
                self.target_size = target_size

            def forward(self, x):
                # 提取特征
                raw_features = self.feature_extractor(x)

                # 转换特征格式：从嵌套字典提取spatial特征
                features = {}
                for layer_name, layer_features in raw_features.items():
                    if isinstance(layer_features, dict) and 'spatial' in layer_features:
                        features[layer_name] = layer_features['spatial']
                    else:
                        features[layer_name] = layer_features

                # 分割预测，传递target_size
                output_dict = self.segmentation_head(features, self.target_size)

                # 返回主要输出
                if isinstance(output_dict, dict) and 'main' in output_dict:
                    return output_dict['main']
                else:
                    return output_dict

        # 计算目标尺寸（通常是输入尺寸的1/4）
        target_size = (self.config.model.image_size // 4, self.config.model.image_size // 4)

        self.model = SegmentationModel(
            feature_extractor,
            segmentation_head,
            target_size
        ).to(self.device)
        
        # 加载训练好的权重
        self.load_model_weights()
        
        # 设置为评估模式
        self.model.eval()
        
        print(f"模型设置完成，参数量: {sum(p.numel() for p in self.model.parameters()):,}")
    
    def load_model_weights(self):
        """加载模型权重"""
        print(f"加载模型权重: {self.model_path}")
        
        checkpoint = torch.load(self.model_path, map_location=self.device)
        
        # 处理不同的checkpoint格式
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
        elif 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
        else:
            state_dict = checkpoint
        
        # 加载权重
        self.model.load_state_dict(state_dict)
        
        print("模型权重加载完成")
    
    def setup_transform(self):
        """设置图像预处理"""
        self.transform = transforms.Compose([
            transforms.Resize((self.config.model.image_size, self.config.model.image_size)),
            transforms.ToTensor(),
            transforms.Normalize(
                mean=self.config.data.normalize_mean,
                std=self.config.data.normalize_std
            )
        ])
    
    def preprocess_image(self, image: Union[str, np.ndarray, Image.Image]) -> torch.Tensor:
        """预处理单张图像"""
        # 加载图像
        if isinstance(image, str):
            image = Image.open(image).convert('RGB')
        elif isinstance(image, np.ndarray):
            image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        elif not isinstance(image, Image.Image):
            raise ValueError(f"不支持的图像类型: {type(image)}")
        
        # 应用预处理
        image_tensor = self.transform(image)
        
        # 添加batch维度
        image_tensor = image_tensor.unsqueeze(0)
        
        return image_tensor
    
    def predict_single(
        self, 
        image: Union[str, np.ndarray, Image.Image],
        return_probs: bool = False
    ) -> Dict[str, Union[np.ndarray, float]]:
        """预测单张图像"""
        # 预处理图像
        image_tensor = self.preprocess_image(image).to(self.device)
        
        # 推理
        with torch.no_grad():
            start_time = time.time()
            outputs = self.model(image_tensor)
            inference_time = time.time() - start_time
        
        # 后处理
        if return_probs:
            probs = F.softmax(outputs, dim=1)
            probs_np = probs.cpu().numpy()[0]  # 移除batch维度
        else:
            probs_np = None
        
        # 获取预测类别
        pred_classes = torch.argmax(outputs, dim=1)
        pred_np = pred_classes.cpu().numpy()[0]  # 移除batch维度
        
        # 获取置信度（最大概率）
        max_probs = F.softmax(outputs, dim=1).max(dim=1)[0]
        confidence = max_probs.cpu().numpy()[0].mean()  # 平均置信度
        
        result = {
            'prediction': pred_np,
            'confidence': confidence,
            'inference_time': inference_time
        }
        
        if return_probs:
            result['probabilities'] = probs_np
        
        return result
    
    def predict_batch(
        self, 
        images: List[Union[str, np.ndarray, Image.Image]],
        batch_size: int = 8,
        return_probs: bool = False
    ) -> List[Dict[str, Union[np.ndarray, float]]]:
        """批量预测图像"""
        results = []
        
        # 分批处理
        for i in range(0, len(images), batch_size):
            batch_images = images[i:i + batch_size]
            
            # 预处理批次
            batch_tensors = []
            for img in batch_images:
                img_tensor = self.preprocess_image(img)
                batch_tensors.append(img_tensor)
            
            # 合并为批次张量
            batch_tensor = torch.cat(batch_tensors, dim=0).to(self.device)
            
            # 推理
            with torch.no_grad():
                start_time = time.time()
                outputs = self.model(batch_tensor)
                inference_time = time.time() - start_time
            
            # 后处理每个样本
            for j in range(outputs.shape[0]):
                output = outputs[j:j+1]  # 保持batch维度
                
                if return_probs:
                    probs = F.softmax(output, dim=1)
                    probs_np = probs.cpu().numpy()[0]
                else:
                    probs_np = None
                
                # 获取预测类别
                pred_classes = torch.argmax(output, dim=1)
                pred_np = pred_classes.cpu().numpy()[0]
                
                # 获取置信度
                max_probs = F.softmax(output, dim=1).max(dim=1)[0]
                confidence = max_probs.cpu().numpy()[0].mean()
                
                result = {
                    'prediction': pred_np,
                    'confidence': confidence,
                    'inference_time': inference_time / len(batch_images)  # 平均时间
                }
                
                if return_probs:
                    result['probabilities'] = probs_np
                
                results.append(result)
        
        return results
    
    def predict_directory(
        self, 
        input_dir: str, 
        output_dir: str,
        save_visualization: bool = True,
        batch_size: int = 8
    ) -> Dict[str, Any]:
        """预测目录中的所有图像"""
        input_dir = Path(input_dir)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取所有图像文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = []
        for ext in image_extensions:
            image_files.extend(input_dir.glob(f'*{ext}'))
            image_files.extend(input_dir.glob(f'*{ext.upper()}'))
        
        if not image_files:
            raise ValueError(f"在目录 {input_dir} 中未找到图像文件")
        
        print(f"找到 {len(image_files)} 个图像文件")
        
        # 批量预测
        image_paths = [str(f) for f in image_files]
        results = self.predict_batch(image_paths, batch_size=batch_size)
        
        # 保存结果
        total_time = 0.0
        for i, (image_file, result) in enumerate(zip(image_files, results)):
            # 保存预测掩码
            pred_mask = result['prediction']
            mask_file = output_dir / f"{image_file.stem}_pred.png"
            cv2.imwrite(str(mask_file), pred_mask.astype(np.uint8) * 32)  # 放大显示
            
            # 保存可视化结果
            if save_visualization:
                self._save_visualization(
                    str(image_file), 
                    pred_mask, 
                    output_dir / f"{image_file.stem}_vis.png"
                )
            
            total_time += result['inference_time']
        
        # 统计信息
        avg_time = total_time / len(image_files)
        fps = 1.0 / avg_time if avg_time > 0 else 0.0
        
        summary = {
            'total_images': len(image_files),
            'total_time': total_time,
            'avg_inference_time': avg_time,
            'fps': fps,
            'output_directory': str(output_dir)
        }
        
        print(f"预测完成!")
        print(f"总图像数: {summary['total_images']}")
        print(f"总时间: {summary['total_time']:.2f}s")
        print(f"平均推理时间: {summary['avg_inference_time']:.4f}s")
        print(f"FPS: {summary['fps']:.2f}")
        print(f"结果保存到: {summary['output_directory']}")
        
        return summary
    
    def _save_visualization(self, image_path: str, pred_mask: np.ndarray, output_path: Path):
        """保存可视化结果"""
        # 加载原图
        image = cv2.imread(image_path)
        if image is None:
            return
        
        # 调整掩码尺寸以匹配原图
        h, w = image.shape[:2]
        pred_mask_resized = cv2.resize(pred_mask.astype(np.uint8), (w, h), interpolation=cv2.INTER_NEAREST)
        
        # 创建彩色掩码
        colored_mask = self._create_colored_mask(pred_mask_resized)
        
        # 叠加到原图
        alpha = 0.6
        overlay = cv2.addWeighted(image, 1 - alpha, colored_mask, alpha, 0)
        
        # 保存结果
        cv2.imwrite(str(output_path), overlay)
    
    def _create_colored_mask(self, mask: np.ndarray) -> np.ndarray:
        """创建彩色掩码"""
        # 定义颜色映射（BGR格式）
        colors = [
            [0, 0, 0],       # 背景 - 黑色
            [0, 255, 0],     # 卵 - 绿色
            [255, 0, 0],     # 幼虫 - 蓝色
            [0, 255, 255],   # 封盖子 - 黄色
            [255, 0, 255],   # 花粉 - 品红
            [255, 255, 0],   # 花蜜 - 青色
            [128, 128, 128], # 蜂蜜 - 灰色
            [255, 255, 255]  # 其他 - 白色
        ]
        
        h, w = mask.shape
        colored_mask = np.zeros((h, w, 3), dtype=np.uint8)
        
        for class_id, color in enumerate(colors):
            if class_id < self.config.model.num_classes:
                colored_mask[mask == class_id] = color
        
        return colored_mask
