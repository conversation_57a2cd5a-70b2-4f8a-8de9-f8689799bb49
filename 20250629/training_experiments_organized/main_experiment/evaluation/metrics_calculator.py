#!/usr/bin/env python3
"""
指标计算器
用于计算和比较不同模型的性能指标
"""

import os
import json
import csv
from pathlib import Path
from typing import Dict, List, Any, Optional
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False
    print("警告: seaborn未安装，某些可视化功能将不可用")


class MetricsCalculator:
    """指标计算器"""
    
    def __init__(self, output_dir: str = "./metrics_analysis"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 存储多个模型的指标
        self.model_metrics = {}
        
    def add_model_metrics(self, model_name: str, metrics: Dict[str, float]):
        """添加模型指标"""
        self.model_metrics[model_name] = metrics
        print(f"已添加模型 '{model_name}' 的指标")
    
    def load_metrics_from_file(self, model_name: str, metrics_file: str):
        """从文件加载模型指标"""
        with open(metrics_file, 'r', encoding='utf-8') as f:
            metrics = json.load(f)
        
        self.add_model_metrics(model_name, metrics)
    
    def load_metrics_from_directory(self, models_dir: str):
        """从目录加载多个模型的指标"""
        models_dir = Path(models_dir)
        
        for model_dir in models_dir.iterdir():
            if model_dir.is_dir():
                metrics_file = model_dir / "evaluation" / "evaluation_results.json"
                if metrics_file.exists():
                    self.load_metrics_from_file(model_dir.name, str(metrics_file))
    
    def compare_models(self, metrics_to_compare: Optional[List[str]] = None) -> pd.DataFrame:
        """比较多个模型的指标"""
        if not self.model_metrics:
            raise ValueError("没有可比较的模型指标")
        
        if metrics_to_compare is None:
            metrics_to_compare = ['mIoU', 'mDice', 'Accuracy', 'mPrecision', 'mRecall', 'mF1']
        
        # 创建比较表
        comparison_data = []
        for model_name, metrics in self.model_metrics.items():
            row = {'Model': model_name}
            for metric in metrics_to_compare:
                row[metric] = metrics.get(metric, 0.0)
            comparison_data.append(row)
        
        df = pd.DataFrame(comparison_data)
        
        # 保存比较结果
        csv_file = self.output_dir / "model_comparison.csv"
        df.to_csv(csv_file, index=False)
        print(f"模型比较结果已保存到: {csv_file}")
        
        return df
    
    def create_comparison_chart(self, metrics_to_plot: Optional[List[str]] = None):
        """创建模型比较图表"""
        if not self.model_metrics:
            raise ValueError("没有可比较的模型指标")
        
        if metrics_to_plot is None:
            metrics_to_plot = ['mIoU', 'mDice', 'Accuracy']
        
        # 准备数据
        models = list(self.model_metrics.keys())
        metric_values = {metric: [] for metric in metrics_to_plot}
        
        for model_name in models:
            metrics = self.model_metrics[model_name]
            for metric in metrics_to_plot:
                metric_values[metric].append(metrics.get(metric, 0.0))
        
        # 创建图表
        fig, axes = plt.subplots(1, len(metrics_to_plot), figsize=(5 * len(metrics_to_plot), 6))
        if len(metrics_to_plot) == 1:
            axes = [axes]
        
        for i, metric in enumerate(metrics_to_plot):
            ax = axes[i]
            bars = ax.bar(models, metric_values[metric])
            ax.set_title(f'{metric} Comparison')
            ax.set_ylabel(metric)
            ax.set_ylim(0, 1.0)
            
            # 添加数值标签
            for bar, value in zip(bars, metric_values[metric]):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{value:.3f}', ha='center', va='bottom')
            
            # 旋转x轴标签
            ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = self.output_dir / "model_comparison_chart.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"比较图表已保存到: {chart_file}")
    
    def create_class_wise_comparison(self, class_names: Optional[List[str]] = None):
        """创建各类别指标比较"""
        if not self.model_metrics:
            raise ValueError("没有可比较的模型指标")
        
        if class_names is None:
            class_names = ['background', 'eggs', 'larvae', 'capped_brood', 'pollen', 'nectar', 'honey', 'other']
        
        # 准备IoU数据
        iou_data = []
        for model_name, metrics in self.model_metrics.items():
            for class_name in class_names:
                iou_key = f'IoU_{class_name}'
                if iou_key in metrics:
                    iou_data.append({
                        'Model': model_name,
                        'Class': class_name,
                        'IoU': metrics[iou_key]
                    })
        
        if not iou_data:
            print("没有找到类别级别的IoU指标")
            return
        
        df = pd.DataFrame(iou_data)
        
        # 创建热力图
        pivot_df = df.pivot(index='Model', columns='Class', values='IoU')
        
        plt.figure(figsize=(12, 8))
        if HAS_SEABORN:
            sns.heatmap(pivot_df, annot=True, fmt='.3f', cmap='YlOrRd',
                       cbar_kws={'label': 'IoU'})
        else:
            # 使用matplotlib的imshow作为替代
            im = plt.imshow(pivot_df.values, cmap='YlOrRd', aspect='auto')
            plt.colorbar(im, label='IoU')
            plt.xticks(range(len(pivot_df.columns)), pivot_df.columns, rotation=45)
            plt.yticks(range(len(pivot_df.index)), pivot_df.index)

            # 添加数值标注
            for i in range(len(pivot_df.index)):
                for j in range(len(pivot_df.columns)):
                    plt.text(j, i, f'{pivot_df.iloc[i, j]:.3f}',
                            ha='center', va='center', color='black')

        plt.title('Class-wise IoU Comparison')
        plt.tight_layout()
        
        # 保存图表
        heatmap_file = self.output_dir / "class_wise_iou_heatmap.png"
        plt.savefig(heatmap_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        # 保存数据
        csv_file = self.output_dir / "class_wise_iou.csv"
        pivot_df.to_csv(csv_file)
        
        print(f"类别级别IoU热力图已保存到: {heatmap_file}")
        print(f"类别级别IoU数据已保存到: {csv_file}")
    
    def create_speed_comparison(self):
        """创建速度指标比较"""
        if not self.model_metrics:
            raise ValueError("没有可比较的模型指标")
        
        # 准备速度数据
        speed_data = []
        for model_name, metrics in self.model_metrics.items():
            if 'avg_inference_time' in metrics and 'fps' in metrics:
                speed_data.append({
                    'Model': model_name,
                    'Inference Time (s)': metrics['avg_inference_time'],
                    'FPS': metrics['fps']
                })
        
        if not speed_data:
            print("没有找到速度指标")
            return
        
        df = pd.DataFrame(speed_data)
        
        # 创建双轴图表
        fig, ax1 = plt.subplots(figsize=(10, 6))
        
        # 推理时间
        color = 'tab:red'
        ax1.set_xlabel('Model')
        ax1.set_ylabel('Inference Time (s)', color=color)
        bars1 = ax1.bar([f"{m}\n(Time)" for m in df['Model']], df['Inference Time (s)'], 
                       color=color, alpha=0.7, width=0.4)
        ax1.tick_params(axis='y', labelcolor=color)
        
        # FPS
        ax2 = ax1.twinx()
        color = 'tab:blue'
        ax2.set_ylabel('FPS', color=color)
        bars2 = ax2.bar([f"{m}\n(FPS)" for m in df['Model']], df['FPS'], 
                       color=color, alpha=0.7, width=0.4)
        ax2.tick_params(axis='y', labelcolor=color)
        
        # 添加数值标签
        for bar, value in zip(bars1, df['Inference Time (s)']):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                    f'{value:.4f}', ha='center', va='bottom')
        
        for bar, value in zip(bars2, df['FPS']):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{value:.1f}', ha='center', va='bottom')
        
        plt.title('Speed Comparison')
        plt.tight_layout()
        
        # 保存图表
        speed_chart_file = self.output_dir / "speed_comparison.png"
        plt.savefig(speed_chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        # 保存数据
        speed_csv_file = self.output_dir / "speed_comparison.csv"
        df.to_csv(speed_csv_file, index=False)
        
        print(f"速度比较图表已保存到: {speed_chart_file}")
        print(f"速度比较数据已保存到: {speed_csv_file}")
    
    def generate_comprehensive_report(self):
        """生成综合报告"""
        if not self.model_metrics:
            raise ValueError("没有可比较的模型指标")
        
        # 创建所有比较图表
        print("生成模型比较图表...")
        self.create_comparison_chart()
        
        print("生成类别级别比较...")
        self.create_class_wise_comparison()
        
        print("生成速度比较...")
        self.create_speed_comparison()
        
        # 生成比较表
        print("生成比较表...")
        comparison_df = self.compare_models()
        
        # 生成Markdown报告
        report_file = self.output_dir / "comprehensive_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 模型性能比较报告\n\n")
            
            f.write("## 总体指标比较\n\n")
            f.write(comparison_df.to_markdown(index=False))
            f.write("\n\n")
            
            f.write("## 最佳模型\n\n")
            best_models = {}
            for metric in ['mIoU', 'mDice', 'Accuracy']:
                if metric in comparison_df.columns:
                    best_idx = comparison_df[metric].idxmax()
                    best_model = comparison_df.loc[best_idx, 'Model']
                    best_value = comparison_df.loc[best_idx, metric]
                    best_models[metric] = (best_model, best_value)
                    f.write(f"- **{metric}**: {best_model} ({best_value:.4f})\n")
            
            f.write("\n## 图表说明\n\n")
            f.write("- `model_comparison_chart.png`: 主要指标比较柱状图\n")
            f.write("- `class_wise_iou_heatmap.png`: 各类别IoU热力图\n")
            f.write("- `speed_comparison.png`: 推理速度比较图\n")
            
            f.write("\n## 数据文件\n\n")
            f.write("- `model_comparison.csv`: 模型比较数据\n")
            f.write("- `class_wise_iou.csv`: 类别级别IoU数据\n")
            f.write("- `speed_comparison.csv`: 速度比较数据\n")
        
        print(f"综合报告已生成: {report_file}")
        print(f"所有结果保存在: {self.output_dir}")
        
        return str(report_file)
    
    def get_best_model(self, metric: str = 'mIoU') -> tuple:
        """获取指定指标下的最佳模型"""
        if not self.model_metrics:
            raise ValueError("没有可比较的模型指标")
        
        best_model = None
        best_value = -1
        
        for model_name, metrics in self.model_metrics.items():
            if metric in metrics and metrics[metric] > best_value:
                best_value = metrics[metric]
                best_model = model_name
        
        return best_model, best_value
