#!/usr/bin/env python3
"""
现代化的Focal Loss实现，专门适用于语义分割任务
解决类别不平衡问题，特别适用于蜂巢8类分割任务
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, List, Union
import numpy as np

class FocalLoss(nn.Module):
    """
    Focal Loss实现，用于解决类别不平衡问题
    
    论文: "Focal Loss for Dense Object Detection" by <PERSON> et al.
    https://arxiv.org/abs/1708.02002
    
    Args:
        alpha: 类别权重，可以是单个数值、列表或None
        gamma: 聚焦参数，默认2.0
        ignore_index: 忽略的类别索引，默认-100
        reduction: 损失缩减方式，'mean'、'sum'或'none'
        label_smoothing: 标签平滑参数，默认0.0
    """
    
    def __init__(
        self,
        alpha: Optional[Union[float, List[float], torch.Tensor]] = None,
        gamma: float = 2.0,
        ignore_index: int = -100,
        reduction: str = 'mean',
        label_smoothing: float = 0.0
    ):
        super(Focal<PERSON>oss, self).__init__()
        self.gamma = gamma
        self.ignore_index = ignore_index
        self.reduction = reduction
        self.label_smoothing = label_smoothing
        
        # 处理alpha参数
        if alpha is None:
            self.alpha = None
        elif isinstance(alpha, (float, int)):
            # 对于标量alpha，我们需要知道类别数量
            # 这里我们假设是8类语义分割，所有类别使用相同的alpha值
            self.alpha = torch.full((8,), alpha, dtype=torch.float32)
        elif isinstance(alpha, list):
            self.alpha = torch.tensor(alpha, dtype=torch.float32)
        elif isinstance(alpha, torch.Tensor):
            self.alpha = alpha.float()
        else:
            raise ValueError(f"Unsupported alpha type: {type(alpha)}")

        # 确保alpha数组有足够的长度
        if self.alpha is not None and len(self.alpha) < 8:
            # 扩展alpha数组到8个类别，用最后一个值填充
            last_val = self.alpha[-1] if len(self.alpha) > 0 else 1.0
            extended_alpha = torch.full((8,), last_val, dtype=torch.float32)
            extended_alpha[:len(self.alpha)] = self.alpha
            self.alpha = extended_alpha
    
    def forward(self, input: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            input: 预测logits [N, C, H, W]
            target: 真实标签 [N, H, W]
        
        Returns:
            focal loss值
        """
        # 验证输入形状
        if input.dim() != 4:
            raise ValueError(f"Expected input to be 4D [N, C, H, W], got {input.dim()}D")
        if target.dim() != 3:
            raise ValueError(f"Expected target to be 3D [N, H, W], got {target.dim()}D")
        
        N, C, H, W = input.shape
        
        # 重塑张量: [N, C, H, W] -> [N*H*W, C]
        input = input.permute(0, 2, 3, 1).contiguous().view(-1, C)
        target = target.view(-1)
        
        # 创建掩码，排除ignore_index
        valid_mask = (target != self.ignore_index)
        
        if not valid_mask.any():
            # 如果所有像素都被忽略，返回零损失
            return torch.tensor(0.0, device=input.device, requires_grad=True)
        
        # 只计算有效像素的损失
        input = input[valid_mask]
        target = target[valid_mask]
        
        # 计算交叉熵损失的组成部分
        log_pt = F.log_softmax(input, dim=1)
        ce_loss = F.nll_loss(log_pt, target, reduction='none')
        
        # 计算pt = exp(log_pt)
        pt = torch.exp(-ce_loss)
        
        # 应用alpha权重
        if self.alpha is not None:
            if self.alpha.device != input.device:
                self.alpha = self.alpha.to(input.device)

            # 安全的索引检查：确保target值在alpha数组范围内
            max_target = target.max().item()
            if max_target >= len(self.alpha):
                raise ValueError(f"Target contains class {max_target} but alpha only has {len(self.alpha)} classes")

            min_target = target.min().item()
            if min_target < 0:
                raise ValueError(f"Target contains negative class {min_target}")

            # 为每个样本选择对应的alpha值
            alpha_t = self.alpha[target]
            ce_loss = alpha_t * ce_loss
        
        # 计算focal loss
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        # 应用reduction
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class CombinedLoss(nn.Module):
    """
    组合损失函数：Focal Loss + Dice Loss
    专门用于语义分割任务
    """
    
    def __init__(
        self,
        focal_weight: float = 1.0,
        dice_weight: float = 1.0,
        focal_alpha: Optional[Union[float, List[float]]] = None,
        focal_gamma: float = 2.0,
        ignore_index: int = -100
    ):
        super(CombinedLoss, self).__init__()
        self.focal_weight = focal_weight
        self.dice_weight = dice_weight
        
        # 导入Dice Loss（从现有的loss_metrics.py）
        try:
            from .loss_metrics import DiceLoss
            self.dice_loss = DiceLoss(ignore_index=ignore_index)
        except ImportError:
            # 简化版Dice Loss实现
            self.dice_loss = self._simple_dice_loss
        
        # Focal Loss
        self.focal_loss = FocalLoss(
            alpha=focal_alpha,
            gamma=focal_gamma,
            ignore_index=ignore_index,
            reduction='mean'
        )
    
    def _simple_dice_loss(self, input: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """简化版Dice Loss实现"""
        smooth = 1e-6
        
        # 应用softmax
        input_soft = F.softmax(input, dim=1)
        
        # 创建one-hot编码
        num_classes = input.size(1)
        target_one_hot = F.one_hot(target, num_classes=num_classes)
        target_one_hot = target_one_hot.permute(0, 3, 1, 2).float()
        
        # 计算Dice系数
        intersection = (input_soft * target_one_hot).sum(dim=(2, 3))
        union = input_soft.sum(dim=(2, 3)) + target_one_hot.sum(dim=(2, 3))
        
        dice = (2.0 * intersection + smooth) / (union + smooth)
        
        return 1.0 - dice.mean()
    
    def forward(self, input: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算组合损失
        
        Args:
            input: 预测logits [N, C, H, W]
            target: 真实标签 [N, H, W]
        
        Returns:
            组合损失值
        """
        focal_loss = self.focal_loss(input, target)
        dice_loss = self.dice_loss(input, target)
        
        total_loss = self.focal_weight * focal_loss + self.dice_weight * dice_loss
        
        return total_loss


def create_focal_loss_for_bee_segmentation(num_classes: int = 8) -> FocalLoss:
    """
    为蜂巢8类分割任务创建优化的Focal Loss
    
    Args:
        num_classes: 类别数量，默认8
    
    Returns:
        配置好的FocalLoss实例
    """
    # 基于我们之前分析的类别分布，设置alpha权重
    # 数据较少的类别给更大权重
    if num_classes == 8:
        # 基于实际数据分布的权重设置
        alpha_weights = [
            0.5,   # background (较多，降低权重)
            2.0,   # eggs
            2.5,   # larvae (数据较少，增加权重)
            1.5,   # capped_brood
            5.0,   # pollen (数据最少，最大权重)
            2.0,   # nectar
            0.8,   # honey (较多，降低权重)
            1.8    # other
        ]
    else:
        # 默认均匀权重
        alpha_weights = [1.0] * num_classes
    
    return FocalLoss(
        alpha=alpha_weights,
        gamma=2.0,
        ignore_index=-100,
        reduction='mean'
    )


def create_combined_loss_for_bee_segmentation(num_classes: int = 8) -> CombinedLoss:
    """
    为蜂巢8类分割任务创建组合损失函数
    
    Args:
        num_classes: 类别数量，默认8
    
    Returns:
        配置好的CombinedLoss实例
    """
    # 基于类别分布设置alpha权重
    if num_classes == 8:
        alpha_weights = [
            0.5, 2.0, 2.5, 1.5, 5.0, 2.0, 0.8, 1.8
        ]
    else:
        alpha_weights = [1.0] * num_classes
    
    return CombinedLoss(
        focal_weight=1.0,
        dice_weight=1.0,
        focal_alpha=alpha_weights,
        focal_gamma=2.0,
        ignore_index=-100
    )


# 测试函数
def test_focal_loss():
    """测试Focal Loss实现"""
    print("🧪 测试Focal Loss实现...")
    
    # 创建测试数据
    batch_size, num_classes, height, width = 2, 8, 64, 64
    
    # 模拟预测logits
    input_tensor = torch.randn(batch_size, num_classes, height, width)
    
    # 模拟真实标签
    target_tensor = torch.randint(0, num_classes, (batch_size, height, width))
    
    # 测试基础Focal Loss
    focal_loss = create_focal_loss_for_bee_segmentation(num_classes)
    loss_value = focal_loss(input_tensor, target_tensor)
    
    print(f"✅ Focal Loss值: {loss_value.item():.4f}")
    
    # 测试组合损失
    combined_loss = create_combined_loss_for_bee_segmentation(num_classes)
    combined_value = combined_loss(input_tensor, target_tensor)
    
    print(f"✅ 组合损失值: {combined_value.item():.4f}")
    
    # 比较与CrossEntropyLoss
    ce_loss = nn.CrossEntropyLoss()
    ce_value = ce_loss(input_tensor, target_tensor)
    
    print(f"📊 CrossEntropyLoss值: {ce_value.item():.4f}")
    print(f"📊 Focal Loss相对变化: {((loss_value.item() - ce_value.item()) / ce_value.item() * 100):+.2f}%")
    
    print("✅ Focal Loss测试完成！")


if __name__ == "__main__":
    test_focal_loss() 