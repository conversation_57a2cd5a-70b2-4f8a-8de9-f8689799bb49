#!/usr/bin/env python3
"""
组合损失函数实现
包含多种损失函数的组合，如Dice+CE+Focal等
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Union, List

from .dice_loss import DiceLoss, IgnoreIndexDiceLoss
from .focal_loss import FocalLoss


class CombinedLoss(nn.Module):
    """组合损失函数：Dice + CrossEntropy + Focal"""
    
    def __init__(
        self,
        dice_weight: float = 0.5,
        ce_weight: float = 0.3,
        focal_weight: float = 0.2,
        focal_alpha: Union[float, List[float], None] = None,
        focal_gamma: float = 2.0,
        class_weights: Optional[torch.Tensor] = None,
        ignore_index: int = -100,
        use_ignore_index_dice: bool = True
    ):
        super().__init__()
        self.dice_weight = dice_weight
        self.ce_weight = ce_weight
        self.focal_weight = focal_weight
        
        # 选择Dice损失类型
        if use_ignore_index_dice:
            self.dice_loss = IgnoreIndexDiceLoss(ignore_index=ignore_index)
        else:
            self.dice_loss = DiceLoss(ignore_index=ignore_index)
        
        # Focal损失
        self.focal_loss = FocalLoss(
            alpha=focal_alpha,
            gamma=focal_gamma,
            ignore_index=ignore_index
        )
        
        # 交叉熵损失
        self.ce_loss = nn.CrossEntropyLoss(
            weight=class_weights,
            ignore_index=ignore_index
        )
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Args:
            pred: 预测结果 [B, C, H, W]
            target: 真实标签 [B, H, W]
        
        Returns:
            包含各个损失分量的字典
        """
        dice_loss = self.dice_loss(pred, target)
        ce_loss = self.ce_loss(pred, target)
        focal_loss = self.focal_loss(pred, target)
        
        # 组合损失
        total_loss = (
            self.dice_weight * dice_loss +
            self.ce_weight * ce_loss +
            self.focal_weight * focal_loss
        )
        
        return {
            'total_loss': total_loss,
            'dice_loss': dice_loss,
            'ce_loss': ce_loss,
            'focal_loss': focal_loss
        }


class BoundaryAwareLoss(nn.Module):
    """边界感知损失函数"""
    
    def __init__(
        self,
        base_loss_weight: float = 0.7,
        boundary_loss_weight: float = 0.3,
        boundary_kernel_size: int = 3,
        ignore_index: int = -100
    ):
        super().__init__()
        self.base_loss_weight = base_loss_weight
        self.boundary_loss_weight = boundary_loss_weight
        self.boundary_kernel_size = boundary_kernel_size
        self.ignore_index = ignore_index
        
        # 基础损失（组合损失）
        self.base_loss = CombinedLoss(ignore_index=ignore_index)
        
        # 边界检测损失
        self.boundary_loss = nn.BCEWithLogitsLoss(reduction='none')
    
    def extract_boundary(self, mask: torch.Tensor) -> torch.Tensor:
        """
        提取边界掩码
        
        Args:
            mask: 输入掩码 [B, H, W]
            
        Returns:
            torch.Tensor: 边界掩码 [B, H, W]
        """
        B, H, W = mask.shape
        device = mask.device
        
        # 转换为float并添加通道维度
        mask_float = mask.float().unsqueeze(1)  # [B, 1, H, W]
        
        # 使用max_pool2d模拟腐蚀操作
        # 腐蚀：min_pool = -max_pool(-x)
        eroded_mask = F.max_pool2d(
            -mask_float, 
            kernel_size=self.boundary_kernel_size, 
            stride=1, 
            padding=self.boundary_kernel_size // 2
        )
        eroded_mask = -eroded_mask  # 转回正值
        
        # 边界 = 原始掩码 - 腐蚀后掩码
        boundary = (mask_float - eroded_mask).clamp(0, 1)
        
        return boundary.squeeze(1)  # [B, H, W]
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Args:
            pred: 预测结果 [B, C, H, W]
            target: 真实标签 [B, H, W]
        
        Returns:
            包含各个损失分量的字典
        """
        # 基础损失
        base_losses = self.base_loss(pred, target)
        
        # 提取边界
        target_boundary = self.extract_boundary(target)  # [B, H, W]
        
        # 预测边界（使用预测的概率）
        pred_probs = F.softmax(pred, dim=1)
        pred_max_prob, pred_classes = torch.max(pred_probs, dim=1)  # [B, H, W]
        pred_boundary = self.extract_boundary(pred_classes.float())
        
        # 创建有效掩码
        if self.ignore_index >= 0:
            valid_mask = (target != self.ignore_index).float()
        else:
            valid_mask = torch.ones_like(target, dtype=torch.float)
        
        # 计算边界损失
        boundary_loss_map = self.boundary_loss(
            pred_boundary.unsqueeze(1),  # 添加通道维度用于BCEWithLogitsLoss
            target_boundary.unsqueeze(1)
        ).squeeze(1)  # 移除通道维度
        
        # 应用有效掩码
        boundary_loss_map = boundary_loss_map * valid_mask
        
        # 计算平均边界损失
        if valid_mask.sum() > 0:
            boundary_loss = boundary_loss_map.sum() / valid_mask.sum()
        else:
            boundary_loss = torch.tensor(0.0, device=pred.device, requires_grad=True)
        
        # 组合损失
        total_loss = (
            self.base_loss_weight * base_losses['total_loss'] +
            self.boundary_loss_weight * boundary_loss
        )
        
        # 返回所有损失分量
        result = base_losses.copy()
        result.update({
            'total_loss': total_loss,
            'boundary_loss': boundary_loss
        })
        
        return result


class WeightedCombinedLoss(nn.Module):
    """加权组合损失，支持动态权重调整"""
    
    def __init__(
        self,
        loss_weights: Dict[str, float],
        class_weights: Optional[torch.Tensor] = None,
        ignore_index: int = -100,
        adaptive_weights: bool = False
    ):
        super().__init__()
        self.loss_weights = loss_weights
        self.adaptive_weights = adaptive_weights
        self.ignore_index = ignore_index
        
        # 初始化各种损失函数
        self.losses = nn.ModuleDict()
        
        if 'dice' in loss_weights:
            self.losses['dice'] = IgnoreIndexDiceLoss(ignore_index=ignore_index)
        
        if 'ce' in loss_weights:
            self.losses['ce'] = nn.CrossEntropyLoss(
                weight=class_weights, ignore_index=ignore_index
            )
        
        if 'focal' in loss_weights:
            self.losses['focal'] = FocalLoss(ignore_index=ignore_index)
        
        if 'boundary' in loss_weights:
            self.losses['boundary'] = BoundaryAwareLoss(ignore_index=ignore_index)
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Args:
            pred: 预测结果 [B, C, H, W]
            target: 真实标签 [B, H, W]
        
        Returns:
            包含各个损失分量的字典
        """
        loss_values = {}
        total_loss = 0.0
        
        # 计算各个损失分量
        for loss_name, loss_fn in self.losses.items():
            if loss_name in self.loss_weights:
                if loss_name == 'boundary':
                    # 边界损失返回字典
                    boundary_losses = loss_fn(pred, target)
                    loss_values.update(boundary_losses)
                    loss_value = boundary_losses['total_loss']
                else:
                    loss_value = loss_fn(pred, target)
                    loss_values[f'{loss_name}_loss'] = loss_value
                
                # 加权累加
                weight = self.loss_weights[loss_name]
                total_loss += weight * loss_value
        
        loss_values['total_loss'] = total_loss
        return loss_values


def create_loss_function(
    loss_type: str = "combined",
    num_classes: int = 8,
    class_weights: Optional[torch.Tensor] = None,
    ignore_index: int = -100,
    **kwargs
) -> nn.Module:
    """
    创建损失函数
    
    Args:
        loss_type: 损失函数类型
        num_classes: 类别数量
        class_weights: 类别权重
        ignore_index: 忽略索引
        **kwargs: 其他参数
        
    Returns:
        nn.Module: 损失函数
    """
    if loss_type == "dice":
        return IgnoreIndexDiceLoss(ignore_index=ignore_index)
    
    elif loss_type == "focal":
        return FocalLoss(ignore_index=ignore_index, **kwargs)
    
    elif loss_type == "ce":
        return nn.CrossEntropyLoss(weight=class_weights, ignore_index=ignore_index)
    
    elif loss_type == "combined":
        return CombinedLoss(
            class_weights=class_weights,
            ignore_index=ignore_index,
            **kwargs
        )
    
    elif loss_type == "boundary":
        return BoundaryAwareLoss(ignore_index=ignore_index, **kwargs)
    
    elif loss_type == "weighted":
        return WeightedCombinedLoss(
            class_weights=class_weights,
            ignore_index=ignore_index,
            **kwargs
        )
    
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")
