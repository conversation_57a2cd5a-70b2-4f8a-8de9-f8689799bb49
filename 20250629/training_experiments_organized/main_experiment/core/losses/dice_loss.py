#!/usr/bin/env python3
"""
Dice损失函数实现
包含标准Dice损失和支持ignore_index的Dice损失
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional


class DiceLoss(nn.Module):
    """标准Dice损失函数"""
    
    def __init__(self, smooth: float = 1e-6, ignore_index: int = -100):
        super().__init__()
        self.smooth = smooth
        self.ignore_index = ignore_index
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred: 预测结果 [B, C, H, W]
            target: 真实标签 [B, H, W]
        """
        # 应用softmax获取概率
        pred = F.softmax(pred, dim=1)
        
        # 创建one-hot编码
        num_classes = pred.shape[1]
        target_one_hot = F.one_hot(target, num_classes=num_classes).permute(0, 3, 1, 2).float()
        
        # 忽略指定索引
        if self.ignore_index >= 0:
            mask = (target != self.ignore_index).float().unsqueeze(1)
            pred = pred * mask
            target_one_hot = target_one_hot * mask
        
        # 计算Dice系数
        intersection = (pred * target_one_hot).sum(dim=(2, 3))
        union = pred.sum(dim=(2, 3)) + target_one_hot.sum(dim=(2, 3))
        
        dice = (2.0 * intersection + self.smooth) / (union + self.smooth)
        
        # 返回Dice损失（1 - Dice系数）
        return 1.0 - dice.mean()


class IgnoreIndexDiceLoss(nn.Module):
    """支持ignore_index的改进Dice损失"""
    
    def __init__(self, smooth: float = 1e-6, ignore_index: int = -100):
        super().__init__()
        self.smooth = smooth
        self.ignore_index = ignore_index
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred: 预测logits [B, C, H, W]
            target: 真实标签 [B, H, W]
        """
        # 应用softmax获取概率
        pred_probs = F.softmax(pred, dim=1)
        
        B, C, H, W = pred.shape
        
        # 创建有效掩码
        if self.ignore_index >= 0:
            valid_mask = (target != self.ignore_index)
        else:
            valid_mask = torch.ones_like(target, dtype=torch.bool)
        
        # 如果没有有效像素，返回零损失
        if not valid_mask.any():
            return torch.tensor(0.0, device=pred.device, requires_grad=True)
        
        # 只在有效区域计算损失
        total_dice_loss = 0.0
        valid_classes = 0
        
        for class_idx in range(C):
            # 获取当前类别的预测和真实标签
            pred_class = pred_probs[:, class_idx, :, :]  # [B, H, W]
            target_class = (target == class_idx).float()  # [B, H, W]
            
            # 应用有效掩码
            pred_class_valid = pred_class[valid_mask]
            target_class_valid = target_class[valid_mask]
            
            # 如果当前类别在有效区域内没有像素，跳过
            if len(pred_class_valid) == 0:
                continue
            
            # 计算Dice系数
            intersection = (pred_class_valid * target_class_valid).sum()
            union = pred_class_valid.sum() + target_class_valid.sum()
            
            dice = (2.0 * intersection + self.smooth) / (union + self.smooth)
            total_dice_loss += (1.0 - dice)
            valid_classes += 1
        
        # 返回平均Dice损失
        if valid_classes > 0:
            return total_dice_loss / valid_classes
        else:
            return torch.tensor(0.0, device=pred.device, requires_grad=True)


class MultiClassDiceLoss(nn.Module):
    """多类别Dice损失，支持类别权重"""
    
    def __init__(
        self, 
        smooth: float = 1e-6, 
        ignore_index: int = -100,
        class_weights: Optional[torch.Tensor] = None,
        reduction: str = 'mean'
    ):
        super().__init__()
        self.smooth = smooth
        self.ignore_index = ignore_index
        self.class_weights = class_weights
        self.reduction = reduction
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred: 预测logits [B, C, H, W]
            target: 真实标签 [B, H, W]
        """
        # 应用softmax获取概率
        pred_probs = F.softmax(pred, dim=1)
        
        B, C, H, W = pred.shape
        
        # 创建有效掩码
        if self.ignore_index >= 0:
            valid_mask = (target != self.ignore_index)
        else:
            valid_mask = torch.ones_like(target, dtype=torch.bool)
        
        # 如果没有有效像素，返回零损失
        if not valid_mask.any():
            return torch.tensor(0.0, device=pred.device, requires_grad=True)
        
        # 计算每个类别的Dice损失
        dice_losses = []
        
        for class_idx in range(C):
            # 获取当前类别的预测和真实标签
            pred_class = pred_probs[:, class_idx, :, :]
            target_class = (target == class_idx).float()
            
            # 应用有效掩码
            pred_class_masked = pred_class * valid_mask.float()
            target_class_masked = target_class * valid_mask.float()
            
            # 计算Dice系数
            intersection = (pred_class_masked * target_class_masked).sum()
            union = pred_class_masked.sum() + target_class_masked.sum()
            
            dice = (2.0 * intersection + self.smooth) / (union + self.smooth)
            dice_loss = 1.0 - dice
            
            dice_losses.append(dice_loss)
        
        # 转换为张量
        dice_losses = torch.stack(dice_losses)
        
        # 应用类别权重
        if self.class_weights is not None:
            if self.class_weights.device != dice_losses.device:
                self.class_weights = self.class_weights.to(dice_losses.device)
            dice_losses = dice_losses * self.class_weights
        
        # 应用reduction
        if self.reduction == 'mean':
            return dice_losses.mean()
        elif self.reduction == 'sum':
            return dice_losses.sum()
        elif self.reduction == 'none':
            return dice_losses
        else:
            raise ValueError(f"Invalid reduction mode: {self.reduction}")


class BinaryDiceLoss(nn.Module):
    """二值分割专用Dice损失"""
    
    def __init__(self, smooth: float = 1e-6):
        super().__init__()
        self.smooth = smooth
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred: 预测logits [B, 1, H, W] 或 [B, H, W]
            target: 真实标签 [B, H, W]，值为0或1
        """
        # 确保预测为概率
        if pred.dim() == 4 and pred.shape[1] == 1:
            pred = torch.sigmoid(pred.squeeze(1))
        elif pred.dim() == 4 and pred.shape[1] == 2:
            pred = F.softmax(pred, dim=1)[:, 1, :, :]
        else:
            pred = torch.sigmoid(pred)
        
        # 确保target为float
        target = target.float()
        
        # 计算Dice系数
        intersection = (pred * target).sum(dim=(1, 2))
        union = pred.sum(dim=(1, 2)) + target.sum(dim=(1, 2))
        
        dice = (2.0 * intersection + self.smooth) / (union + self.smooth)
        
        # 返回Dice损失
        return 1.0 - dice.mean()


def create_dice_loss(
    loss_type: str = "standard",
    smooth: float = 1e-6,
    ignore_index: int = -100,
    class_weights: Optional[torch.Tensor] = None,
    **kwargs
) -> nn.Module:
    """
    创建Dice损失函数
    
    Args:
        loss_type: 损失类型 ('standard', 'ignore_index', 'multiclass', 'binary')
        smooth: 平滑因子
        ignore_index: 忽略的索引
        class_weights: 类别权重
        **kwargs: 其他参数
        
    Returns:
        nn.Module: Dice损失函数
    """
    if loss_type == "standard":
        return DiceLoss(smooth=smooth, ignore_index=ignore_index)
    elif loss_type == "ignore_index":
        return IgnoreIndexDiceLoss(smooth=smooth, ignore_index=ignore_index)
    elif loss_type == "multiclass":
        return MultiClassDiceLoss(
            smooth=smooth, 
            ignore_index=ignore_index,
            class_weights=class_weights,
            **kwargs
        )
    elif loss_type == "binary":
        return BinaryDiceLoss(smooth=smooth)
    else:
        raise ValueError(f"Unknown dice loss type: {loss_type}")
