#!/usr/bin/env python3
"""
基础配置管理系统
支持配置文件加载、验证和保存
"""

import os
import json
import yaml
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List, Union
from pathlib import Path

from .model_config import ModelConfig
from .data_config import DataConfig


@dataclass
class PseudoLabelConfig:
    """伪标签配置"""
    # 伪标签开关
    enable_pseudo_labeling: bool = False

    # 伪标签策略
    pseudo_label_strategy: str = "confidence_threshold"  # confidence_threshold, entropy_threshold, top_k
    confidence_threshold: float = 0.9  # 置信度阈值
    entropy_threshold: float = 0.5  # 熵阈值
    top_k_ratio: float = 0.3  # top-k策略的比例

    # 伪标签数据
    unlabeled_data_dir: str = ""  # 无标签数据目录
    pseudo_label_batch_ratio: float = 0.5  # 伪标签数据在batch中的比例

    # 伪标签训练策略
    start_epoch: int = 10  # 开始使用伪标签的epoch
    update_frequency: int = 5  # 伪标签更新频率（每N个epoch）
    pseudo_label_weight: float = 0.5  # 伪标签损失权重

    # 伪标签质量控制
    min_pseudo_samples: int = 100  # 最少伪标签样本数
    max_pseudo_samples: int = 10000  # 最多伪标签样本数
    quality_check_interval: int = 10  # 质量检查间隔

    # 伪标签存储
    save_pseudo_labels: bool = True  # 是否保存生成的伪标签
    pseudo_label_save_dir: str = "./pseudo_labels"  # 伪标签保存目录


@dataclass
class TrainingConfig:
    """训练配置"""
    # 基础训练参数
    epochs: int = 100
    learning_rate: float = 1e-4
    weight_decay: float = 1e-4
    warmup_epochs: int = 5

    # 优化器配置
    optimizer: str = "adamw"  # adamw, sgd
    momentum: float = 0.9  # for SGD
    betas: List[float] = None  # for AdamW

    # 学习率调度
    lr_scheduler: str = "cosine_warmup"  # cosine_warmup, cosine_warmup_restarts, cosine, step, plateau
    lr_step_size: int = 30  # for step scheduler
    lr_gamma: float = 0.1  # for step scheduler
    lr_patience: int = 10  # for plateau scheduler

    # 损失函数
    loss_function: str = "combined"  # ce, dice, focal, combined, focal_bee
    dice_weight: float = 0.5
    ce_weight: float = 0.5
    focal_weight: float = 0.3  # 组合损失中focal loss的权重
    focal_alpha: Union[float, str] = 0.25  # focal loss alpha参数，可以是数值或"auto"
    focal_gamma: float = 2.0

    # 训练策略
    gradient_clip_norm: float = 1.0
    mixed_precision: bool = True
    accumulate_grad_batches: int = 1

    # 伪标签配置
    pseudo_label: PseudoLabelConfig = None
    
    def __post_init__(self):
        if self.betas is None:
            self.betas = [0.9, 0.999]
        if self.pseudo_label is None:
            self.pseudo_label = PseudoLabelConfig()
        elif isinstance(self.pseudo_label, dict):
            self.pseudo_label = PseudoLabelConfig(**self.pseudo_label)


@dataclass
class SystemConfig:
    """系统配置"""
    # 设备配置
    device: str = "auto"  # auto, cpu, cuda, cuda:0
    num_gpus: int = 1
    
    # 输出配置
    output_dir: str = "./outputs"
    experiment_name: str = "honeycomb_segmentation"
    save_top_k: int = 3
    save_every_n_epochs: int = 10
    
    # 日志配置
    log_every_n_steps: int = 10
    val_every_n_epochs: int = 1
    
    # 调试配置
    debug_mode: bool = False
    overfit_batches: int = 0  # 0表示不使用
    fast_dev_run: bool = False


@dataclass
class BaseConfig:
    """完整配置基类"""
    model: ModelConfig
    data: DataConfig
    training: TrainingConfig
    system: SystemConfig
    
    @classmethod
    def from_dict(cls, config_dict: dict) -> 'BaseConfig':
        """从字典创建配置"""
        return cls(
            model=ModelConfig(**config_dict.get('model', {})),
            data=DataConfig(**config_dict.get('data', {})),
            training=TrainingConfig(**config_dict.get('training', {})),
            system=SystemConfig(**config_dict.get('system', {}))
        )

    @classmethod
    def from_yaml(cls, config_path: str) -> 'BaseConfig':
        """从YAML文件加载配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)

        return cls.from_dict(config_dict)
    
    @classmethod
    def from_json(cls, config_path: str) -> 'BaseConfig':
        """从JSON文件加载配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)

        return cls.from_dict(config_dict)
    
    def to_yaml(self, config_path: str):
        """保存配置到YAML文件"""
        config_dict = {
            'model': asdict(self.model),
            'data': asdict(self.data),
            'training': asdict(self.training),
            'system': asdict(self.system)
        }

        # 只有当路径包含目录时才创建目录
        dir_path = os.path.dirname(config_path)
        if dir_path:
            os.makedirs(dir_path, exist_ok=True)

        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
    
    def to_json(self, config_path: str):
        """保存配置到JSON文件"""
        config_dict = {
            'model': asdict(self.model),
            'data': asdict(self.data),
            'training': asdict(self.training),
            'system': asdict(self.system)
        }
        
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    def validate(self) -> List[str]:
        """验证配置的有效性"""
        errors = []
        
        # 验证模型配置
        if self.model.image_size <= 0:
            errors.append("image_size must be positive")
        if self.model.patch_size <= 0 or self.model.image_size % self.model.patch_size != 0:
            errors.append("patch_size must be positive and divide image_size")
        if self.model.num_classes <= 0:
            errors.append("num_classes must be positive")
        
        # 验证数据配置
        if self.data.batch_size <= 0:
            errors.append("batch_size must be positive")
        if not os.path.exists(self.data.data_root):
            errors.append(f"data_root does not exist: {self.data.data_root}")
        
        # 验证训练配置
        if self.training.epochs <= 0:
            errors.append("epochs must be positive")
        if self.training.learning_rate <= 0:
            errors.append("learning_rate must be positive")
        
        return errors
    
    def get_device(self):
        """获取训练设备"""
        import torch
        
        if self.system.device == "auto":
            return torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            return torch.device(self.system.device)


def create_default_config() -> BaseConfig:
    """创建默认配置"""
    return BaseConfig(
        model=ModelConfig(),
        data=DataConfig(),
        training=TrainingConfig(),
        system=SystemConfig()
    )


def load_config(config_path: str) -> BaseConfig:
    """加载配置文件"""
    config_path = Path(config_path)
    
    if not config_path.exists():
        print(f"配置文件不存在: {config_path}，使用默认配置")
        return create_default_config()
    
    if config_path.suffix.lower() == '.yaml' or config_path.suffix.lower() == '.yml':
        return BaseConfig.from_yaml(str(config_path))
    elif config_path.suffix.lower() == '.json':
        return BaseConfig.from_json(str(config_path))
    else:
        raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
