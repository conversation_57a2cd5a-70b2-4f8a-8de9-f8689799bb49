#!/usr/bin/env python3
"""
模型配置管理
包含ViT、UPerNet和MAE相关的所有模型配置
"""

from dataclasses import dataclass
from typing import List, Optional


@dataclass
class ModelConfig:
    """模型配置"""
    # ViT配置
    image_size: int = 224
    patch_size: int = 16
    embed_dim: int = 768
    depth: int = 12
    num_heads: int = 12
    mlp_ratio: float = 4.0
    
    # UPerNet配置
    num_classes: int = 8  # 完整8类蜂巢分割：背景+卵+幼虫+封盖子+花粉+花蜜+蜂蜜+其他
    upernet_in_channels: List[int] = None
    upernet_pool_scales: List[int] = None
    upernet_channels: int = 512
    
    # MAE预训练
    mae_checkpoint_path: str = "mae_pretrain_vit_base.pth"
    use_pretrained: bool = True
    
    # 模型架构选择
    backbone_type: str = "vit_base"  # vit_base, vit_large, vit_huge
    segmentation_head: str = "upernet"  # upernet, fcn, deeplabv3
    
    # 特征提取配置
    feature_layers: List[int] = None  # 用于分割的特征层，默认[3, 6, 9, 12]
    feature_dim: int = 768  # 特征维度
    
    # 模型优化配置
    dropout_rate: float = 0.1
    drop_path_rate: float = 0.1
    use_checkpoint: bool = False  # 是否使用gradient checkpointing节省显存
    
    def __post_init__(self):
        """初始化后处理"""
        if self.upernet_in_channels is None:
            self.upernet_in_channels = [768, 768, 768, 768]  # ViT各层特征维度
        if self.upernet_pool_scales is None:
            self.upernet_pool_scales = [1, 2, 3, 6]
        if self.feature_layers is None:
            self.feature_layers = [3, 6, 9, 12]  # 默认使用的特征层
    
    def get_model_size(self) -> str:
        """根据embed_dim获取模型大小"""
        if self.embed_dim == 768:
            return "base"
        elif self.embed_dim == 1024:
            return "large"
        elif self.embed_dim == 1280:
            return "huge"
        else:
            return "custom"
    
    def get_num_patches(self) -> int:
        """计算patch数量"""
        return (self.image_size // self.patch_size) ** 2
    
    def validate(self) -> List[str]:
        """验证模型配置"""
        errors = []
        
        # 验证基础参数
        if self.image_size <= 0:
            errors.append("image_size must be positive")
        if self.patch_size <= 0:
            errors.append("patch_size must be positive")
        if self.image_size % self.patch_size != 0:
            errors.append("image_size must be divisible by patch_size")
        
        # 验证ViT参数
        if self.embed_dim <= 0:
            errors.append("embed_dim must be positive")
        if self.depth <= 0:
            errors.append("depth must be positive")
        if self.num_heads <= 0:
            errors.append("num_heads must be positive")
        if self.embed_dim % self.num_heads != 0:
            errors.append("embed_dim must be divisible by num_heads")
        
        # 验证分割参数
        if self.num_classes <= 0:
            errors.append("num_classes must be positive")
        if len(self.upernet_in_channels) != len(self.feature_layers):
            errors.append("upernet_in_channels length must match feature_layers length")
        
        # 验证dropout参数
        if not 0 <= self.dropout_rate <= 1:
            errors.append("dropout_rate must be between 0 and 1")
        if not 0 <= self.drop_path_rate <= 1:
            errors.append("drop_path_rate must be between 0 and 1")
        
        return errors


# 预定义的模型配置
def get_vit_base_config() -> ModelConfig:
    """获取ViT-Base配置"""
    return ModelConfig(
        image_size=224,
        patch_size=16,
        embed_dim=768,
        depth=12,
        num_heads=12,
        mlp_ratio=4.0,
        backbone_type="vit_base"
    )


def get_vit_large_config() -> ModelConfig:
    """获取ViT-Large配置"""
    return ModelConfig(
        image_size=224,
        patch_size=16,
        embed_dim=1024,
        depth=24,
        num_heads=16,
        mlp_ratio=4.0,
        backbone_type="vit_large",
        upernet_in_channels=[1024, 1024, 1024, 1024]
    )


def get_vit_huge_config() -> ModelConfig:
    """获取ViT-Huge配置"""
    return ModelConfig(
        image_size=224,
        patch_size=14,
        embed_dim=1280,
        depth=32,
        num_heads=16,
        mlp_ratio=4.0,
        backbone_type="vit_huge",
        upernet_in_channels=[1280, 1280, 1280, 1280]
    )
