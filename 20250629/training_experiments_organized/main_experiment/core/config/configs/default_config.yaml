# 蜂巢图像分割训练配置文件
# 基于MAE+ViT+UPerNet架构

model:
  # ViT基础配置
  image_size: 512  # 匹配生成的512x512图块
  patch_size: 16
  embed_dim: 768
  depth: 12
  num_heads: 12
  mlp_ratio: 4.0
  
  # 分割头配置
  num_classes: 8  # 完整8类：background + eggs + larvae + capped_brood + pollen + nectar + honey + other
  upernet_in_channels: [768, 768, 768, 768]
  upernet_pool_scales: [1, 2, 3, 6]
  upernet_channels: 512
  
  # 预训练模型
  mae_checkpoint_path: "/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/checkpoint-0315.pth"
  use_pretrained: true

data:
  # 数据路径 - 指向重新划分的正确8类数据 ✅
  data_root: "/home/<USER>/PycharmProjects/20250629/training"
  train_images_dir: "data_8classes/train/images"  # 使用正确的8类训练数据 
  train_masks_dir: "data_8classes/train/masks"    # 使用正确的8类掩码数据
  val_images_dir: "data_8classes/val/images"      # 使用正确的8类验证数据
  val_masks_dir: "data_8classes/val/masks"        # 使用正确的8类验证掩码
  
  # 图像预处理
  image_size: 512  # 匹配生成的512x512图块
  normalize_mean: [0.485, 0.456, 0.406]
  normalize_std: [0.229, 0.224, 0.225]
  
  # 数据增强
  use_augmentation: true
  horizontal_flip_prob: 0.5
  vertical_flip_prob: 0.5
  rotation_degrees: 15
  color_jitter_brightness: 0.2
  color_jitter_contrast: 0.2
  
  # 数据加载
  batch_size: 4  # 减少batch size以适应512x512大尺寸
  num_workers: 10
  pin_memory: true

training:
  # 基础训练参数
  epochs: 300
  learning_rate: 0.0004
  weight_decay: 0.0001
  warmup_epochs: 10
  
  # 优化器
  optimizer: "adamw"
  momentum: 0.9
  betas: [0.9, 0.999]
  
  # 学习率调度
  lr_scheduler: "cosine_warmup"  # 推荐：cosine_warmup
  lr_step_size: 30
  lr_gamma: 0.1
  lr_patience: 10
  
  # 损失函数配置
  loss_function: "focal_bee"  # 🎯 使用专门为蜂巢分割优化的Focal Loss
  # 可选的损失函数类型:
  # - "ce": 标准交叉熵损失
  # - "dice": Dice损失  
  # - "focal": 标准Focal Loss
  # - "focal_bee": 蜂巢优化Focal Loss (推荐) ✅
  # - "combined": 组合损失 (Dice + CE)
  # - "combined_bee": 蜂巢优化组合损失
  
  # 损失函数权重 (针对组合损失)
  dice_weight: 0.4
  ce_weight: 0.3
  focal_weight: 0.3
  
  # Focal Loss参数
  focal_alpha: "auto"  # 自动为8类蜂巢分割设置权重
  focal_gamma: 2.0     # 聚焦参数，控制困难样本权重
  
  # 训练策略
  gradient_clip_norm: 1.0
  mixed_precision: true
  accumulate_grad_batches: 8

system:
  # 设备配置
  device: "auto"  # 自动检测GPU
  num_gpus: 1
  
  # 输出配置
  output_dir: "./outputs"
  experiment_name: "honeycomb_segmentation"
  save_top_k: 3
  save_every_n_epochs: 10
  
  # 监控配置
  log_every_n_steps: 10
  val_every_n_epochs: 1
  
  # 调试配置
  debug_mode: false
  overfit_batches: 0
  fast_dev_run: false