# 带伪标签功能的训练配置示例
# 这个配置展示了如何启用和配置伪标签功能进行对比实验

model:
  architecture: "mae_vit_upernet"
  backbone: "vit_base_patch16_224"
  num_classes: 3
  pretrained_mae_path: "experiments/phase6_mae_vit_base/outputs/checkpoint-0315.pth"
  freeze_backbone: false
  dropout_rate: 0.1

data:
  data_root: "data"
  train_images_dir: "train/images"
  train_masks_dir: "train/masks"
  val_images_dir: "val/images"
  val_masks_dir: "val/masks"
  
  image_size: 224
  batch_size: 16
  num_workers: 4
  pin_memory: true
  
  # 数据增强
  horizontal_flip_prob: 0.5
  vertical_flip_prob: 0.2
  rotation_degrees: 15
  color_jitter_brightness: 0.2
  color_jitter_contrast: 0.2
  
  # 归一化参数
  normalize_mean: [0.485, 0.456, 0.406]
  normalize_std: [0.229, 0.224, 0.225]

training:
  # 基础训练参数
  epochs: 100
  learning_rate: 1e-4
  weight_decay: 1e-4
  warmup_epochs: 5
  
  # 优化器配置
  optimizer: "adamw"
  betas: [0.9, 0.999]
  
  # 学习率调度
  lr_scheduler: "cosine_warmup"
  
  # 损失函数
  loss_function: "combined"
  dice_weight: 0.5
  ce_weight: 0.5
  
  # 训练策略
  gradient_clip_norm: 1.0
  mixed_precision: true
  accumulate_grad_batches: 1
  
  # 伪标签配置
  pseudo_label:
    # 🔧 主开关：控制是否启用伪标签功能
    enable_pseudo_labeling: true  # 设为 false 可关闭伪标签功能进行对比实验
    
    # 伪标签策略
    pseudo_label_strategy: "confidence_threshold"  # confidence_threshold, entropy_threshold, top_k
    confidence_threshold: 0.9  # 置信度阈值（越高越严格）
    entropy_threshold: 0.5     # 熵阈值（越低越严格）
    top_k_ratio: 0.3          # top-k策略的比例
    
    # 伪标签数据源
    unlabeled_data_dir: "data/unlabeled_images"  # 无标签数据目录
    pseudo_label_batch_ratio: 0.5  # 伪标签数据在batch中的比例
    
    # 伪标签训练策略
    start_epoch: 10           # 从第10个epoch开始使用伪标签
    update_frequency: 5       # 每5个epoch更新一次伪标签
    pseudo_label_weight: 0.5  # 伪标签损失权重（相对于有标签数据）
    
    # 伪标签质量控制
    min_pseudo_samples: 100    # 最少伪标签样本数
    max_pseudo_samples: 10000  # 最多伪标签样本数
    quality_check_interval: 10 # 质量检查间隔
    
    # 伪标签存储
    save_pseudo_labels: true
    pseudo_label_save_dir: "./pseudo_labels"

system:
  device: "auto"  # auto, cpu, cuda, cuda:0
  output_dir: "outputs"
  experiment_name: "honeycomb_segmentation_with_pseudo_labels"
  
  # 日志和保存
  log_every_n_steps: 50
  save_every_n_epochs: 10
  keep_last_n_checkpoints: 5
  
  # 监控
  use_tensorboard: true
  use_wandb: false
  wandb_project: "honeycomb_segmentation"
  wandb_entity: ""

# 对比实验配置说明：
# 
# 1. 启用伪标签的实验：
#    - enable_pseudo_labeling: true
#    - experiment_name: "honeycomb_segmentation_with_pseudo_labels"
#
# 2. 不使用伪标签的基线实验：
#    - enable_pseudo_labeling: false
#    - experiment_name: "honeycomb_segmentation_baseline"
#
# 3. 不同伪标签策略的对比：
#    - confidence_threshold: 调整置信度阈值 (0.8, 0.9, 0.95)
#    - pseudo_label_strategy: 尝试不同策略
#    - pseudo_label_weight: 调整伪标签权重 (0.3, 0.5, 0.7)
#
# 4. 不同启动时机的对比：
#    - start_epoch: 尝试不同的启动epoch (5, 10, 20)
#    - update_frequency: 尝试不同的更新频率 (3, 5, 10)
