# 快速重训练配置 - 验证权重修复效果
# 基于默认配置，但只训练10个epoch

model:
  image_size: 512
  patch_size: 16
  embed_dim: 768
  depth: 12
  num_heads: 12
  mlp_ratio: 4.0
  num_classes: 8
  upernet_in_channels: [768, 768, 768, 768]
  upernet_pool_scales: [1, 2, 3, 6]
  upernet_channels: 512
  mae_checkpoint_path: "/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/checkpoint-0315.pth"
  use_pretrained: true

data:
  data_root: "/home/<USER>/PycharmProjects/20250629/training"
  train_images_dir: "data_8classes/train/images"
  train_masks_dir: "data_8classes/train/masks"
  val_images_dir: "data_8classes/val/images"
  val_masks_dir: "data_8classes/val/masks"
  image_size: 512
  normalize_mean: [0.485, 0.456, 0.406]
  normalize_std: [0.229, 0.224, 0.225]
  use_augmentation: true
  horizontal_flip_prob: 0.5
  vertical_flip_prob: 0.5
  rotation_degrees: 15
  color_jitter_brightness: 0.2
  color_jitter_contrast: 0.2
  batch_size: 4
  num_workers: 10
  pin_memory: true

training:
  epochs: 10
  learning_rate: 0.0004
  weight_decay: 0.0001
  warmup_epochs: 2
  optimizer: "adamw"
  momentum: 0.9
  betas: [0.9, 0.999]
  lr_scheduler: "cosine_warmup"
  lr_step_size: 30
  lr_gamma: 0.1
  lr_patience: 10
  loss_function: "focal_bee"
  focal_alpha: "auto"
  focal_gamma: 2.0
  dice_weight: 0.4
  ce_weight: 0.3
  focal_weight: 0.3
  gradient_clip_norm: 1.0
  mixed_precision: true
  accumulate_grad_batches: 8

system:
  device: "auto"
  num_gpus: 1
  output_dir: "./outputs_fixed"
  experiment_name: "honeycomb_segmentation_fixed"
  save_top_k: 3
  save_every_n_epochs: 5
  log_every_n_steps: 10
  val_every_n_epochs: 1
  debug_mode: false
  overfit_batches: 0
  fast_dev_run: false
