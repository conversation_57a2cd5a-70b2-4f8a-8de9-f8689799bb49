#!/usr/bin/env python3
"""
数据配置管理
包含数据路径、预处理、增强和加载相关的所有配置
"""

import os
from dataclasses import dataclass
from typing import List, Dict, Any, Optional


@dataclass
class DataConfig:
    """数据配置"""
    # 数据路径
    data_root: str = "./data"
    train_images_dir: str = "train/images"
    train_masks_dir: str = "train/masks"
    val_images_dir: str = "val/images"
    val_masks_dir: str = "val/masks"
    test_images_dir: str = "test/images"
    test_masks_dir: str = "test/masks"
    
    # 数据预处理
    image_size: int = 224
    normalize_mean: List[float] = None
    normalize_std: List[float] = None
    
    # 数据增强配置
    use_augmentation: bool = True
    augmentation_config: Dict[str, Any] = None
    
    # 基础数据增强参数
    horizontal_flip_prob: float = 0.5
    vertical_flip_prob: float = 0.5
    rotation_degrees: int = 15
    color_jitter_brightness: float = 0.2
    color_jitter_contrast: float = 0.2
    color_jitter_saturation: float = 0.2
    color_jitter_hue: float = 0.1
    
    # 高级数据增强参数
    gaussian_blur_prob: float = 0.2
    gaussian_noise_prob: float = 0.2
    elastic_transform_prob: float = 0.2
    grid_distortion_prob: float = 0.2
    
    # 数据加载
    batch_size: int = 8
    num_workers: int = 4
    pin_memory: bool = True
    shuffle_train: bool = True
    drop_last: bool = True
    
    # 数据集分割
    train_split: float = 0.7
    val_split: float = 0.2
    test_split: float = 0.1
    random_seed: int = 42
    
    # 类别配置
    class_names: List[str] = None
    class_weights: List[float] = None
    ignore_index: int = -100
    
    def __post_init__(self):
        """初始化后处理"""
        if self.normalize_mean is None:
            self.normalize_mean = [0.485, 0.456, 0.406]  # ImageNet标准
        if self.normalize_std is None:
            self.normalize_std = [0.229, 0.224, 0.225]
        
        if self.class_names is None:
            self.class_names = [
                "background", "egg", "larva", "capped_brood", 
                "pollen", "nectar", "honey", "other"
            ]
        
        if self.augmentation_config is None:
            self.augmentation_config = self._get_default_augmentation_config()
    
    def _get_default_augmentation_config(self) -> Dict[str, Any]:
        """获取默认数据增强配置"""
        return {
            "horizontal_flip": {"p": self.horizontal_flip_prob},
            "vertical_flip": {"p": self.vertical_flip_prob},
            "rotate": {"limit": self.rotation_degrees, "p": 0.5},
            "color_jitter": {
                "brightness": self.color_jitter_brightness,
                "contrast": self.color_jitter_contrast,
                "saturation": self.color_jitter_saturation,
                "hue": self.color_jitter_hue,
                "p": 0.5
            },
            "gaussian_blur": {"blur_limit": (3, 7), "p": self.gaussian_blur_prob},
            "gaussian_noise": {"var_limit": (10, 50), "p": self.gaussian_noise_prob},
            "elastic_transform": {
                "alpha": 1, "sigma": 50, "alpha_affine": 50, 
                "p": self.elastic_transform_prob
            },
            "grid_distortion": {"p": self.grid_distortion_prob},
            "normalize": {
                "mean": self.normalize_mean,
                "std": self.normalize_std
            }
        }
    
    def get_train_data_path(self) -> tuple:
        """获取训练数据路径"""
        images_path = os.path.join(self.data_root, self.train_images_dir)
        masks_path = os.path.join(self.data_root, self.train_masks_dir)
        return images_path, masks_path
    
    def get_val_data_path(self) -> tuple:
        """获取验证数据路径"""
        images_path = os.path.join(self.data_root, self.val_images_dir)
        masks_path = os.path.join(self.data_root, self.val_masks_dir)
        return images_path, masks_path
    
    def get_test_data_path(self) -> tuple:
        """获取测试数据路径"""
        images_path = os.path.join(self.data_root, self.test_images_dir)
        masks_path = os.path.join(self.data_root, self.test_masks_dir)
        return images_path, masks_path
    
    def validate(self) -> List[str]:
        """验证数据配置"""
        errors = []
        
        # 验证基础参数
        if self.image_size <= 0:
            errors.append("image_size must be positive")
        if self.batch_size <= 0:
            errors.append("batch_size must be positive")
        if self.num_workers < 0:
            errors.append("num_workers must be non-negative")
        
        # 验证数据路径
        if not os.path.exists(self.data_root):
            errors.append(f"data_root does not exist: {self.data_root}")
        
        # 验证数据分割比例
        total_split = self.train_split + self.val_split + self.test_split
        if abs(total_split - 1.0) > 1e-6:
            errors.append(f"train_split + val_split + test_split must equal 1.0, got {total_split}")
        
        # 验证归一化参数
        if len(self.normalize_mean) != 3:
            errors.append("normalize_mean must have 3 values for RGB channels")
        if len(self.normalize_std) != 3:
            errors.append("normalize_std must have 3 values for RGB channels")
        
        # 验证概率参数
        prob_params = [
            ("horizontal_flip_prob", self.horizontal_flip_prob),
            ("vertical_flip_prob", self.vertical_flip_prob),
            ("gaussian_blur_prob", self.gaussian_blur_prob),
            ("gaussian_noise_prob", self.gaussian_noise_prob),
            ("elastic_transform_prob", self.elastic_transform_prob),
            ("grid_distortion_prob", self.grid_distortion_prob)
        ]
        
        for param_name, param_value in prob_params:
            if not 0 <= param_value <= 1:
                errors.append(f"{param_name} must be between 0 and 1")
        
        return errors
    
    def get_num_classes(self) -> int:
        """获取类别数量"""
        return len(self.class_names)
    
    def get_class_to_idx(self) -> Dict[str, int]:
        """获取类别名到索引的映射"""
        return {name: idx for idx, name in enumerate(self.class_names)}
    
    def get_idx_to_class(self) -> Dict[int, str]:
        """获取索引到类别名的映射"""
        return {idx: name for idx, name in enumerate(self.class_names)}


# 预定义的数据配置
def get_honeycomb_8class_config() -> DataConfig:
    """获取8类蜂巢分割数据配置"""
    return DataConfig(
        data_root="./data/8classes",
        class_names=[
            "background", "egg", "larva", "capped_brood", 
            "pollen", "nectar", "honey", "other"
        ]
    )


def get_honeycomb_binary_config() -> DataConfig:
    """获取二值蜂巢分割数据配置"""
    return DataConfig(
        data_root="./data/binary",
        class_names=["background", "honeycomb"]
    )


def get_debug_config() -> DataConfig:
    """获取调试用的小数据集配置"""
    config = get_honeycomb_8class_config()
    config.batch_size = 2
    config.num_workers = 0
    config.use_augmentation = False
    return config
