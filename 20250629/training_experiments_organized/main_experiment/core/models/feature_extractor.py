#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定制化ViT特征提取器
基于深度特征分析结果，专门设计用于提取MAE预训练ViT的多层特征
重点利用Layer 3, 6, 9, 12的特征组合，为UPerNet分割头提供最优特征

设计理念：
- ViT-Native设计：不强行模拟ResNet特征，而是设计专门适配ViT特征的处理流程
- 多层特征利用：充分利用ViT不同层的特征表示能力
- 空间信息恢复：通过渐进式上采样恢复空间细节
- MAE预训练优势最大化：保持预训练特征的完整性
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import timm
from pathlib import Path
import math
from typing import Dict, List, Tuple

class PositionAwareFeatureReorganizer(nn.Module):
    """
    位置编码感知的特征重组模块
    将ViT的序列特征重新组织为2D空间特征图，同时保持位置信息
    """
    def __init__(self, embed_dim=768, patch_size=16, img_size=512):
        super().__init__()
        self.embed_dim = embed_dim
        self.patch_size = patch_size
        self.img_size = img_size
        self.num_patches = (img_size // patch_size) ** 2  # 32x32 = 1024
        self.spatial_size = img_size // patch_size  # 32
        
        # 位置编码增强模块
        self.pos_enhance = nn.Sequential(
            nn.LayerNorm(embed_dim),
            nn.Linear(embed_dim, embed_dim),
            nn.GELU(),
            nn.Linear(embed_dim, embed_dim)
        )
        
    def forward(self, vit_features):
        """
        Args:
            vit_features: (B, N+1, D) ViT输出特征，包含CLS token
            
        Returns:
            tuple: (spatial_features, cls_token)
                - spatial_features: (B, D, H, W) 2D空间特征图
                - cls_token: (B, D) 全局特征
        """
        B = vit_features.shape[0]
        
        # 分离CLS token和patch特征
        cls_token = vit_features[:, 0, :]  # (B, D)
        patch_features = vit_features[:, 1:, :]  # (B, N, D)
        
        # 位置编码增强
        enhanced_features = self.pos_enhance(patch_features)
        
        # 重塑为2D特征图
        H = W = self.spatial_size  # 32
        spatial_features = enhanced_features.transpose(1, 2).reshape(B, self.embed_dim, H, W)
        
        return spatial_features, cls_token

class ProgressiveResolutionRecovery(nn.Module):
    """
    渐进式空间分辨率恢复策略
    通过多阶段上采样逐步恢复空间分辨率，保持特征质量
    """
    def __init__(self, in_channels, target_channels, scale_factor=2):
        super().__init__()
        self.scale_factor = scale_factor
        
        # 渐进式上采样模块
        self.upsample_conv = nn.Sequential(
            nn.ConvTranspose2d(in_channels, target_channels, 
                             kernel_size=scale_factor*2, stride=scale_factor, 
                             padding=scale_factor//2),
            nn.BatchNorm2d(target_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(target_channels, target_channels, 3, 1, 1),
            nn.BatchNorm2d(target_channels),
            nn.ReLU(inplace=True)
        )
        
        # 特征细化模块
        self.refine = nn.Sequential(
            nn.Conv2d(target_channels, target_channels, 3, 1, 1),
            nn.BatchNorm2d(target_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(target_channels, target_channels, 1, 1, 0)
        )
        
    def forward(self, x):
        """
        Args:
            x: (B, C_in, H, W) 输入特征图
            
        Returns:
            torch.Tensor: (B, C_out, H*scale, W*scale) 上采样后的特征图
        """
        # 渐进式上采样
        upsampled = self.upsample_conv(x)
        
        # 特征细化
        refined = self.refine(upsampled)
        
        return refined

class AdaptiveChannelAdjuster(nn.Module):
    """
    通道维度自适应调整机制
    根据不同层级的语义需求调整特征通道数
    """
    def __init__(self, in_channels, out_channels, layer_type='early'):
        super().__init__()
        self.layer_type = layer_type
        
        # 通道调整策略
        if layer_type == 'early':  # Layer 3 - 早期特征，保持细节
            self.channel_adjust = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1, 1, 0),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            )
        elif layer_type == 'middle':  # Layer 6 - 中层特征，平衡语义和细节
            self.channel_adjust = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1, 1, 0),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_channels, out_channels, 3, 1, 1),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            )
        elif layer_type == 'high':  # Layer 9 - 高层特征，增强语义
            self.channel_adjust = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1, 1, 0),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_channels, out_channels, 3, 1, 1),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_channels, out_channels, 1, 1, 0),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            )
        else:  # Layer 12 - 最终特征，最大语义多样性
            self.channel_adjust = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1, 1, 0),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_channels, out_channels, 3, 1, 1),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_channels, out_channels, 3, 1, 1),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_channels, out_channels, 1, 1, 0),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            )
            
    def forward(self, x):
        return self.channel_adjust(x)

class MultiLayerViTFeatureExtractor(nn.Module):
    """
    多层ViT特征同时提取机制
    专门设计用于同时提取Layer 3, 6, 9, 12的特征
    """
    def __init__(self, model_name='vit_base_patch16_224', img_size=512, pretrained_path=None, external_weight_loading=False):
        super().__init__()
        self.model_name = model_name
        self.img_size = img_size
        self.embed_dim = 768  # ViT-Base
        self.patch_size = 16
        
        # 目标提取层（基于深度特征分析结果）
        self.target_layers = [3, 6, 9, 12]  # Layer 3, 6, 9, 12
        
        # 创建ViT编码器
        self.encoder = timm.create_model(
            model_name, 
            pretrained=False, 
            num_classes=0, 
            img_size=img_size
        )
        
        # 加载MAE预训练权重
        if pretrained_path and Path(pretrained_path).exists():
            self.load_mae_weights(pretrained_path)
            print(f"成功加载MAE预训练权重: {pretrained_path}")
        elif external_weight_loading:
            # 权重将通过外部MAEWeightLoader加载，这是正常流程
            print("等待外部权重加载器加载MAE预训练权重...")
        elif pretrained_path is None:
            # 权重将通过外部加载或使用随机初始化
            pass
        else:
            print(f"警告: 未找到预训练权重文件 {pretrained_path}，使用随机初始化")
            
        # 注册特征提取钩子
        self.features = {}
        self.hooks = []
        self._register_hooks()
        
        # 位置编码感知的特征重组模块
        self.feature_reorganizers = nn.ModuleDict({
            f'layer_{layer}': PositionAwareFeatureReorganizer(
                embed_dim=self.embed_dim, 
                patch_size=self.patch_size, 
                img_size=img_size
            ) for layer in self.target_layers
        })
        
    def load_mae_weights(self, checkpoint_path):
        """
        加载MAE预训练权重到ViT编码器
        """
        checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
        
        # 处理checkpoint格式
        if 'model' in checkpoint:
            state_dict = checkpoint['model']
        else:
            state_dict = checkpoint
            
        # 过滤出编码器相关的权重
        encoder_state_dict = {}
        for key, value in state_dict.items():
            if key.startswith('encoder.'):
                # 移除'encoder.'前缀
                new_key = key[8:]
                encoder_state_dict[new_key] = value
                
        # 加载权重（允许部分匹配）
        missing_keys, unexpected_keys = self.encoder.load_state_dict(encoder_state_dict, strict=False)
        
        if missing_keys:
            print(f"缺失的权重键: {len(missing_keys)} 个")
        if unexpected_keys:
            print(f"意外的权重键: {len(unexpected_keys)} 个")
            
    def _register_hooks(self):
        """
        注册特征提取钩子到目标层
        """
        def get_activation(name):
            def hook(model, input, output):
                self.features[name] = output.detach()
            return hook
            
        # 为目标层注册钩子
        for layer_idx in self.target_layers:
            layer_name = f'layer_{layer_idx}'
            # ViT的blocks是从0开始索引的，所以layer_idx-1
            block_idx = layer_idx - 1
            if block_idx < len(self.encoder.blocks):
                hook = self.encoder.blocks[block_idx].register_forward_hook(
                    get_activation(layer_name)
                )
                self.hooks.append(hook)
                
    def extract_multi_layer_features(self, x):
        """
        提取多层特征
        
        Args:
            x: (B, 3, H, W) 输入图像
            
        Returns:
            dict: 包含多层特征的字典
                - 'layer_3': Layer 3特征
                - 'layer_6': Layer 6特征  
                - 'layer_9': Layer 9特征
                - 'layer_12': Layer 12特征
        """
        self.features.clear()
        
        # 前向传播以触发钩子
        with torch.no_grad():
            _ = self.encoder(x)
            
        # 处理提取的特征
        processed_features = {}
        
        for layer_name, raw_features in self.features.items():
            # 使用位置编码感知的特征重组
            reorganizer = self.feature_reorganizers[layer_name]
            spatial_features, cls_token = reorganizer(raw_features)
            
            processed_features[layer_name] = {
                'spatial': spatial_features,  # (B, 768, 32, 32)
                'global': cls_token  # (B, 768)
            }
            
        return processed_features
        
    def forward(self, x):
        """
        前向传播，提取多层特征
        """
        return self.extract_multi_layer_features(x)
        
    def cleanup(self):
        """
        清理钩子
        """
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
        
    def __del__(self):
        """
        析构函数，确保钩子被清理
        """
        self.cleanup()

def test_multi_layer_feature_extractor():
    """
    测试多层ViT特征提取器
    """
    print("=== 多层ViT特征提取器测试 ===")
    
    # 模型参数
    mae_checkpoint = "/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/checkpoint-0315.pth"
    img_size = 512
    
    # 创建特征提取器
    print("创建多层ViT特征提取器...")
    extractor = MultiLayerViTFeatureExtractor(
        model_name='vit_base_patch16_224',
        img_size=img_size,
        pretrained_path=mae_checkpoint
    )
    
    # 测试特征提取
    print("\n测试多层特征提取...")
    batch_size = 2
    dummy_input = torch.randn(batch_size, 3, img_size, img_size)
    
    try:
        with torch.no_grad():
            features = extractor(dummy_input)
            
        print(f"✓ 多层特征提取成功!")
        print(f"  输入尺寸: {dummy_input.shape}")
        
        for layer_name, layer_features in features.items():
            spatial_shape = layer_features['spatial'].shape
            global_shape = layer_features['global'].shape
            print(f"  {layer_name}:")
            print(f"    空间特征: {spatial_shape}")
            print(f"    全局特征: {global_shape}")
            
        # 验证特征尺寸
        expected_spatial_shape = (batch_size, 768, 32, 32)
        expected_global_shape = (batch_size, 768)
        
        all_correct = True
        for layer_name, layer_features in features.items():
            if layer_features['spatial'].shape != expected_spatial_shape:
                print(f"✗ {layer_name} 空间特征尺寸不匹配!")
                all_correct = False
            if layer_features['global'].shape != expected_global_shape:
                print(f"✗ {layer_name} 全局特征尺寸不匹配!")
                all_correct = False
                
        if all_correct:
            print("✓ 所有特征尺寸正确!")
            
    except Exception as e:
        print(f"✗ 多层特征提取失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理钩子
        extractor.cleanup()
        
    print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_multi_layer_feature_extractor()