#!/usr/bin/env python3
"""
ViT专用UPerNet分割头
基于深度特征分析结果，专门为ViT特征设计的UPerNet分割头

设计理念：
1. ViT-Native设计：不强行模拟ResNet特征，而是设计专门适配ViT特征的分割头
2. 多层特征利用：充分利用ViT不同层的特征表示能力（Layer 3,6,9,12）
3. 空间信息恢复：通过渐进式上采样恢复空间细节
4. MAE预训练优势最大化：保持预训练特征的完整性

Author: AI Assistant
Date: 2024
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional
import math


class ConvBnAct(nn.Module):
    """基础卷积-批归一化-激活模块"""
    
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, 
                 padding=None, dilation=1, bias=False, act=True):
        super().__init__()
        if padding is None:
            padding = kernel_size // 2 if isinstance(kernel_size, int) else [k // 2 for k in kernel_size]
        
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride,
                             padding=padding, dilation=dilation, bias=bias)

        # 使用GroupNorm替代BatchNorm，在小批次和小特征图上更稳定
        # 将通道数分成8组，如果通道数小于8则使用1组
        num_groups = min(8, out_channels)
        self.norm = nn.GroupNorm(num_groups, out_channels)
        self.act = nn.ReLU(inplace=True) if act else nn.Identity()
    
    def forward(self, x):
        x = self.conv(x)
        x = self.norm(x)
        x = self.act(x)
        return x


class ViTAwarePyramidPooling(nn.Module):
    """ViT感知的金字塔池化模块
    
    专门为ViT特征设计的金字塔池化，考虑ViT的patch-based特性
    """
    
    def __init__(self, in_channels, out_channels, pool_scales=[1, 2, 3, 6]):
        super().__init__()
        self.pool_scales = pool_scales
        inter_channels = in_channels // len(pool_scales)
        
        # 为每个池化尺度创建分支
        self.pool_branches = nn.ModuleList([
            nn.Sequential(
                nn.AdaptiveAvgPool2d(scale),
                ConvBnAct(in_channels, inter_channels, 1, 1, 0)
            ) for scale in pool_scales
        ])
        
        # 输出融合层
        total_channels = in_channels + inter_channels * len(pool_scales)
        self.fusion = ConvBnAct(total_channels, out_channels, 3, 1, 1)
        
        # ViT特征增强模块
        self.vit_enhance = nn.Sequential(
            ConvBnAct(in_channels, in_channels, 3, 1, 1),
            nn.Conv2d(in_channels, in_channels, 1, 1, 0),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        """
        Args:
            x: ViT特征 (B, C, H, W)
        Returns:
            enhanced_features: 增强后的特征 (B, out_channels, H, W)
        """
        B, C, H, W = x.shape
        
        # ViT特征增强
        attention = self.vit_enhance(x)
        enhanced_x = x * attention
        
        # 金字塔池化
        pool_features = [enhanced_x]
        for pool_branch in self.pool_branches:
            pooled = pool_branch(enhanced_x)
            upsampled = F.interpolate(pooled, size=(H, W), mode='bilinear', align_corners=False)
            pool_features.append(upsampled)
        
        # 特征融合
        fused = torch.cat(pool_features, dim=1)
        output = self.fusion(fused)
        
        return output


class ViTFeatureFusionModule(nn.Module):
    """ViT特征融合模块
    
    专门为ViT多层特征设计的融合网络，考虑不同层的语义层次
    """
    
    def __init__(self, feature_channels=768, fpn_dim=256):
        super().__init__()
        self.feature_channels = feature_channels
        self.fpn_dim = fpn_dim
        
        # 为每个ViT层设计特定的处理分支
        # Layer 3: 早期特征，注重空间细节
        self.layer3_branch = nn.Sequential(
            ConvBnAct(feature_channels, fpn_dim, 3, 1, 1),
            ConvBnAct(fpn_dim, fpn_dim, 3, 1, 1)
        )
        
        # Layer 6: 中层特征，平衡语义和空间信息
        self.layer6_branch = nn.Sequential(
            ConvBnAct(feature_channels, fpn_dim, 3, 1, 1),
            ConvBnAct(fpn_dim, fpn_dim, 3, 1, 1),
            ConvBnAct(fpn_dim, fpn_dim, 1, 1, 0)
        )
        
        # Layer 9: 高层特征，注重语义信息
        self.layer9_branch = nn.Sequential(
            ConvBnAct(feature_channels, fpn_dim, 1, 1, 0),
            ConvBnAct(fpn_dim, fpn_dim, 3, 1, 1)
        )
        
        # Layer 12: 最高层特征，最丰富的语义信息
        self.layer12_branch = nn.Sequential(
            ConvBnAct(feature_channels, fpn_dim, 1, 1, 0),
            ConvBnAct(fpn_dim, fpn_dim, 1, 1, 0)
        )
        
        # 金字塔池化后的特征处理分支
        self.enhanced_layer12_branch = nn.Sequential(
            ConvBnAct(fpn_dim, fpn_dim, 3, 1, 1),
            ConvBnAct(fpn_dim, fpn_dim, 1, 1, 0)
        )
        
        # 跨层注意力机制
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=fpn_dim, num_heads=8, dropout=0.1, batch_first=True
        )
        
        # 渐进式融合网络
        self.progressive_fusion = nn.ModuleList([
            ConvBnAct(fpn_dim * 2, fpn_dim, 3, 1, 1),  # layer12 + layer9
            ConvBnAct(fpn_dim * 2, fpn_dim, 3, 1, 1),  # prev + layer6
            ConvBnAct(fpn_dim * 2, fpn_dim, 3, 1, 1),  # prev + layer3
        ])
        
        # 最终输出层
        self.output_conv = ConvBnAct(fpn_dim, fpn_dim, 3, 1, 1)
    
    def forward(self, vit_features: Dict[str, torch.Tensor], enhanced_layer12: torch.Tensor = None) -> Dict[str, torch.Tensor]:
        """
        Args:
            vit_features: ViT多层特征字典
                - 'layer_3': (B, C, H, W)
                - 'layer_6': (B, C, H, W) 
                - 'layer_9': (B, C, H, W)
                - 'layer_12': (B, C, H, W)
            enhanced_layer12: 经过金字塔池化增强的layer_12特征 (B, fpn_dim, H, W)
        Returns:
            fpn_features: FPN风格的多尺度特征
        """
        # 处理各层特征
        f3 = self.layer3_branch(vit_features['layer_3'])
        f6 = self.layer6_branch(vit_features['layer_6'])
        f9 = self.layer9_branch(vit_features['layer_9'])
        
        # 处理layer_12特征
        if enhanced_layer12 is not None:
            # 使用增强后的特征
            f12 = self.enhanced_layer12_branch(enhanced_layer12)
        else:
            # 使用原始特征
            f12 = self.layer12_branch(vit_features['layer_12'])
        
        B, C, H, W = f12.shape
        
        # 渐进式融合：从高层到低层
        # Step 1: 融合layer12和layer9
        fused = self.progressive_fusion[0](torch.cat([f12, f9], dim=1))
        
        # Step 2: 融合前面的结果和layer6
        fused = self.progressive_fusion[1](torch.cat([fused, f6], dim=1))
        
        # Step 3: 融合前面的结果和layer3
        fused = self.progressive_fusion[2](torch.cat([fused, f3], dim=1))
        
        # 最终处理
        output = self.output_conv(fused)
        
        # 生成多尺度特征（模拟FPN结构）
        fpn_features = {
            'fpn_layer1': output,  # 原始分辨率
            'fpn_layer2': F.avg_pool2d(output, 2),  # 1/2分辨率
            'fpn_layer3': F.avg_pool2d(output, 4),  # 1/4分辨率
            'fpn_layer4': F.avg_pool2d(output, 8),  # 1/8分辨率
        }
        
        return fpn_features


class ProgressiveUpsampling(nn.Module):
    """渐进式上采样解码器
    
    专门为ViT特征设计的渐进式上采样，恢复空间细节
    """
    
    def __init__(self, fpn_dim=256, num_classes=8):
        super().__init__()
        self.fpn_dim = fpn_dim
        self.num_classes = num_classes
        
        # 多尺度特征处理
        self.lateral_convs = nn.ModuleList([
            ConvBnAct(fpn_dim, fpn_dim, 1, 1, 0),  # fpn_layer1
            ConvBnAct(fpn_dim, fpn_dim, 1, 1, 0),  # fpn_layer2
            ConvBnAct(fpn_dim, fpn_dim, 1, 1, 0),  # fpn_layer3
            ConvBnAct(fpn_dim, fpn_dim, 1, 1, 0),  # fpn_layer4
        ])
        
        # 渐进式上采样模块
        self.upsample_convs = nn.ModuleList([
            ConvBnAct(fpn_dim, fpn_dim, 3, 1, 1),
            ConvBnAct(fpn_dim, fpn_dim, 3, 1, 1),
            ConvBnAct(fpn_dim, fpn_dim, 3, 1, 1),
        ])
        
        # 特征融合层
        self.fusion_conv = ConvBnAct(fpn_dim * 4, fpn_dim, 3, 1, 1)
        
        # 分割预测头
        self.seg_head = nn.Sequential(
            ConvBnAct(fpn_dim, fpn_dim, 3, 1, 1),
            nn.Dropout2d(0.1),
            nn.Conv2d(fpn_dim, num_classes, 1, 1, 0)
        )
    
    def forward(self, fpn_features: Dict[str, torch.Tensor], target_size: tuple) -> torch.Tensor:
        """
        Args:
            fpn_features: FPN特征字典
            target_size: 目标输出尺寸 (H, W)
        Returns:
            segmentation_logits: 分割预测 (B, num_classes, H, W)
        """
        # 处理各层特征
        f1 = self.lateral_convs[0](fpn_features['fpn_layer1'])
        f2 = self.lateral_convs[1](fpn_features['fpn_layer2'])
        f3 = self.lateral_convs[2](fpn_features['fpn_layer3'])
        f4 = self.lateral_convs[3](fpn_features['fpn_layer4'])
        
        # 渐进式上采样和融合
        # 从最小尺度开始
        upsampled = f4
        
        # 逐步上采样并融合
        upsampled = F.interpolate(upsampled, size=f3.shape[2:], mode='bilinear', align_corners=False)
        upsampled = upsampled + f3
        upsampled = self.upsample_convs[0](upsampled)
        
        upsampled = F.interpolate(upsampled, size=f2.shape[2:], mode='bilinear', align_corners=False)
        upsampled = upsampled + f2
        upsampled = self.upsample_convs[1](upsampled)
        
        upsampled = F.interpolate(upsampled, size=f1.shape[2:], mode='bilinear', align_corners=False)
        upsampled = upsampled + f1
        upsampled = self.upsample_convs[2](upsampled)
        
        # 多尺度特征融合
        # 将所有特征上采样到相同尺寸
        target_h, target_w = f1.shape[2:]
        multi_scale_features = [
            f1,
            F.interpolate(f2, size=(target_h, target_w), mode='bilinear', align_corners=False),
            F.interpolate(f3, size=(target_h, target_w), mode='bilinear', align_corners=False),
            F.interpolate(f4, size=(target_h, target_w), mode='bilinear', align_corners=False),
        ]
        
        # 融合多尺度特征
        fused_features = self.fusion_conv(torch.cat(multi_scale_features, dim=1))
        
        # 结合渐进式上采样结果
        final_features = fused_features + upsampled
        
        # 生成分割预测
        seg_logits = self.seg_head(final_features)
        
        # 上采样到目标尺寸
        if target_size != seg_logits.shape[2:]:
            seg_logits = F.interpolate(seg_logits, size=target_size, mode='bilinear', align_corners=False)
        
        return seg_logits


class ViTUPerNetHead(nn.Module):
    """ViT专用UPerNet分割头
    
    完整的ViT专用分割头，包含：
    1. ViT感知的金字塔池化
    2. ViT特征融合模块
    3. 渐进式上采样解码器
    """
    
    def __init__(self, 
                 feature_channels=768,
                 fpn_dim=256, 
                 num_classes=8,
                 pool_scales=[1, 2, 3, 6]):
        super().__init__()
        self.feature_channels = feature_channels
        self.fpn_dim = fpn_dim
        self.num_classes = num_classes
        
        # ViT感知的金字塔池化（应用于最高层特征）
        self.pyramid_pooling = ViTAwarePyramidPooling(
            in_channels=feature_channels,
            out_channels=fpn_dim,
            pool_scales=pool_scales
        )
        
        # ViT特征融合模块
        self.feature_fusion = ViTFeatureFusionModule(
            feature_channels=feature_channels,
            fpn_dim=fpn_dim
        )
        
        # 渐进式上采样解码器
        self.decoder = ProgressiveUpsampling(
            fpn_dim=fpn_dim,
            num_classes=num_classes
        )
        
        # 辅助损失分支（可选）
        self.aux_head = nn.Sequential(
            ConvBnAct(feature_channels, fpn_dim, 3, 1, 1),
            nn.Dropout2d(0.1),
            nn.Conv2d(fpn_dim, num_classes, 1, 1, 0)
        )
    
    def forward(self, vit_features: Dict[str, torch.Tensor], target_size: tuple) -> Dict[str, torch.Tensor]:
        """
        Args:
            vit_features: ViT多层特征字典
                - 'layer_3': (B, C, H, W)
                - 'layer_6': (B, C, H, W) 
                - 'layer_9': (B, C, H, W)
                - 'layer_12': (B, C, H, W)
            target_size: 目标输出尺寸 (H, W)
        Returns:
            outputs: 输出字典
                - 'main': 主要分割预测 (B, num_classes, H, W)
                - 'aux': 辅助分割预测 (B, num_classes, H, W)
        """
        # 对最高层特征应用金字塔池化
        enhanced_layer12 = self.pyramid_pooling(vit_features['layer_12'])
        
        # ViT特征融合（传递增强的layer_12特征）
        fpn_features = self.feature_fusion(vit_features, enhanced_layer12)
        
        # 渐进式上采样解码
        main_output = self.decoder(fpn_features, target_size)
        
        # 辅助输出（用于深度监督）
        aux_output = self.aux_head(vit_features['layer_9'])
        aux_output = F.interpolate(aux_output, size=target_size, mode='bilinear', align_corners=False)
        
        return {
            'main': main_output,
            'aux': aux_output
        }


if __name__ == "__main__":
    # 测试代码
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模拟的ViT特征
    batch_size = 2
    feature_dim = 768
    spatial_size = 32  # 512/16 = 32
    
    vit_features = {
        'layer_3': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
        'layer_6': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
        'layer_9': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
        'layer_12': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
    }
    
    # 创建ViT UPerNet分割头
    seg_head = ViTUPerNetHead(
        feature_channels=768,
        fpn_dim=256,
        num_classes=8,
        pool_scales=[1, 2, 3, 6]
    ).to(device)
    
    # 前向传播测试
    target_size = (512, 512)
    with torch.no_grad():
        outputs = seg_head(vit_features, target_size)
    
    print("ViT UPerNet分割头测试结果:")
    print(f"主要输出形状: {outputs['main'].shape}")  # 应该是 (2, 3, 512, 512)
    print(f"辅助输出形状: {outputs['aux'].shape}")   # 应该是 (2, 3, 512, 512)
    
    # 计算参数量
    total_params = sum(p.numel() for p in seg_head.parameters())
    trainable_params = sum(p.numel() for p in seg_head.parameters() if p.requires_grad)
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    
    print("\n✅ ViT专用UPerNet分割头创建成功！")