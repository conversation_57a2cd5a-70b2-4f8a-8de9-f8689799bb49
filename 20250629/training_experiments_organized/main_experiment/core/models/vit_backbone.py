#!/usr/bin/env python3
"""
ViT主干网络
基于timm库的ViT实现，支持MAE预训练权重加载
"""

import torch
import torch.nn as nn
import timm
from pathlib import Path
from typing import Dict, List, Optional, Tuple


class ViTBackbone(nn.Module):
    """ViT主干网络"""
    
    def __init__(
        self,
        model_name: str = "vit_base_patch16_224",
        img_size: int = 224,
        patch_size: int = 16,
        embed_dim: int = 768,
        depth: int = 12,
        num_heads: int = 12,
        mlp_ratio: float = 4.0,
        pretrained: bool = True,
        pretrained_path: Optional[str] = None,
        drop_rate: float = 0.0,
        drop_path_rate: float = 0.0,
        use_checkpoint: bool = False
    ):
        """
        Args:
            model_name: timm模型名称
            img_size: 输入图像尺寸
            patch_size: patch大小
            embed_dim: 嵌入维度
            depth: Transformer层数
            num_heads: 注意力头数
            mlp_ratio: MLP扩展比例
            pretrained: 是否使用预训练权重
            pretrained_path: 自定义预训练权重路径
            drop_rate: dropout率
            drop_path_rate: drop path率
            use_checkpoint: 是否使用gradient checkpointing
        """
        super().__init__()
        
        self.img_size = img_size
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        self.depth = depth
        self.num_heads = num_heads
        
        # 创建ViT模型
        if pretrained and pretrained_path is None:
            # 使用timm预训练权重
            self.vit = timm.create_model(
                model_name,
                pretrained=True,
                img_size=img_size,
                drop_rate=drop_rate,
                drop_path_rate=drop_path_rate,
                num_classes=0,  # 移除分类头
                global_pool=''  # 移除全局池化
            )
        else:
            # 创建未预训练的模型
            self.vit = timm.create_model(
                model_name,
                pretrained=False,
                img_size=img_size,
                patch_size=patch_size,
                embed_dim=embed_dim,
                depth=depth,
                num_heads=num_heads,
                mlp_ratio=mlp_ratio,
                drop_rate=drop_rate,
                drop_path_rate=drop_path_rate,
                num_classes=0,
                global_pool=''
            )
        
        # 加载自定义预训练权重
        if pretrained_path is not None:
            self.load_pretrained_weights(pretrained_path)
        
        # 启用gradient checkpointing以节省显存
        if use_checkpoint:
            self.vit.set_grad_checkpointing(True)
        
        # 计算特征图尺寸
        self.num_patches = (img_size // patch_size) ** 2
        self.spatial_size = img_size // patch_size
        
    def load_pretrained_weights(self, checkpoint_path: str):
        """加载预训练权重"""
        if not Path(checkpoint_path).exists():
            print(f"警告: 预训练权重文件不存在: {checkpoint_path}")
            return
        
        try:
            checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
            
            # 处理不同的checkpoint格式
            if 'model' in checkpoint:
                state_dict = checkpoint['model']
            elif 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            else:
                state_dict = checkpoint
            
            # 过滤和映射权重键
            model_state_dict = self.vit.state_dict()
            filtered_state_dict = {}
            
            for key, value in state_dict.items():
                # 移除可能的前缀
                clean_key = key
                for prefix in ['encoder.', 'backbone.', 'vit.']:
                    if clean_key.startswith(prefix):
                        clean_key = clean_key[len(prefix):]
                
                # 检查键是否存在于模型中
                if clean_key in model_state_dict:
                    if value.shape == model_state_dict[clean_key].shape:
                        filtered_state_dict[clean_key] = value
                    else:
                        print(f"形状不匹配，跳过: {clean_key}")
            
            # 加载权重
            missing_keys, unexpected_keys = self.vit.load_state_dict(
                filtered_state_dict, strict=False
            )
            
            print(f"成功加载预训练权重: {checkpoint_path}")
            if missing_keys:
                print(f"缺失的权重键: {len(missing_keys)} 个")
            if unexpected_keys:
                print(f"意外的权重键: {len(unexpected_keys)} 个")
                
        except Exception as e:
            print(f"加载预训练权重失败: {e}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: (B, 3, H, W) 输入图像
            
        Returns:
            torch.Tensor: (B, N+1, D) ViT输出特征，包含CLS token
        """
        return self.vit.forward_features(x)
    
    def get_intermediate_features(self, x: torch.Tensor, layers: List[int]) -> Dict[str, torch.Tensor]:
        """
        获取中间层特征
        
        Args:
            x: (B, 3, H, W) 输入图像
            layers: 需要提取特征的层索引列表
            
        Returns:
            Dict[str, torch.Tensor]: 中间层特征字典
        """
        features = {}
        
        # Patch embedding
        x = self.vit.patch_embed(x)
        x = self.vit._pos_embed(x)
        x = self.vit.norm_pre(x)
        
        # 逐层前向传播
        for i, block in enumerate(self.vit.blocks):
            x = block(x)
            
            # 保存指定层的特征
            layer_idx = i + 1  # 层索引从1开始
            if layer_idx in layers:
                features[f'layer_{layer_idx}'] = x.clone()
        
        # 最终归一化
        x = self.vit.norm(x)
        features['final'] = x
        
        return features
    
    def reshape_to_spatial(self, features: torch.Tensor) -> torch.Tensor:
        """
        将序列特征重塑为空间特征图
        
        Args:
            features: (B, N+1, D) ViT特征，包含CLS token
            
        Returns:
            torch.Tensor: (B, D, H, W) 空间特征图
        """
        B, N_plus_1, D = features.shape
        
        # 移除CLS token
        spatial_features = features[:, 1:, :]  # (B, N, D)
        
        # 重塑为空间维度
        H = W = self.spatial_size
        spatial_features = spatial_features.transpose(1, 2).reshape(B, D, H, W)
        
        return spatial_features
    
    def get_cls_token(self, features: torch.Tensor) -> torch.Tensor:
        """
        提取CLS token
        
        Args:
            features: (B, N+1, D) ViT特征，包含CLS token
            
        Returns:
            torch.Tensor: (B, D) CLS token特征
        """
        return features[:, 0, :]  # 第一个token是CLS token


def create_vit_base(
    img_size: int = 224,
    pretrained: bool = True,
    pretrained_path: Optional[str] = None,
    **kwargs
) -> ViTBackbone:
    """创建ViT-Base模型"""
    return ViTBackbone(
        model_name="vit_base_patch16_224",
        img_size=img_size,
        patch_size=16,
        embed_dim=768,
        depth=12,
        num_heads=12,
        pretrained=pretrained,
        pretrained_path=pretrained_path,
        **kwargs
    )


def create_vit_large(
    img_size: int = 224,
    pretrained: bool = True,
    pretrained_path: Optional[str] = None,
    **kwargs
) -> ViTBackbone:
    """创建ViT-Large模型"""
    return ViTBackbone(
        model_name="vit_large_patch16_224",
        img_size=img_size,
        patch_size=16,
        embed_dim=1024,
        depth=24,
        num_heads=16,
        pretrained=pretrained,
        pretrained_path=pretrained_path,
        **kwargs
    )


def create_vit_huge(
    img_size: int = 224,
    pretrained: bool = True,
    pretrained_path: Optional[str] = None,
    **kwargs
) -> ViTBackbone:
    """创建ViT-Huge模型"""
    return ViTBackbone(
        model_name="vit_huge_patch14_224",
        img_size=img_size,
        patch_size=14,
        embed_dim=1280,
        depth=32,
        num_heads=16,
        pretrained=pretrained,
        pretrained_path=pretrained_path,
        **kwargs
    )
