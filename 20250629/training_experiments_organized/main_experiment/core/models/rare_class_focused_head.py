#!/usr/bin/env python3
"""
稀有类别专用分割头
专门针对稀有类别（eggs, larvae, nectar, pollen）优化的分割头
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, List


class RareClassFocusedHead(nn.Module):
    """
    稀有类别专用分割头
    
    设计思路：
    1. 双分支架构：主分支处理所有类别，稀有分支专门处理稀有类别
    2. 特征增强：对稀有类别相关特征进行增强
    3. 多尺度融合：在多个尺度上进行特征融合
    4. 类别平衡损失：使用专门的损失函数处理类别不平衡
    """
    
    def __init__(
        self,
        feature_channels: int = 768,
        num_classes: int = 8,
        rare_classes: List[int] = [2, 5, 6, 7],  # eggs, larvae, nectar, pollen
        fpn_dim: int = 256,
        dropout: float = 0.1
    ):
        super().__init__()
        
        self.feature_channels = feature_channels
        self.num_classes = num_classes
        self.rare_classes = rare_classes
        self.common_classes = [i for i in range(num_classes) if i not in rare_classes]
        self.fpn_dim = fpn_dim
        
        # 特征金字塔网络 - 多尺度特征提取
        self.fpn_convs = nn.ModuleDict({
            'layer_3': nn.Conv2d(feature_channels, fpn_dim, 1),
            'layer_6': nn.Conv2d(feature_channels, fpn_dim, 1),
            'layer_9': nn.Conv2d(feature_channels, fpn_dim, 1),
            'layer_12': nn.Conv2d(feature_channels, fpn_dim, 1),
        })
        
        # 主分支 - 处理所有类别
        self.main_branch = self._build_main_branch()
        
        # 稀有类别专用分支
        self.rare_branch = self._build_rare_branch()
        
        # 特征融合模块
        self.feature_fusion = self._build_feature_fusion()
        
        # 最终分类头
        self.main_classifier = nn.Conv2d(fpn_dim, num_classes, 1)
        self.rare_classifier = nn.Conv2d(fpn_dim, len(rare_classes), 1)
        
        # 注意力机制 - 用于稀有类别特征增强
        self.rare_attention = self._build_rare_attention()
        
    def _build_main_branch(self):
        """构建主分支"""
        return nn.Sequential(
            nn.Conv2d(self.fpn_dim * 4, self.fpn_dim, 3, padding=1),
            nn.BatchNorm2d(self.fpn_dim),
            nn.ReLU(inplace=True),
            nn.Dropout2d(0.1),
            nn.Conv2d(self.fpn_dim, self.fpn_dim, 3, padding=1),
            nn.BatchNorm2d(self.fpn_dim),
            nn.ReLU(inplace=True),
        )
    
    def _build_rare_branch(self):
        """构建稀有类别专用分支"""
        return nn.Sequential(
            # 更深的网络用于稀有类别特征提取
            nn.Conv2d(self.fpn_dim * 4, self.fpn_dim, 3, padding=1),
            nn.BatchNorm2d(self.fpn_dim),
            nn.ReLU(inplace=True),
            nn.Dropout2d(0.1),
            
            nn.Conv2d(self.fpn_dim, self.fpn_dim, 3, padding=1),
            nn.BatchNorm2d(self.fpn_dim),
            nn.ReLU(inplace=True),
            nn.Dropout2d(0.1),
            
            # 扩张卷积增加感受野
            nn.Conv2d(self.fpn_dim, self.fpn_dim, 3, padding=2, dilation=2),
            nn.BatchNorm2d(self.fpn_dim),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(self.fpn_dim, self.fpn_dim, 3, padding=4, dilation=4),
            nn.BatchNorm2d(self.fpn_dim),
            nn.ReLU(inplace=True),
        )
    
    def _build_feature_fusion(self):
        """构建特征融合模块"""
        return nn.Sequential(
            nn.Conv2d(self.fpn_dim * 2, self.fpn_dim, 1),
            nn.BatchNorm2d(self.fpn_dim),
            nn.ReLU(inplace=True),
        )
    
    def _build_rare_attention(self):
        """构建稀有类别注意力机制"""
        return nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(self.fpn_dim, self.fpn_dim // 4, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(self.fpn_dim // 4, self.fpn_dim, 1),
            nn.Sigmoid()
        )
    
    def forward(self, vit_features: Dict[str, torch.Tensor], target_size: Tuple[int, int]) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            vit_features: MAE多层特征
            target_size: 目标输出尺寸
            
        Returns:
            输出字典包含主要预测、稀有类别预测和融合预测
        """
        # 1. 特征金字塔处理
        fpn_features = []
        target_h, target_w = target_size[0] // 16, target_size[1] // 16  # MAE特征的空间尺寸
        
        for layer_name, feature in vit_features.items():
            # 转换特征维度
            fpn_feat = self.fpn_convs[layer_name](feature)
            # 统一空间尺寸
            if fpn_feat.shape[2:] != (target_h, target_w):
                fpn_feat = F.interpolate(fpn_feat, size=(target_h, target_w), mode='bilinear', align_corners=False)
            fpn_features.append(fpn_feat)
        
        # 2. 多尺度特征融合
        fused_features = torch.cat(fpn_features, dim=1)  # [B, fpn_dim*4, H, W]
        
        # 3. 双分支处理
        # 主分支 - 处理所有类别
        main_features = self.main_branch(fused_features)
        
        # 稀有分支 - 专门处理稀有类别
        rare_features = self.rare_branch(fused_features)
        
        # 4. 稀有类别注意力增强
        attention_weights = self.rare_attention(rare_features)
        rare_features = rare_features * attention_weights
        
        # 5. 特征融合
        combined_features = torch.cat([main_features, rare_features], dim=1)
        final_features = self.feature_fusion(combined_features)
        
        # 6. 分类预测
        # 主分类器 - 所有类别
        main_output = self.main_classifier(final_features)
        main_output = F.interpolate(main_output, size=target_size, mode='bilinear', align_corners=False)
        
        # 稀有类别分类器
        rare_output = self.rare_classifier(rare_features)
        rare_output = F.interpolate(rare_output, size=target_size, mode='bilinear', align_corners=False)
        
        # 7. 构建最终输出
        # 将稀有类别预测融合到主输出中
        final_output = main_output.clone()
        for i, rare_class_idx in enumerate(self.rare_classes):
            # 使用稀有分支的预测替换主分支中对应类别的预测
            final_output[:, rare_class_idx:rare_class_idx+1] = rare_output[:, i:i+1]
        
        return {
            'main': final_output,
            'main_branch': main_output,
            'rare_branch': rare_output,
            'features': final_features
        }


class RareClassBalancedLoss(nn.Module):
    """稀有类别平衡损失函数"""
    
    def __init__(
        self,
        num_classes: int = 8,
        rare_classes: List[int] = [2, 5, 6, 7],
        rare_weight: float = 5.0,
        focal_alpha: float = 0.25,
        focal_gamma: float = 2.0
    ):
        super().__init__()
        self.num_classes = num_classes
        self.rare_classes = rare_classes
        self.rare_weight = rare_weight
        self.focal_alpha = focal_alpha
        self.focal_gamma = focal_gamma
        
        # 创建类别权重
        self.class_weights = torch.ones(num_classes)
        for rare_idx in rare_classes:
            self.class_weights[rare_idx] = rare_weight
    
    def focal_loss(self, pred, target, alpha=0.25, gamma=2.0):
        """Focal Loss实现"""
        ce_loss = F.cross_entropy(pred, target, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = alpha * (1 - pt) ** gamma * ce_loss
        return focal_loss.mean()
    
    def forward(self, outputs: Dict[str, torch.Tensor], target: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算损失
        
        Args:
            outputs: 模型输出字典
            target: 真实标签
            
        Returns:
            损失字典
        """
        device = target.device
        self.class_weights = self.class_weights.to(device)
        
        losses = {}
        
        # 主损失 - 使用Focal Loss处理类别不平衡
        main_loss = self.focal_loss(outputs['main'], target, self.focal_alpha, self.focal_gamma)
        losses['main_loss'] = main_loss
        
        # 稀有类别专用损失
        if 'rare_branch' in outputs:
            # 创建稀有类别标签
            rare_target = target.clone()
            for i, rare_idx in enumerate(self.rare_classes):
                rare_target[target == rare_idx] = i
            
            # 只在有稀有类别像素的地方计算损失
            rare_mask = torch.zeros_like(target, dtype=torch.bool)
            for rare_idx in self.rare_classes:
                rare_mask |= (target == rare_idx)
            
            if rare_mask.sum() > 0:
                rare_pred = outputs['rare_branch']
                rare_loss = F.cross_entropy(
                    rare_pred[rare_mask.unsqueeze(1).expand(-1, rare_pred.size(1), -1, -1)].view(-1, rare_pred.size(1)),
                    rare_target[rare_mask].view(-1),
                    weight=torch.ones(len(self.rare_classes)).to(device)
                )
                losses['rare_loss'] = rare_loss * 2.0  # 增加稀有类别损失权重
            else:
                losses['rare_loss'] = torch.tensor(0.0, device=device)
        
        # 总损失
        total_loss = losses['main_loss'] + losses.get('rare_loss', 0)
        losses['total_loss'] = total_loss
        
        return losses


if __name__ == "__main__":
    # 测试代码
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模拟数据
    batch_size = 2
    feature_dim = 768
    spatial_size = 32
    
    vit_features = {
        'layer_3': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
        'layer_6': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
        'layer_9': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
        'layer_12': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
    }
    
    target = torch.randint(0, 8, (batch_size, 512, 512)).to(device)
    
    # 创建模型
    model = RareClassFocusedHead(
        feature_channels=768,
        num_classes=8,
        rare_classes=[2, 5, 6, 7]
    ).to(device)
    
    # 创建损失函数
    criterion = RareClassBalancedLoss(
        num_classes=8,
        rare_classes=[2, 5, 6, 7],
        rare_weight=5.0
    )
    
    # 前向传播
    with torch.no_grad():
        outputs = model(vit_features, (512, 512))
        losses = criterion(outputs, target)
    
    print("稀有类别专用分割头测试结果:")
    print(f"主要输出形状: {outputs['main'].shape}")
    print(f"稀有分支输出形状: {outputs['rare_branch'].shape}")
    print(f"总损失: {losses['total_loss'].item():.4f}")
    print(f"主损失: {losses['main_loss'].item():.4f}")
    print(f"稀有损失: {losses['rare_loss'].item():.4f}")
    
    # 参数量统计
    total_params = sum(p.numel() for p in model.parameters())
    print(f"总参数量: {total_params:,}")
    
    print("\n✅ 稀有类别专用分割头创建成功！")
