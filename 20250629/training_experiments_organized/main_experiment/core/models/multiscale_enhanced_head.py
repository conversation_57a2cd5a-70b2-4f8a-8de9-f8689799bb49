#!/usr/bin/env python3
"""
多尺度特征增强分割头
结合多尺度特征融合、空间注意力和通道注意力的高级分割头
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, List


class ASPP(nn.Module):
    """空洞空间金字塔池化"""
    def __init__(self, in_channels: int, out_channels: int, atrous_rates: List[int] = [6, 12, 18]):
        super().__init__()
        
        self.convs = nn.ModuleList()
        
        # 1x1卷积
        self.convs.append(nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        ))
        
        # 空洞卷积
        for rate in atrous_rates:
            self.convs.append(nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 3, padding=rate, dilation=rate, bias=False),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            ))
        
        # 全局平均池化
        self.global_pool = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
        
        # 融合卷积
        self.project = nn.Sequential(
            nn.Conv2d(out_channels * (len(atrous_rates) + 2), out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Dropout2d(0.1)
        )
    
    def forward(self, x):
        features = []
        
        # 各种尺度的特征
        for conv in self.convs:
            features.append(conv(x))
        
        # 全局特征
        global_feat = self.global_pool(x)
        global_feat = F.interpolate(global_feat, size=x.shape[2:], mode='bilinear', align_corners=False)
        features.append(global_feat)
        
        # 特征融合
        x = torch.cat(features, dim=1)
        x = self.project(x)
        
        return x


class ChannelAttention(nn.Module):
    """通道注意力模块"""
    def __init__(self, channels: int, reduction: int = 16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )
        
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return x * self.sigmoid(out)


class SpatialAttention(nn.Module):
    """空间注意力模块"""
    def __init__(self, kernel_size: int = 7):
        super().__init__()
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size // 2, bias=False)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        attention = torch.cat([avg_out, max_out], dim=1)
        attention = self.conv(attention)
        return x * self.sigmoid(attention)


class CBAM(nn.Module):
    """卷积块注意力模块"""
    def __init__(self, channels: int, reduction: int = 16, kernel_size: int = 7):
        super().__init__()
        self.channel_attention = ChannelAttention(channels, reduction)
        self.spatial_attention = SpatialAttention(kernel_size)
    
    def forward(self, x):
        x = self.channel_attention(x)
        x = self.spatial_attention(x)
        return x


class MultiscaleEnhancedHead(nn.Module):
    """
    多尺度特征增强分割头
    
    特点：
    1. ASPP多尺度特征提取
    2. CBAM注意力机制
    3. 特征金字塔融合
    4. 渐进式上采样
    5. 深度监督训练
    """
    
    def __init__(
        self,
        feature_channels: int = 768,
        num_classes: int = 8,
        fpn_dim: int = 256,
        aspp_rates: List[int] = [6, 12, 18],
        use_cbam: bool = True,
        use_deep_supervision: bool = True,
        dropout: float = 0.1
    ):
        super().__init__()
        
        self.feature_channels = feature_channels
        self.num_classes = num_classes
        self.fpn_dim = fpn_dim
        self.use_cbam = use_cbam
        self.use_deep_supervision = use_deep_supervision
        
        # 特征适配层
        self.feature_adapters = nn.ModuleDict({
            'layer_3': nn.Conv2d(feature_channels, fpn_dim, 1),
            'layer_6': nn.Conv2d(feature_channels, fpn_dim, 1),
            'layer_9': nn.Conv2d(feature_channels, fpn_dim, 1),
            'layer_12': nn.Conv2d(feature_channels, fpn_dim, 1),
        })
        
        # ASPP模块用于最高层特征
        self.aspp = ASPP(fpn_dim, fpn_dim, aspp_rates)
        
        # 特征金字塔融合
        self.fpn_convs = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(fpn_dim, fpn_dim, 3, padding=1),
                nn.BatchNorm2d(fpn_dim),
                nn.ReLU(inplace=True)
            ) for _ in range(4)
        ])
        
        # CBAM注意力模块
        if use_cbam:
            self.cbam_modules = nn.ModuleList([
                CBAM(fpn_dim) for _ in range(4)
            ])
        
        # 渐进式解码器
        self.decoder_stages = nn.ModuleList()
        for i in range(3):  # 3个上采样阶段
            stage = nn.Sequential(
                nn.Conv2d(fpn_dim * 2 if i == 0 else fpn_dim, fpn_dim, 3, padding=1),
                nn.BatchNorm2d(fpn_dim),
                nn.ReLU(inplace=True),
                nn.Dropout2d(dropout),
                nn.Conv2d(fpn_dim, fpn_dim, 3, padding=1),
                nn.BatchNorm2d(fpn_dim),
                nn.ReLU(inplace=True)
            )
            self.decoder_stages.append(stage)
        
        # 深度监督的辅助分类头
        if use_deep_supervision:
            self.aux_heads = nn.ModuleList([
                nn.Conv2d(fpn_dim, num_classes, 1) for _ in range(3)
            ])
        
        # 最终分类头
        self.final_conv = nn.Sequential(
            nn.Conv2d(fpn_dim, fpn_dim // 2, 3, padding=1),
            nn.BatchNorm2d(fpn_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout),
            nn.Conv2d(fpn_dim // 2, num_classes, 1)
        )
        
        # 边缘增强模块
        self.edge_enhancer = self._build_edge_enhancer()
        
    def _build_edge_enhancer(self):
        """构建边缘增强模块"""
        return nn.Sequential(
            nn.Conv2d(self.fpn_dim, self.fpn_dim // 4, 1),
            nn.BatchNorm2d(self.fpn_dim // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(self.fpn_dim // 4, self.fpn_dim // 4, 3, padding=1),
            nn.BatchNorm2d(self.fpn_dim // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(self.fpn_dim // 4, 1, 1),
            nn.Sigmoid()
        )
    
    def forward(self, vit_features: Dict[str, torch.Tensor], target_size: Tuple[int, int]) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            vit_features: MAE多层特征
            target_size: 目标输出尺寸
            
        Returns:
            输出字典
        """
        # 1. 特征适配
        adapted_features = {}
        for layer_name, feature in vit_features.items():
            adapted_features[layer_name] = self.feature_adapters[layer_name](feature)
        
        # 2. 特征金字塔处理
        fpn_features = []
        layer_names = ['layer_12', 'layer_9', 'layer_6', 'layer_3']
        
        for i, layer_name in enumerate(layer_names):
            feat = adapted_features[layer_name]
            
            # 对最高层特征应用ASPP
            if i == 0:
                feat = self.aspp(feat)
            
            # FPN卷积
            feat = self.fpn_convs[i](feat)
            
            # CBAM注意力
            if self.use_cbam:
                feat = self.cbam_modules[i](feat)
            
            fpn_features.append(feat)
        
        # 3. 自顶向下的特征融合
        # 从最高层开始
        x = fpn_features[0]  # layer_12
        aux_outputs = []
        
        for i in range(1, len(fpn_features)):
            # 上采样到下一层的尺寸
            target_feat = fpn_features[i]
            if x.shape[2:] != target_feat.shape[2:]:
                x = F.interpolate(x, size=target_feat.shape[2:], mode='bilinear', align_corners=False)
            
            # 特征融合
            x = torch.cat([x, target_feat], dim=1)
            
            # 解码器处理
            x = self.decoder_stages[i-1](x)
            
            # 深度监督
            if self.use_deep_supervision:
                aux_out = self.aux_heads[i-1](x)
                aux_out = F.interpolate(aux_out, size=target_size, mode='bilinear', align_corners=False)
                aux_outputs.append(aux_out)
        
        # 4. 边缘增强
        edge_attention = self.edge_enhancer(x)
        x = x * edge_attention
        
        # 5. 最终预测
        main_output = self.final_conv(x)
        main_output = F.interpolate(main_output, size=target_size, mode='bilinear', align_corners=False)
        
        # 6. 构建输出
        outputs = {'main': main_output}
        
        if self.use_deep_supervision and aux_outputs:
            outputs['aux'] = aux_outputs
        
        outputs['edge_attention'] = F.interpolate(edge_attention, size=target_size, mode='bilinear', align_corners=False)
        
        return outputs


if __name__ == "__main__":
    # 测试代码
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模拟数据
    batch_size = 2
    feature_dim = 768
    spatial_size = 32
    
    vit_features = {
        'layer_3': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
        'layer_6': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
        'layer_9': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
        'layer_12': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
    }
    
    # 创建模型
    model = MultiscaleEnhancedHead(
        feature_channels=768,
        num_classes=8,
        fpn_dim=256,
        aspp_rates=[6, 12, 18],
        use_cbam=True,
        use_deep_supervision=True
    ).to(device)
    
    # 前向传播
    with torch.no_grad():
        outputs = model(vit_features, (512, 512))
    
    print("多尺度特征增强分割头测试结果:")
    print(f"主要输出形状: {outputs['main'].shape}")
    print(f"边缘注意力形状: {outputs['edge_attention'].shape}")
    
    if 'aux' in outputs:
        print(f"辅助输出数量: {len(outputs['aux'])}")
        for i, aux_out in enumerate(outputs['aux']):
            print(f"辅助输出{i+1}形状: {aux_out.shape}")
    
    # 参数量统计
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    
    print("\n✅ 多尺度特征增强分割头创建成功！")
