"""
模型定义模块
包含ViT主干网络、UPerNet分割头和特征提取器
"""

from .vit_backbone import (
    ViTBackbone, create_vit_base, create_vit_large, create_vit_huge
)
from .upernet_head import (
    ViTUPerNetHead, ViTAwarePyramidPooling,
    ViTFeatureFusionModule, ProgressiveUpsampling
)
from .feature_extractor import (
    MultiLayerViTFeatureExtractor, PositionAwareFeatureReorganizer
)

__all__ = [
    "ViTBackbone",
    "create_vit_base",
    "create_vit_large",
    "create_vit_huge",
    "ViTUPerNetHead",
    "ViTAwarePyramidPooling",
    "ViTFeatureFusionModule",
    "ProgressiveUpsampling",
    "MultiLayerViTFeatureExtractor",
    "PositionAwareFeatureReorganizer"
]
