#!/usr/bin/env python3
"""
MAE + UNet混合分割头
结合MAE的强大特征提取和UNet的优秀空间细节恢复能力
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional


class ConvBlock(nn.Module):
    """UNet风格的卷积块"""
    def __init__(self, in_channels: int, out_channels: int, dropout: float = 0.1):
        super().__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, 3, padding=1)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.conv2 = nn.Conv2d(out_channels, out_channels, 3, padding=1)
        self.bn2 = nn.BatchNorm2d(out_channels)
        self.dropout = nn.Dropout2d(dropout)
        self.relu = nn.ReLU(inplace=True)
        
    def forward(self, x):
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.dropout(x)
        x = self.relu(self.bn2(self.conv2(x)))
        return x


class AttentionGate(nn.Module):
    """注意力门控机制，用于特征融合"""
    def __init__(self, F_g: int, F_l: int, F_int: int):
        super().__init__()
        self.W_g = nn.Sequential(
            nn.Conv2d(F_g, F_int, 1, bias=True),
            nn.BatchNorm2d(F_int)
        )
        self.W_x = nn.Sequential(
            nn.Conv2d(F_l, F_int, 1, bias=True),
            nn.BatchNorm2d(F_int)
        )
        self.psi = nn.Sequential(
            nn.Conv2d(F_int, 1, 1, bias=True),
            nn.BatchNorm2d(1),
            nn.Sigmoid()
        )
        self.relu = nn.ReLU(inplace=True)
        
    def forward(self, g, x):
        g1 = self.W_g(g)
        x1 = self.W_x(x)
        psi = self.relu(g1 + x1)
        psi = self.psi(psi)
        return x * psi


class MAEUNetHead(nn.Module):
    """
    MAE + UNet混合分割头
    
    特点：
    1. 使用MAE的多层特征作为UNet的编码器特征
    2. 添加注意力门控机制
    3. 针对稀有类别的特殊处理
    4. 深度监督训练
    """
    
    def __init__(
        self,
        feature_channels: int = 768,
        num_classes: int = 8,
        decoder_channels: Tuple[int, ...] = (512, 256, 128, 64),
        dropout: float = 0.1,
        use_attention: bool = True,
        use_deep_supervision: bool = True
    ):
        super().__init__()
        
        self.feature_channels = feature_channels
        self.num_classes = num_classes
        self.decoder_channels = decoder_channels
        self.use_attention = use_attention
        self.use_deep_supervision = use_deep_supervision
        
        # 特征适配层 - 将MAE特征适配到UNet解码器
        self.feature_adapters = nn.ModuleDict({
            'layer_3': nn.Conv2d(feature_channels, decoder_channels[3], 1),  # 64
            'layer_6': nn.Conv2d(feature_channels, decoder_channels[2], 1),  # 128
            'layer_9': nn.Conv2d(feature_channels, decoder_channels[1], 1),  # 256
            'layer_12': nn.Conv2d(feature_channels, decoder_channels[0], 1), # 512
        })
        
        # UNet解码器
        self.decoder_blocks = nn.ModuleList()
        self.upsamples = nn.ModuleList()
        
        # 注意力门控（如果启用）
        if use_attention:
            self.attention_gates = nn.ModuleList()
        
        # 构建解码器层
        in_channels = decoder_channels[0]  # 512
        for i, out_channels in enumerate(decoder_channels[1:]):  # [256, 128, 64]
            # 上采样
            self.upsamples.append(
                nn.ConvTranspose2d(in_channels, out_channels, 2, stride=2)
            )
            
            # 注意力门控
            if use_attention:
                self.attention_gates.append(
                    AttentionGate(out_channels, out_channels, out_channels // 2)
                )
            
            # 解码器块（考虑skip connection）
            conv_in_channels = out_channels * 2 if i < len(decoder_channels) - 2 else out_channels
            self.decoder_blocks.append(
                ConvBlock(conv_in_channels, out_channels, dropout)
            )
            
            in_channels = out_channels
        
        # 最终分类头
        self.final_conv = nn.Conv2d(decoder_channels[-1], num_classes, 1)
        
        # 深度监督的辅助分类头
        if use_deep_supervision:
            self.aux_heads = nn.ModuleList([
                nn.Conv2d(decoder_channels[1], num_classes, 1),  # 256 -> num_classes
                nn.Conv2d(decoder_channels[2], num_classes, 1),  # 128 -> num_classes
            ])
        
        # 稀有类别增强模块
        self.rare_class_enhancer = self._build_rare_class_enhancer()
        
    def _build_rare_class_enhancer(self):
        """构建稀有类别增强模块"""
        return nn.Sequential(
            nn.Conv2d(self.decoder_channels[-1], self.decoder_channels[-1], 3, padding=1),
            nn.BatchNorm2d(self.decoder_channels[-1]),
            nn.ReLU(inplace=True),
            nn.Conv2d(self.decoder_channels[-1], self.decoder_channels[-1], 3, padding=1),
            nn.BatchNorm2d(self.decoder_channels[-1]),
            nn.ReLU(inplace=True),
        )
    
    def forward(self, vit_features: Dict[str, torch.Tensor], target_size: Tuple[int, int]) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            vit_features: MAE多层特征
            target_size: 目标输出尺寸
            
        Returns:
            输出字典包含主要预测和辅助预测
        """
        # 适配MAE特征到UNet解码器通道
        adapted_features = {}
        for layer_name, feature in vit_features.items():
            adapted_features[layer_name] = self.feature_adapters[layer_name](feature)
        
        # 开始解码 - 从最深层开始
        x = adapted_features['layer_12']  # [B, 512, H/16, W/16]
        
        # 存储中间结果用于深度监督
        intermediate_outputs = []
        
        # 逐层解码
        skip_features = [
            adapted_features['layer_9'],   # [B, 256, H/16, W/16]
            adapted_features['layer_6'],   # [B, 128, H/16, W/16]
            adapted_features['layer_3'],   # [B, 64, H/16, W/16]
        ]
        
        for i, (upsample, decoder_block) in enumerate(zip(self.upsamples, self.decoder_blocks)):
            # 上采样
            x = upsample(x)  # 尺寸翻倍
            
            # Skip connection（如果有对应的特征）
            if i < len(skip_features):
                skip_feat = skip_features[i]
                
                # 确保尺寸匹配
                if x.shape[2:] != skip_feat.shape[2:]:
                    skip_feat = F.interpolate(skip_feat, size=x.shape[2:], mode='bilinear', align_corners=False)
                
                # 注意力门控
                if self.use_attention and i < len(self.attention_gates):
                    skip_feat = self.attention_gates[i](x, skip_feat)
                
                # 特征融合
                x = torch.cat([x, skip_feat], dim=1)
            
            # 解码器块
            x = decoder_block(x)
            
            # 保存中间结果用于深度监督
            if self.use_deep_supervision and i < len(self.aux_heads):
                aux_out = self.aux_heads[i](x)
                aux_out = F.interpolate(aux_out, size=target_size, mode='bilinear', align_corners=False)
                intermediate_outputs.append(aux_out)
        
        # 稀有类别增强
        x = self.rare_class_enhancer(x)
        
        # 最终预测
        main_output = self.final_conv(x)
        main_output = F.interpolate(main_output, size=target_size, mode='bilinear', align_corners=False)
        
        # 构建输出字典
        outputs = {'main': main_output}
        
        if self.use_deep_supervision and intermediate_outputs:
            outputs['aux'] = intermediate_outputs
        
        return outputs


if __name__ == "__main__":
    # 测试代码
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模拟的MAE特征
    batch_size = 2
    feature_dim = 768
    spatial_size = 32  # 512/16 = 32
    
    vit_features = {
        'layer_3': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
        'layer_6': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
        'layer_9': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
        'layer_12': torch.randn(batch_size, feature_dim, spatial_size, spatial_size).to(device),
    }
    
    # 创建MAE UNet分割头
    seg_head = MAEUNetHead(
        feature_channels=768,
        num_classes=8,
        decoder_channels=(512, 256, 128, 64),
        use_attention=True,
        use_deep_supervision=True
    ).to(device)
    
    # 前向传播测试
    target_size = (512, 512)
    with torch.no_grad():
        outputs = seg_head(vit_features, target_size)
    
    print("MAE UNet分割头测试结果:")
    print(f"主要输出形状: {outputs['main'].shape}")
    if 'aux' in outputs:
        print(f"辅助输出数量: {len(outputs['aux'])}")
        for i, aux_out in enumerate(outputs['aux']):
            print(f"辅助输出{i+1}形状: {aux_out.shape}")
    
    # 计算参数量
    total_params = sum(p.numel() for p in seg_head.parameters())
    trainable_params = sum(p.numel() for p in seg_head.parameters() if p.requires_grad)
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    
    print("\n✅ MAE UNet混合分割头创建成功！")
