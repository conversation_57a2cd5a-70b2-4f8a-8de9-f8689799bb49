#!/usr/bin/env python3
"""
蜂巢图像分割数据集加载器
支持图像和掩码的加载、预处理和数据增强
"""

import os
import cv2
import numpy as np
from PIL import Image
import torch
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import torchvision.transforms.functional as TF
from pathlib import Path
from typing import Tuple, Optional, List, Dict, Any
import albumentations as A
from albumentations.pytorch import ToTensorV2
import logging
try:
    from ..config.data_config import DataConfig
except ImportError:
    from core.config.data_config import DataConfig

class HoneycombDataset(Dataset):
    """蜂巢图像分割数据集"""
    
    def __init__(
        self,
        images_dir: str,
        masks_dir: str,
        image_size: int = 224,
        normalize_mean: List[float] = None,
        normalize_std: List[float] = None,
        use_augmentation: bool = False,
        augmentation_config: Dict[str, Any] = None
    ):
        """
        Args:
            images_dir: 图像文件夹路径
            masks_dir: 掩码文件夹路径
            image_size: 目标图像尺寸
            normalize_mean: 归一化均值
            normalize_std: 归一化标准差
            use_augmentation: 是否使用数据增强
            augmentation_config: 数据增强配置
        """
        self.images_dir = Path(images_dir)
        self.masks_dir = Path(masks_dir)
        self.image_size = image_size
        self.use_augmentation = use_augmentation
        
        # 默认归一化参数（ImageNet）
        self.normalize_mean = normalize_mean or [0.485, 0.456, 0.406]
        self.normalize_std = normalize_std or [0.229, 0.224, 0.225]
        
        # 获取所有图像文件
        self.image_files = self._get_image_files()
        
        # 验证数据集
        self._validate_dataset()
        
        # 设置数据增强
        self.transform = self._setup_transforms(augmentation_config or {})
        
        logging.info(f"数据集初始化完成: {len(self.image_files)} 个样本")
    
    def _get_image_files(self) -> List[str]:
        """获取所有图像文件"""
        supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        image_files = []
        
        for file_path in self.images_dir.iterdir():
            if file_path.suffix.lower() in supported_formats:
                image_files.append(file_path.stem)
        
        return sorted(image_files)
    
    def _validate_dataset(self):
        """验证数据集完整性"""
        if not self.images_dir.exists():
            raise FileNotFoundError(f"图像目录不存在: {self.images_dir}")
        
        if not self.masks_dir.exists():
            raise FileNotFoundError(f"掩码目录不存在: {self.masks_dir}")
        
        if len(self.image_files) == 0:
            raise ValueError(f"图像目录为空: {self.images_dir}")
        
        # 检查每个图像是否有对应的掩码
        missing_masks = []
        for image_name in self.image_files:
            mask_path = self._get_mask_path(image_name)
            if not mask_path.exists():
                missing_masks.append(image_name)
        
        if missing_masks:
            logging.warning(f"以下图像缺少对应掩码: {missing_masks[:5]}...")
            # 移除缺少掩码的图像
            self.image_files = [name for name in self.image_files if name not in missing_masks]
        
        logging.info(f"数据集验证完成: {len(self.image_files)} 个有效样本")
    
    def _get_image_path(self, image_name: str) -> Path:
        """获取图像文件路径"""
        for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            path = self.images_dir / f"{image_name}{ext}"
            if path.exists():
                return path
        raise FileNotFoundError(f"找不到图像文件: {image_name}")
    
    def _get_mask_path(self, image_name: str) -> Path:
        """获取掩码文件路径"""
        # 首先尝试带_mask后缀的文件名
        for ext in ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']:
            path = self.masks_dir / f"{image_name}_mask{ext}"
            if path.exists():
                return path
        
        # 如果没找到，尝试不带后缀的原始文件名
        for ext in ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']:
            path = self.masks_dir / f"{image_name}{ext}"
            if path.exists():
                return path
        
        raise FileNotFoundError(f"找不到掩码文件: {image_name} (尝试了 {image_name}_mask 和 {image_name})")
    
    def _setup_transforms(self, aug_config: Dict[str, Any]):
        """设置数据变换"""
        if self.use_augmentation:
            # 使用Albumentations进行数据增强
            transform_list = [
                A.Resize(self.image_size, self.image_size),
                A.HorizontalFlip(p=aug_config.get('horizontal_flip_prob', 0.5)),
                A.VerticalFlip(p=aug_config.get('vertical_flip_prob', 0.5)),
                A.Rotate(
                    limit=aug_config.get('rotation_degrees', 15),
                    p=0.5,
                    border_mode=cv2.BORDER_CONSTANT,
                    value=0
                ),
                A.ColorJitter(
                    brightness=aug_config.get('color_jitter_brightness', 0.2),
                    contrast=aug_config.get('color_jitter_contrast', 0.2),
                    saturation=0.1,
                    hue=0.1,
                    p=0.5
                ),
                A.GaussianBlur(blur_limit=(3, 7), p=0.3),
                A.GaussNoise(var_limit=(10.0, 50.0), p=0.3),
                A.Normalize(mean=self.normalize_mean, std=self.normalize_std),
                ToTensorV2()
            ]
        else:
            # 仅基础变换
            transform_list = [
                A.Resize(self.image_size, self.image_size),
                A.Normalize(mean=self.normalize_mean, std=self.normalize_std),
                ToTensorV2()
            ]
        
        return A.Compose(transform_list)
    
    def __len__(self) -> int:
        return len(self.image_files)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, Dict[str, Any]]:
        """获取单个样本"""
        image_name = self.image_files[idx]
        
        try:
            # 加载图像
            image_path = self._get_image_path(image_name)
            image = cv2.imread(str(image_path))
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 加载掩码
            mask_path = self._get_mask_path(image_name)
            mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
            
            # 处理8类语义分割掩码（0-7直接像素值映射）
            # 正确的类别映射（基于真实数据分析）：
            # 0: background(39.39%), 1: capped_brood(10.81%), 2: eggs(0.34%), 3: honey(2.86%),
            # 4: honeycomb(42.37%), 5: larvae(0.65%), 6: nectar(2.44%), 7: pollen(1.14%)
            mask = mask.astype(np.uint8)
            # 确保掩码值在有效范围内 [0, 7]
            mask = np.clip(mask, 0, 7)
            
            # 应用变换
            transformed = self.transform(image=image, mask=mask)
            image_tensor = transformed['image']
            mask_tensor = transformed['mask'].long()
            
            # 元数据
            metadata = {
                'image_name': image_name,
                'image_path': str(image_path),
                'mask_path': str(mask_path),
                'original_size': image.shape[:2]
            }
            
            return image_tensor, mask_tensor, metadata
            
        except Exception as e:
            logging.error(f"加载样本失败 {image_name}: {e}")
            # 返回零张量作为fallback
            image_tensor = torch.zeros(3, self.image_size, self.image_size)
            mask_tensor = torch.zeros(self.image_size, self.image_size, dtype=torch.long)
            metadata = {'image_name': image_name, 'error': str(e)}
            return image_tensor, mask_tensor, metadata
    
    def get_class_weights(self) -> torch.Tensor:
        """计算8类语义分割的类别权重用于处理类别不平衡"""
        logging.info("计算8类语义分割的类别权重...")
        
        # 定义类别名称（正确的映射顺序）
        class_names = ['background', 'capped_brood', 'eggs', 'honey',
                      'honeycomb', 'larvae', 'nectar', 'pollen']
        
        pixel_counts = np.zeros(8)  # 8个类别
        
        for idx in range(min(len(self), 100)):  # 采样前100个样本
            _, mask, _ = self[idx]
            unique, counts = torch.unique(mask, return_counts=True)
            for cls, count in zip(unique, counts):
                if cls < 8:
                    pixel_counts[cls] += count.item()
        
        # 计算权重（逆频率）
        total_pixels = pixel_counts.sum()
        weights = total_pixels / (8 * pixel_counts + 1e-8)
        weights = weights / weights.sum() * 8  # 归一化
        
        # 记录每个类别的权重
        logging.info("8类语义分割类别权重:")
        for i, (name, weight) in enumerate(zip(class_names, weights)):
            logging.info(f"  {i}. {name}: {weight:.3f}")
        
        return torch.tensor(weights, dtype=torch.float32)

def create_dataloaders(
    train_images_dir: str,
    train_masks_dir: str,
    val_images_dir: str,
    val_masks_dir: str,
    batch_size: int = 8,
    num_workers: int = 4,
    pin_memory: bool = True,
    **dataset_kwargs
) -> Tuple[DataLoader, DataLoader]:
    """创建训练和验证数据加载器"""
    
    # 创建训练数据集（带数据增强）
    train_dataset = HoneycombDataset(
        images_dir=train_images_dir,
        masks_dir=train_masks_dir,
        use_augmentation=True,
        **dataset_kwargs
    )
    
    # 创建验证数据集（不带数据增强）
    val_dataset = HoneycombDataset(
        images_dir=val_images_dir,
        masks_dir=val_masks_dir,
        use_augmentation=False,
        **dataset_kwargs
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=False
    )
    
    logging.info(f"数据加载器创建完成:")
    logging.info(f"  训练集: {len(train_dataset)} 样本, {len(train_loader)} 批次")
    logging.info(f"  验证集: {len(val_dataset)} 样本, {len(val_loader)} 批次")
    
    return train_loader, val_loader

def test_dataset():
    """测试数据集加载"""
    import matplotlib.pyplot as plt
    
    # 创建测试数据集
    dataset = HoneycombDataset(
        images_dir="./data/train/images",
        masks_dir="./data/train/masks",
        image_size=224,
        use_augmentation=True
    )
    
    print(f"数据集大小: {len(dataset)}")
    
    if len(dataset) > 0:
        # 测试加载样本
        image, mask, metadata = dataset[0]
        print(f"图像形状: {image.shape}")
        print(f"掩码形状: {mask.shape}")
        print(f"掩码唯一值: {torch.unique(mask)}")
        print(f"元数据: {metadata}")
        
        # 可视化样本
        fig, axes = plt.subplots(1, 2, figsize=(10, 5))
        
        # 反归一化图像用于显示
        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
        image_denorm = image * std + mean
        image_denorm = torch.clamp(image_denorm, 0, 1)
        
        axes[0].imshow(image_denorm.permute(1, 2, 0))
        axes[0].set_title('图像')
        axes[0].axis('off')
        
        axes[1].imshow(mask, cmap='gray')
        axes[1].set_title('掩码')
        axes[1].axis('off')
        
        plt.tight_layout()
        plt.savefig('dataset_sample.png')
        print("样本可视化已保存到 dataset_sample.png")

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 测试数据集
    test_dataset()