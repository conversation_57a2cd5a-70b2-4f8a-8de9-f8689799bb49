#!/usr/bin/env python3
"""
数据集工具函数
包含数据集划分、数据验证和数据统计等功能
"""

import os
import shutil
import random
from pathlib import Path
from typing import List, Tuple, Dict, Optional
import logging
from collections import defaultdict
import numpy as np
from PIL import Image
import torch
from torch.utils.data import Dataset, DataLoader


class DataUtils:
    """数据工具类"""
    
    @staticmethod
    def split_dataset(
        source_images_dir: str,
        source_masks_dir: str,
        output_dir: str,
        train_ratio: float = 0.7,
        val_ratio: float = 0.2,
        test_ratio: float = 0.1,
        random_seed: int = 42,
        copy_files: bool = True
    ) -> Dict[str, List[str]]:
        """
        划分数据集
        
        Args:
            source_images_dir: 源图像目录
            source_masks_dir: 源掩码目录
            output_dir: 输出目录
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            test_ratio: 测试集比例
            random_seed: 随机种子
            copy_files: 是否复制文件
            
        Returns:
            Dict[str, List[str]]: 划分结果
        """
        # 验证比例
        assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-6, \
            "比例之和必须等于1.0"
        
        source_images_dir = Path(source_images_dir)
        source_masks_dir = Path(source_masks_dir)
        output_dir = Path(output_dir)
        
        # 获取所有图像文件
        image_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            image_files.extend(source_images_dir.glob(f'*{ext}'))
            image_files.extend(source_images_dir.glob(f'*{ext.upper()}'))
        
        # 验证对应的掩码文件存在
        valid_pairs = []
        for img_file in image_files:
            mask_file = source_masks_dir / img_file.name
            if mask_file.exists():
                valid_pairs.append((img_file, mask_file))
        
        if not valid_pairs:
            raise ValueError("未找到有效的图像-掩码对")
        
        # 随机打乱
        random.seed(random_seed)
        random.shuffle(valid_pairs)
        
        # 计算划分点
        total_count = len(valid_pairs)
        train_count = int(total_count * train_ratio)
        val_count = int(total_count * val_ratio)
        
        # 划分数据
        train_pairs = valid_pairs[:train_count]
        val_pairs = valid_pairs[train_count:train_count + val_count]
        test_pairs = valid_pairs[train_count + val_count:]
        
        splits = {
            'train': train_pairs,
            'val': val_pairs,
            'test': test_pairs
        }
        
        # 创建输出目录并复制文件
        if copy_files:
            for split_name, pairs in splits.items():
                # 创建目录
                images_dir = output_dir / split_name / 'images'
                masks_dir = output_dir / split_name / 'masks'
                images_dir.mkdir(parents=True, exist_ok=True)
                masks_dir.mkdir(parents=True, exist_ok=True)
                
                # 复制文件
                for img_file, mask_file in pairs:
                    shutil.copy2(img_file, images_dir / img_file.name)
                    shutil.copy2(mask_file, masks_dir / mask_file.name)
        
        # 返回文件列表
        result = {}
        for split_name, pairs in splits.items():
            result[split_name] = [str(pair[0].name) for pair in pairs]
        
        return result
    
    @staticmethod
    def validate_dataset(
        images_dir: str,
        masks_dir: str,
        check_size: bool = True,
        check_format: bool = True
    ) -> Dict[str, any]:
        """
        验证数据集
        
        Args:
            images_dir: 图像目录
            masks_dir: 掩码目录
            check_size: 是否检查尺寸匹配
            check_format: 是否检查格式
            
        Returns:
            Dict: 验证结果
        """
        images_dir = Path(images_dir)
        masks_dir = Path(masks_dir)
        
        # 获取文件列表
        image_files = list(images_dir.glob('*'))
        mask_files = list(masks_dir.glob('*'))
        
        # 基础统计
        stats = {
            'total_images': len(image_files),
            'total_masks': len(mask_files),
            'matched_pairs': 0,
            'size_mismatches': [],
            'format_errors': [],
            'missing_masks': [],
            'missing_images': []
        }
        
        # 检查配对
        for img_file in image_files:
            mask_file = masks_dir / img_file.name
            if mask_file.exists():
                stats['matched_pairs'] += 1
                
                if check_size or check_format:
                    try:
                        img = Image.open(img_file)
                        mask = Image.open(mask_file)
                        
                        if check_size and img.size != mask.size:
                            stats['size_mismatches'].append({
                                'file': img_file.name,
                                'img_size': img.size,
                                'mask_size': mask.size
                            })
                        
                        if check_format:
                            # 检查掩码是否为单通道
                            if mask.mode not in ['L', 'P']:
                                stats['format_errors'].append({
                                    'file': mask_file.name,
                                    'mode': mask.mode,
                                    'expected': 'L or P'
                                })
                        
                    except Exception as e:
                        stats['format_errors'].append({
                            'file': img_file.name,
                            'error': str(e)
                        })
            else:
                stats['missing_masks'].append(img_file.name)
        
        # 检查孤立的掩码文件
        for mask_file in mask_files:
            img_file = images_dir / mask_file.name
            if not img_file.exists():
                stats['missing_images'].append(mask_file.name)
        
        return stats
    
    @staticmethod
    def analyze_dataset_distribution(
        masks_dir: str,
        num_classes: int = 8,
        sample_ratio: float = 0.1
    ) -> Dict[str, any]:
        """
        分析数据集类别分布
        
        Args:
            masks_dir: 掩码目录
            num_classes: 类别数量
            sample_ratio: 采样比例
            
        Returns:
            Dict: 分布统计
        """
        masks_dir = Path(masks_dir)
        mask_files = list(masks_dir.glob('*'))
        
        # 采样文件
        if sample_ratio < 1.0:
            sample_size = max(1, int(len(mask_files) * sample_ratio))
            mask_files = random.sample(mask_files, sample_size)
        
        # 统计类别分布
        class_counts = defaultdict(int)
        total_pixels = 0
        
        for mask_file in mask_files:
            try:
                mask = np.array(Image.open(mask_file))
                unique, counts = np.unique(mask, return_counts=True)
                
                for class_id, count in zip(unique, counts):
                    if 0 <= class_id < num_classes:
                        class_counts[class_id] += count
                        total_pixels += count
                        
            except Exception as e:
                print(f"处理文件 {mask_file} 时出错: {e}")
        
        # 计算比例
        class_ratios = {}
        for class_id in range(num_classes):
            count = class_counts.get(class_id, 0)
            ratio = count / total_pixels if total_pixels > 0 else 0
            class_ratios[class_id] = {
                'count': count,
                'ratio': ratio,
                'percentage': ratio * 100
            }
        
        return {
            'total_pixels': total_pixels,
            'total_files_analyzed': len(mask_files),
            'class_distribution': class_ratios,
            'most_common_class': max(class_counts, key=class_counts.get) if class_counts else None,
            'least_common_class': min(class_counts, key=class_counts.get) if class_counts else None
        }
    
    @staticmethod
    def create_class_weights(
        masks_dir: str,
        num_classes: int = 8,
        method: str = 'inverse_frequency',
        smooth_factor: float = 0.1
    ) -> torch.Tensor:
        """
        创建类别权重用于损失函数
        
        Args:
            masks_dir: 掩码目录
            num_classes: 类别数量
            method: 权重计算方法 ('inverse_frequency', 'effective_number')
            smooth_factor: 平滑因子
            
        Returns:
            torch.Tensor: 类别权重
        """
        # 分析类别分布
        distribution = DataUtils.analyze_dataset_distribution(
            masks_dir, num_classes, sample_ratio=0.2
        )
        
        class_counts = []
        for i in range(num_classes):
            count = distribution['class_distribution'][i]['count']
            class_counts.append(count)
        
        class_counts = np.array(class_counts)
        
        if method == 'inverse_frequency':
            # 逆频率权重
            total_samples = np.sum(class_counts)
            weights = total_samples / (num_classes * (class_counts + smooth_factor))
            
        elif method == 'effective_number':
            # 有效样本数权重
            beta = 0.9999
            effective_num = 1.0 - np.power(beta, class_counts)
            weights = (1.0 - beta) / effective_num
            
        else:
            raise ValueError(f"不支持的权重计算方法: {method}")
        
        # 归一化权重
        weights = weights / np.sum(weights) * num_classes
        
        return torch.FloatTensor(weights)
    
    @staticmethod
    def get_dataset_info(data_dir: str) -> Dict[str, any]:
        """
        获取数据集基本信息
        
        Args:
            data_dir: 数据集根目录
            
        Returns:
            Dict: 数据集信息
        """
        data_dir = Path(data_dir)
        
        info = {
            'splits': {},
            'total_files': 0,
            'directory_structure': []
        }
        
        # 检查标准分割
        for split in ['train', 'val', 'test']:
            split_dir = data_dir / split
            if split_dir.exists():
                images_dir = split_dir / 'images'
                masks_dir = split_dir / 'masks'
                
                if images_dir.exists() and masks_dir.exists():
                    img_count = len(list(images_dir.glob('*')))
                    mask_count = len(list(masks_dir.glob('*')))
                    
                    info['splits'][split] = {
                        'images': img_count,
                        'masks': mask_count,
                        'matched': min(img_count, mask_count)
                    }
                    info['total_files'] += min(img_count, mask_count)
        
        # 记录目录结构
        for item in data_dir.rglob('*'):
            if item.is_dir():
                rel_path = item.relative_to(data_dir)
                info['directory_structure'].append(str(rel_path))
        
        return info
