#!/usr/bin/env python3
"""
混合数据加载器
支持有标签数据和伪标签数据的混合训练
"""

import random
import logging
from typing import Iterator, Optional, Tuple, Union
import torch
from torch.utils.data import Dataset, DataLoader, Sampler
import numpy as np

logger = logging.getLogger(__name__)


class MixedBatchSampler(Sampler):
    """混合批次采样器
    
    在每个批次中混合有标签数据和伪标签数据
    """
    
    def __init__(self, 
                 labeled_dataset_size: int,
                 pseudo_dataset_size: int,
                 batch_size: int,
                 pseudo_ratio: float = 0.5,
                 drop_last: bool = True):
        """
        Args:
            labeled_dataset_size: 有标签数据集大小
            pseudo_dataset_size: 伪标签数据集大小
            batch_size: 批次大小
            pseudo_ratio: 伪标签数据在批次中的比例
            drop_last: 是否丢弃最后不完整的批次
        """
        self.labeled_size = labeled_dataset_size
        self.pseudo_size = pseudo_dataset_size
        self.batch_size = batch_size
        self.pseudo_ratio = pseudo_ratio
        self.drop_last = drop_last
        
        # 计算每个批次中有标签和伪标签的数量
        self.pseudo_per_batch = int(batch_size * pseudo_ratio)
        self.labeled_per_batch = batch_size - self.pseudo_per_batch
        
        # 计算总批次数
        if pseudo_dataset_size == 0:
            # 没有伪标签数据，只使用有标签数据
            self.num_batches = labeled_dataset_size // batch_size
            self.pseudo_per_batch = 0
            self.labeled_per_batch = batch_size
        else:
            # 根据较小的数据集确定批次数
            labeled_batches = labeled_dataset_size // self.labeled_per_batch
            pseudo_batches = pseudo_dataset_size // self.pseudo_per_batch
            self.num_batches = min(labeled_batches, pseudo_batches)
        
        if not drop_last and pseudo_dataset_size > 0:
            # 如果不丢弃最后的批次，需要重新计算
            self.num_batches = max(
                (labeled_dataset_size + self.labeled_per_batch - 1) // self.labeled_per_batch,
                (pseudo_dataset_size + self.pseudo_per_batch - 1) // self.pseudo_per_batch
            )
        
        logger.info(f"混合采样器配置:")
        logger.info(f"  有标签数据: {labeled_dataset_size}, 每批次: {self.labeled_per_batch}")
        logger.info(f"  伪标签数据: {pseudo_dataset_size}, 每批次: {self.pseudo_per_batch}")
        logger.info(f"  总批次数: {self.num_batches}")
    
    def __iter__(self) -> Iterator[Tuple[str, int]]:
        """返回批次索引迭代器
        
        Returns:
            Iterator yielding (data_type, index) tuples
            data_type: 'labeled' or 'pseudo'
        """
        # 创建索引列表
        labeled_indices = list(range(self.labeled_size))
        pseudo_indices = list(range(self.pseudo_size))
        
        # 打乱索引
        random.shuffle(labeled_indices)
        random.shuffle(pseudo_indices)
        
        labeled_idx = 0
        pseudo_idx = 0
        
        for batch_num in range(self.num_batches):
            batch_indices = []
            
            # 添加有标签数据索引
            for _ in range(self.labeled_per_batch):
                if labeled_idx < len(labeled_indices):
                    batch_indices.append(('labeled', labeled_indices[labeled_idx]))
                    labeled_idx += 1
                else:
                    # 如果有标签数据用完了，重新开始
                    random.shuffle(labeled_indices)
                    labeled_idx = 0
                    if labeled_indices:
                        batch_indices.append(('labeled', labeled_indices[labeled_idx]))
                        labeled_idx += 1
            
            # 添加伪标签数据索引
            for _ in range(self.pseudo_per_batch):
                if pseudo_idx < len(pseudo_indices):
                    batch_indices.append(('pseudo', pseudo_indices[pseudo_idx]))
                    pseudo_idx += 1
                else:
                    # 如果伪标签数据用完了，重新开始
                    random.shuffle(pseudo_indices)
                    pseudo_idx = 0
                    if pseudo_indices:
                        batch_indices.append(('pseudo', pseudo_indices[pseudo_idx]))
                        pseudo_idx += 1
            
            # 打乱批次内的顺序
            random.shuffle(batch_indices)
            
            for item in batch_indices:
                yield item
    
    def __len__(self) -> int:
        return self.num_batches * self.batch_size


class MixedDataset(Dataset):
    """混合数据集
    
    将有标签数据集和伪标签数据集组合
    """
    
    def __init__(self, labeled_dataset: Dataset, pseudo_dataset: Optional[Dataset] = None):
        self.labeled_dataset = labeled_dataset
        self.pseudo_dataset = pseudo_dataset
        
        self.labeled_size = len(labeled_dataset)
        self.pseudo_size = len(pseudo_dataset) if pseudo_dataset else 0
        
        logger.info(f"混合数据集: 有标签 {self.labeled_size}, 伪标签 {self.pseudo_size}")
    
    def __len__(self):
        return self.labeled_size + self.pseudo_size
    
    def __getitem__(self, item):
        """根据采样器提供的信息获取数据"""
        if isinstance(item, tuple):
            data_type, index = item
            if data_type == 'labeled':
                return self.labeled_dataset[index], 'labeled'
            elif data_type == 'pseudo':
                return self.pseudo_dataset[index], 'pseudo'
            else:
                raise ValueError(f"未知的数据类型: {data_type}")
        else:
            # 兼容普通索引访问
            if item < self.labeled_size:
                return self.labeled_dataset[item], 'labeled'
            else:
                return self.pseudo_dataset[item - self.labeled_size], 'pseudo'


class MixedDataLoader:
    """混合数据加载器
    
    管理有标签数据和伪标签数据的混合加载
    """
    
    def __init__(self,
                 labeled_dataset: Dataset,
                 pseudo_dataset: Optional[Dataset] = None,
                 batch_size: int = 32,
                 pseudo_ratio: float = 0.5,
                 num_workers: int = 4,
                 pin_memory: bool = True,
                 drop_last: bool = True):
        """
        Args:
            labeled_dataset: 有标签数据集
            pseudo_dataset: 伪标签数据集
            batch_size: 批次大小
            pseudo_ratio: 伪标签数据比例
            num_workers: 工作进程数
            pin_memory: 是否固定内存
            drop_last: 是否丢弃最后不完整的批次
        """
        self.labeled_dataset = labeled_dataset
        self.pseudo_dataset = pseudo_dataset
        self.batch_size = batch_size
        self.pseudo_ratio = pseudo_ratio if pseudo_dataset else 0.0
        
        # 如果没有伪标签数据，使用普通的DataLoader
        if not pseudo_dataset:
            self.dataloader = DataLoader(
                labeled_dataset,
                batch_size=batch_size,
                shuffle=True,
                num_workers=num_workers,
                pin_memory=pin_memory,
                drop_last=drop_last
            )
            self.is_mixed = False
        else:
            # 创建混合数据集和采样器
            mixed_dataset = MixedDataset(labeled_dataset, pseudo_dataset)
            
            sampler = MixedBatchSampler(
                labeled_dataset_size=len(labeled_dataset),
                pseudo_dataset_size=len(pseudo_dataset),
                batch_size=batch_size,
                pseudo_ratio=pseudo_ratio,
                drop_last=drop_last
            )
            
            self.dataloader = DataLoader(
                mixed_dataset,
                batch_sampler=sampler,
                num_workers=num_workers,
                pin_memory=pin_memory,
                collate_fn=self._collate_fn
            )
            self.is_mixed = True
    
    def _collate_fn(self, batch):
        """自定义批次整理函数"""
        labeled_items = []
        pseudo_items = []
        
        for (data, data_type) in batch:
            if data_type == 'labeled':
                labeled_items.append(data)
            elif data_type == 'pseudo':
                pseudo_items.append(data)
        
        # 分别整理有标签和伪标签数据
        labeled_batch = None
        pseudo_batch = None
        
        if labeled_items:
            # 假设数据格式为 (image, mask, ...)
            labeled_images = torch.stack([item[0] for item in labeled_items])
            labeled_masks = torch.stack([item[1] for item in labeled_items])
            labeled_batch = (labeled_images, labeled_masks)
            
            # 如果有额外信息（如路径），也要处理
            if len(labeled_items[0]) > 2:
                labeled_extras = [item[2:] for item in labeled_items]
                labeled_batch = labeled_batch + tuple(zip(*labeled_extras))
        
        if pseudo_items:
            pseudo_images = torch.stack([item[0] for item in pseudo_items])
            pseudo_masks = torch.stack([item[1] for item in pseudo_items])
            pseudo_batch = (pseudo_images, pseudo_masks)
            
            if len(pseudo_items[0]) > 2:
                pseudo_extras = [item[2:] for item in pseudo_items]
                pseudo_batch = pseudo_batch + tuple(zip(*pseudo_extras))
        
        return {
            'labeled': labeled_batch,
            'pseudo': pseudo_batch,
            'labeled_size': len(labeled_items) if labeled_items else 0,
            'pseudo_size': len(pseudo_items) if pseudo_items else 0
        }
    
    def __iter__(self):
        if self.is_mixed:
            return iter(self.dataloader)
        else:
            # 对于非混合模式，包装返回格式以保持一致性
            for batch in self.dataloader:
                yield {
                    'labeled': batch,
                    'pseudo': None,
                    'labeled_size': len(batch[0]),
                    'pseudo_size': 0
                }
    
    def __len__(self):
        return len(self.dataloader)
    
    def update_pseudo_dataset(self, new_pseudo_dataset: Optional[Dataset]):
        """更新伪标签数据集"""
        if new_pseudo_dataset is None:
            # 切换到非混合模式
            self.dataloader = DataLoader(
                self.labeled_dataset,
                batch_size=self.batch_size,
                shuffle=True,
                num_workers=self.dataloader.num_workers,
                pin_memory=self.dataloader.pin_memory,
                drop_last=True
            )
            self.is_mixed = False
            logger.info("切换到非混合模式（无伪标签数据）")
        else:
            # 重新创建混合数据加载器
            self.pseudo_dataset = new_pseudo_dataset
            mixed_dataset = MixedDataset(self.labeled_dataset, new_pseudo_dataset)
            
            sampler = MixedBatchSampler(
                labeled_dataset_size=len(self.labeled_dataset),
                pseudo_dataset_size=len(new_pseudo_dataset),
                batch_size=self.batch_size,
                pseudo_ratio=self.pseudo_ratio,
                drop_last=True
            )
            
            self.dataloader = DataLoader(
                mixed_dataset,
                batch_sampler=sampler,
                num_workers=self.dataloader.num_workers,
                pin_memory=self.dataloader.pin_memory,
                collate_fn=self._collate_fn
            )
            self.is_mixed = True
            logger.info(f"更新混合数据加载器: 伪标签数据 {len(new_pseudo_dataset)} 个")


def create_mixed_dataloader(labeled_dataset: Dataset,
                           pseudo_dataset: Optional[Dataset] = None,
                           batch_size: int = 32,
                           pseudo_ratio: float = 0.5,
                           num_workers: int = 4) -> MixedDataLoader:
    """创建混合数据加载器的便捷函数"""
    return MixedDataLoader(
        labeled_dataset=labeled_dataset,
        pseudo_dataset=pseudo_dataset,
        batch_size=batch_size,
        pseudo_ratio=pseudo_ratio,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True
    )
