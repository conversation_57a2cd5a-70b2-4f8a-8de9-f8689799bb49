#!/bin/bash

# 语义分割训练启动脚本
# 使用正确的类别映射和优化的权重配置

echo "🚀 启动语义分割训练"
echo "配置文件: configs/semantic_segmentation_config_correct.yaml"
echo "=========================================="

# 检查配置文件
if [ ! -f "configs/semantic_segmentation_config_correct.yaml" ]; then
    echo "❌ 配置文件不存在"
    exit 1
fi

# 检查MAE权重
MAE_WEIGHTS="/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/best_checkpoint_epoch_1556.pth"
if [ ! -f "$MAE_WEIGHTS" ]; then
    echo "❌ MAE权重文件不存在: $MAE_WEIGHTS"
    exit 1
fi

# 创建输出目录
mkdir -p outputs/semantic_segmentation_correct

echo "✅ 环境检查通过"
echo "🎯 真实数据分布:"
echo "  honeycomb: 42.37% (权重: 2.2)"
echo "  background: 39.39% (权重: 2.3)" 
echo "  capped_brood: 10.81% (权重: 4.4)"
echo "  eggs: 0.34% (权重: 50.0)"
echo "  larvae: 0.65% (权重: 36.2)"
echo "=========================================="

# 启动训练
python scripts/train_semantic_segmentation.py \
    --config configs/semantic_segmentation_config_correct.yaml
