#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化工具
用于保存训练过程中的可视化结果
"""

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import seaborn as sns
from PIL import Image
import logging

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class SegmentationVisualizer:
    """分割可视化器"""
    
    def __init__(self, num_classes: int = 3):
        """
        初始化可视化器
        
        Args:
            num_classes: 类别数量
        """
        self.num_classes = num_classes
        self.class_names = ['Background', 'Honeycomb', 'Honey']
        self.class_colors = [
            [0, 0, 0],        # 背景 - 黑色
            [255, 255, 0],    # 蜂巢 - 黄色
            [255, 165, 0]     # 蜂蜜 - 橙色
        ]
        
        # 创建颜色映射
        self.colormap = ListedColormap([np.array(color)/255.0 for color in self.class_colors])
    
    def denormalize_image(self, tensor: torch.Tensor) -> np.ndarray:
        """反归一化图像"""
        # ImageNet标准化参数
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        
        # 转换为numpy
        if isinstance(tensor, torch.Tensor):
            image = tensor.cpu().numpy()
        else:
            image = tensor
        
        # 反归一化
        if image.ndim == 3 and image.shape[0] == 3:  # (C, H, W)
            image = image.transpose(1, 2, 0)  # (H, W, C)
        
        image = image * std + mean
        image = np.clip(image, 0, 1)
        
        return image
    
    def mask_to_color(self, mask: np.ndarray) -> np.ndarray:
        """将掩码转换为彩色图像"""
        if isinstance(mask, torch.Tensor):
            mask = mask.cpu().numpy()
        
        # 确保mask是2D
        if mask.ndim == 3:
            mask = mask.squeeze()
        
        # 创建彩色图像
        h, w = mask.shape
        color_mask = np.zeros((h, w, 3), dtype=np.uint8)
        
        for class_id in range(self.num_classes):
            class_mask = (mask == class_id)
            color_mask[class_mask] = self.class_colors[class_id]
        
        return color_mask
    
    def create_overlay(
        self,
        image: np.ndarray,
        mask: np.ndarray,
        alpha: float = 0.5
    ) -> np.ndarray:
        """创建图像和掩码的叠加"""
        # 确保图像是0-255范围
        if image.max() <= 1.0:
            image = (image * 255).astype(np.uint8)
        
        # 转换掩码为彩色
        color_mask = self.mask_to_color(mask)
        
        # 叠加
        overlay = cv2.addWeighted(image, 1-alpha, color_mask, alpha, 0)
        
        return overlay
    
    def visualize_predictions(
        self,
        images: torch.Tensor,
        predictions: torch.Tensor,
        targets: torch.Tensor,
        save_path: str,
        max_samples: int = 8,
        title: str = "Segmentation Results"
    ):
        """
        可视化预测结果
        
        Args:
            images: 输入图像 (N, C, H, W)
            predictions: 预测结果 (N, C, H, W) 或 (N, H, W)
            targets: 真实标签 (N, H, W)
            save_path: 保存路径
            max_samples: 最大显示样本数
            title: 图像标题
        """
        # 限制样本数量
        batch_size = min(images.shape[0], max_samples)
        
        # 转换预测为类别索引
        if predictions.dim() == 4:
            pred_classes = torch.argmax(predictions, dim=1)
        else:
            pred_classes = predictions
        
        # 创建子图
        fig, axes = plt.subplots(batch_size, 4, figsize=(16, 4*batch_size))
        if batch_size == 1:
            axes = axes.reshape(1, -1)
        
        for i in range(batch_size):
            # 原始图像
            image = self.denormalize_image(images[i])
            axes[i, 0].imshow(image)
            axes[i, 0].set_title('Original Image')
            axes[i, 0].axis('off')
            
            # 真实标签
            target_color = self.mask_to_color(targets[i])
            axes[i, 1].imshow(target_color)
            axes[i, 1].set_title('Ground Truth')
            axes[i, 1].axis('off')
            
            # 预测结果
            pred_color = self.mask_to_color(pred_classes[i])
            axes[i, 2].imshow(pred_color)
            axes[i, 2].set_title('Prediction')
            axes[i, 2].axis('off')
            
            # 叠加图像
            overlay = self.create_overlay(
                (image * 255).astype(np.uint8),
                pred_classes[i].cpu().numpy()
            )
            axes[i, 3].imshow(overlay)
            axes[i, 3].set_title('Overlay')
            axes[i, 3].axis('off')
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        logging.info(f"预测可视化已保存: {save_path}")
    
    def visualize_pseudo_labels(
        self,
        images: torch.Tensor,
        teacher_predictions: torch.Tensor,
        pseudo_labels: torch.Tensor,
        confidence_masks: torch.Tensor,
        save_path: str,
        max_samples: int = 6,
        title: str = "Pseudo Labels"
    ):
        """
        可视化伪标签生成过程
        
        Args:
            images: 输入图像 (N, C, H, W)
            teacher_predictions: 教师模型预测 (N, C, H, W)
            pseudo_labels: 伪标签 (N, H, W)
            confidence_masks: 置信度掩码 (N, H, W)
            save_path: 保存路径
            max_samples: 最大显示样本数
            title: 图像标题
        """
        batch_size = min(images.shape[0], max_samples)
        
        # 转换教师预测为概率
        teacher_probs = F.softmax(teacher_predictions, dim=1)
        max_probs, _ = torch.max(teacher_probs, dim=1)
        
        # 创建子图
        fig, axes = plt.subplots(batch_size, 5, figsize=(20, 4*batch_size))
        if batch_size == 1:
            axes = axes.reshape(1, -1)
        
        for i in range(batch_size):
            # 原始图像
            image = self.denormalize_image(images[i])
            axes[i, 0].imshow(image)
            axes[i, 0].set_title('Original Image')
            axes[i, 0].axis('off')
            
            # 教师预测置信度
            conf_map = max_probs[i].cpu().numpy()
            im1 = axes[i, 1].imshow(conf_map, cmap='viridis', vmin=0, vmax=1)
            axes[i, 1].set_title('Teacher Confidence')
            axes[i, 1].axis('off')
            plt.colorbar(im1, ax=axes[i, 1], fraction=0.046, pad=0.04)
            
            # 置信度掩码
            conf_mask = confidence_masks[i].cpu().numpy()
            axes[i, 2].imshow(conf_mask, cmap='gray', vmin=0, vmax=1)
            axes[i, 2].set_title('Confidence Mask')
            axes[i, 2].axis('off')
            
            # 伪标签
            pseudo_color = self.mask_to_color(pseudo_labels[i])
            axes[i, 3].imshow(pseudo_color)
            axes[i, 3].set_title('Pseudo Labels')
            axes[i, 3].axis('off')
            
            # 掩码后的伪标签
            masked_pseudo = pseudo_labels[i].cpu().numpy() * conf_mask
            masked_color = self.mask_to_color(masked_pseudo)
            axes[i, 4].imshow(masked_color)
            axes[i, 4].set_title('Masked Pseudo Labels')
            axes[i, 4].axis('off')
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        logging.info(f"伪标签可视化已保存: {save_path}")
    
    def plot_training_curves(
        self,
        training_stats: List[Dict[str, Any]],
        save_path: str,
        title: str = "Training Curves"
    ):
        """
        绘制训练曲线
        
        Args:
            training_stats: 训练统计信息列表
            save_path: 保存路径
            title: 图像标题
        """
        if not training_stats:
            return
        
        # 提取数据
        epochs = [stat['epoch'] for stat in training_stats]
        
        # 提取损失数据
        train_losses = []
        val_losses = []
        val_mious = []
        learning_rates = []
        
        for stat in training_stats:
            # 训练损失
            if 'train' in stat and 'total' in stat['train']:
                train_losses.append(stat['train']['total'])
            else:
                train_losses.append(np.nan)
            
            # 验证损失和mIoU
            if 'val' in stat and stat['val']:
                val_losses.append(stat['val'].get('loss', np.nan))
                val_mious.append(stat['val'].get('miou', np.nan))
            else:
                val_losses.append(np.nan)
                val_mious.append(np.nan)
            
            # 学习率
            learning_rates.append(stat.get('lr', np.nan))
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 损失曲线
        axes[0, 0].plot(epochs, train_losses, label='Train Loss', color='blue')
        valid_val_losses = [x for x in val_losses if not np.isnan(x)]
        valid_val_epochs = [epochs[i] for i, x in enumerate(val_losses) if not np.isnan(x)]
        if valid_val_losses:
            axes[0, 0].plot(valid_val_epochs, valid_val_losses, label='Val Loss', color='red')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].set_title('Training and Validation Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # mIoU曲线
        valid_mious = [x for x in val_mious if not np.isnan(x)]
        valid_miou_epochs = [epochs[i] for i, x in enumerate(val_mious) if not np.isnan(x)]
        if valid_mious:
            axes[0, 1].plot(valid_miou_epochs, valid_mious, label='Val mIoU', color='green')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('mIoU')
            axes[0, 1].set_title('Validation mIoU')
            axes[0, 1].legend()
            axes[0, 1].grid(True)
        
        # 学习率曲线
        valid_lrs = [x for x in learning_rates if not np.isnan(x)]
        valid_lr_epochs = [epochs[i] for i, x in enumerate(learning_rates) if not np.isnan(x)]
        if valid_lrs:
            axes[1, 0].plot(valid_lr_epochs, valid_lrs, label='Learning Rate', color='orange')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Learning Rate')
            axes[1, 0].set_title('Learning Rate Schedule')
            axes[1, 0].set_yscale('log')
            axes[1, 0].legend()
            axes[1, 0].grid(True)
        
        # 掩码比例（如果有的话）
        mask_ratios = []
        for stat in training_stats:
            if 'train' in stat and 'mask_ratio' in stat['train']:
                mask_ratios.append(stat['train']['mask_ratio'])
            else:
                mask_ratios.append(np.nan)
        
        valid_mask_ratios = [x for x in mask_ratios if not np.isnan(x)]
        valid_mask_epochs = [epochs[i] for i, x in enumerate(mask_ratios) if not np.isnan(x)]
        if valid_mask_ratios:
            axes[1, 1].plot(valid_mask_epochs, valid_mask_ratios, label='Mask Ratio', color='purple')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Mask Ratio')
            axes[1, 1].set_title('Pseudo Label Mask Ratio')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        logging.info(f"训练曲线已保存: {save_path}")

def save_training_visualizations(
    trainer,
    epoch: int,
    labeled_batch: Dict[str, torch.Tensor],
    unlabeled_batch: Dict[str, torch.Tensor],
    save_dir: Path
):
    """
    保存训练过程中的可视化
    
    Args:
        trainer: 训练器实例
        epoch: 当前轮数
        labeled_batch: 有标注数据批次
        unlabeled_batch: 无标注数据批次
        save_dir: 保存目录
    """
    visualizer = SegmentationVisualizer(trainer.config['data']['num_classes'])
    
    # 创建可视化目录
    vis_dir = save_dir / 'visualizations' / f'epoch_{epoch:04d}'
    vis_dir.mkdir(parents=True, exist_ok=True)
    
    with torch.no_grad():
        # 1. 可视化有标注数据的预测
        labeled_images = labeled_batch['image'].to(trainer.device)
        labeled_masks = labeled_batch['mask'].to(trainer.device)
        
        labeled_outputs = trainer.student_model(labeled_images)
        labeled_predictions = labeled_outputs['main']
        
        visualizer.visualize_predictions(
            labeled_images[:4],  # 只显示前4个样本
            labeled_predictions[:4],
            labeled_masks[:4],
            str(vis_dir / 'labeled_predictions.png'),
            title=f'Labeled Data Predictions - Epoch {epoch}'
        )
        
        # 2. 可视化伪标签生成
        if unlabeled_batch:
            unlabeled_images = unlabeled_batch['image'].to(trainer.device)
            
            # 教师模型预测
            teacher_outputs = trainer.teacher_model(unlabeled_images)
            teacher_predictions = teacher_outputs['main']
            
            # 生成伪标签
            pseudo_labels, confidence_masks = trainer.criterion.generate_pseudo_labels(
                teacher_predictions,
                trainer.config['ssl']['confidence_threshold']
            )
            
            visualizer.visualize_pseudo_labels(
                unlabeled_images[:4],
                teacher_predictions[:4],
                pseudo_labels[:4],
                confidence_masks[:4],
                str(vis_dir / 'pseudo_labels.png'),
                title=f'Pseudo Labels Generation - Epoch {epoch}'
            )
    
    # 3. 绘制训练曲线
    if trainer.training_stats:
        visualizer.plot_training_curves(
            trainer.training_stats,
            str(vis_dir / 'training_curves.png'),
            title=f'Training Progress - Epoch {epoch}'
        )
