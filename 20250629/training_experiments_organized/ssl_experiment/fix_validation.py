#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证问题修复脚本
修复SSL训练中的验证部分问题
"""

import sys
import torch
import numpy as np
from pathlib import Path

def fix_ssl_trainer_validation():
    """修复SSL训练器中的验证方法"""
    
    trainer_file = Path(__file__).parent / "training" / "ssl_trainer.py"
    
    # 读取原文件
    with open(trainer_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复验证方法
    old_validation_method = '''    def _validate_epoch(self, val_loader: DataLoader) -> Dict[str, float]:
        """验证一个epoch"""
        self.student_model.eval()

        total_loss = 0.0
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for batch in val_loader:
                # 验证数据也是元组格式 (image, mask, metadata)
                images = batch[0].to(self.device)
                masks = batch[1].to(self.device)

                # 前向传播
                outputs = self.student_model(images)
                predictions = outputs['main']

                # 计算监督损失
                supervised_loss = self.supervised_criterion(predictions, masks)
                total_loss += supervised_loss.item()

                # 收集预测和真实标签
                pred_classes = torch.argmax(predictions, dim=1)
                all_predictions.append(pred_classes.cpu())
                all_targets.append(masks.cpu())

        # 计算指标
        all_predictions = torch.cat(all_predictions, dim=0)
        all_targets = torch.cat(all_targets, dim=0)

        metrics = self.metrics.compute_metrics(all_predictions, all_targets)
        metrics['loss'] = total_loss / len(val_loader)

        # 生成并打印详细的类别指标报告
        detailed_report = self.metrics.get_detailed_report(all_predictions, all_targets)
        print(detailed_report)
        logging.info(detailed_report)

        return metrics'''
    
    new_validation_method = '''    def _validate_epoch(self, val_loader: DataLoader) -> Dict[str, float]:
        """验证一个epoch - 修复版本"""
        self.student_model.eval()

        total_loss = 0.0
        all_predictions = []
        all_targets = []
        num_batches = 0

        with torch.no_grad():
            for batch_idx, batch in enumerate(val_loader):
                try:
                    # 验证数据格式并提取
                    if isinstance(batch, (list, tuple)) and len(batch) >= 2:
                        images = batch[0].to(self.device)
                        masks = batch[1].to(self.device)
                    else:
                        logging.warning(f"批次 {batch_idx}: 意外的数据格式 {type(batch)}")
                        continue

                    # 验证数据形状
                    if images.dim() != 4 or masks.dim() != 3:
                        logging.warning(f"批次 {batch_idx}: 数据形状异常 - images: {images.shape}, masks: {masks.shape}")
                        continue

                    # 验证标签值范围
                    max_label = masks.max().item()
                    min_label = masks.min().item()
                    if max_label >= self.config['data']['num_classes'] or min_label < 0:
                        logging.warning(f"批次 {batch_idx}: 标签值超出范围 [{min_label}, {max_label}]")
                        # 裁剪标签值到有效范围
                        masks = torch.clamp(masks, 0, self.config['data']['num_classes'] - 1)

                    # 前向传播
                    outputs = self.student_model(images)
                    
                    # 验证输出格式
                    if isinstance(outputs, dict) and 'main' in outputs:
                        predictions = outputs['main']
                    else:
                        logging.warning(f"批次 {batch_idx}: 意外的模型输出格式")
                        continue

                    # 验证输出形状
                    expected_shape = (images.shape[0], self.config['data']['num_classes'], images.shape[2], images.shape[3])
                    if predictions.shape != expected_shape:
                        logging.warning(f"批次 {batch_idx}: 预测形状异常 - 期望: {expected_shape}, 实际: {predictions.shape}")
                        continue

                    # 计算监督损失
                    try:
                        supervised_loss = self.supervised_criterion(predictions, masks)
                        total_loss += supervised_loss.item()
                    except Exception as e:
                        logging.warning(f"批次 {batch_idx}: 损失计算失败 - {e}")
                        continue

                    # 收集预测和真实标签
                    pred_classes = torch.argmax(predictions, dim=1)
                    all_predictions.append(pred_classes.cpu())
                    all_targets.append(masks.cpu())
                    num_batches += 1

                except Exception as e:
                    logging.error(f"批次 {batch_idx}: 处理失败 - {e}")
                    continue

        # 检查是否有有效数据
        if num_batches == 0:
            logging.error("验证失败: 没有有效的验证批次")
            return {'loss': float('inf'), 'miou': 0.0, 'pixel_accuracy': 0.0}

        # 计算指标
        try:
            all_predictions = torch.cat(all_predictions, dim=0)
            all_targets = torch.cat(all_targets, dim=0)

            # 再次验证合并后的数据
            logging.info(f"验证数据统计: 预测形状={all_predictions.shape}, 标签形状={all_targets.shape}")
            logging.info(f"标签值范围: [{all_targets.min()}, {all_targets.max()}]")

            metrics = self.metrics.compute_metrics(all_predictions, all_targets)
            metrics['loss'] = total_loss / num_batches

            # 生成并打印详细的类别指标报告
            try:
                detailed_report = self.metrics.get_detailed_report(all_predictions, all_targets)
                print(detailed_report)
                logging.info(detailed_report)
            except Exception as e:
                logging.warning(f"详细报告生成失败: {e}")

            return metrics

        except Exception as e:
            logging.error(f"指标计算失败: {e}")
            return {'loss': total_loss / max(num_batches, 1), 'miou': 0.0, 'pixel_accuracy': 0.0}'''
    
    # 替换验证方法
    if old_validation_method in content:
        content = content.replace(old_validation_method, new_validation_method)
        
        # 写回文件
        with open(trainer_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ SSL训练器验证方法已修复")
        return True
    else:
        print("⚠️  未找到需要修复的验证方法")
        return False

def create_validation_test_script():
    """创建验证测试脚本"""
    
    test_script = Path(__file__).parent / "test_validation.py"
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证功能测试脚本
"""

import sys
import torch
import yaml
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from training.ssl_trainer import SSLTrainer

def test_validation():
    """测试验证功能"""
    print("开始验证功能测试...")
    
    # 加载配置
    config_path = Path(__file__).parent / "configs" / "ssl_config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 设置调试模式
    config['training']['epochs'] = 1
    config['training']['val_interval'] = 1
    config['data']['labeled_batch_size'] = 1
    config['data']['unlabeled_batch_size'] = 1
    
    try:
        # 创建训练器
        trainer = SSLTrainer(config, fold=0)
        
        # 获取验证数据加载器
        val_loader = trainer.data_loader.get_val_loader()
        
        print(f"验证数据集大小: {len(val_loader)} 批次")
        
        # 测试验证方法
        val_stats = trainer._validate_epoch(val_loader)
        
        print("验证结果:")
        for key, value in val_stats.items():
            print(f"  {key}: {value}")
        
        print("✅ 验证功能测试成功")
        
    except Exception as e:
        print(f"❌ 验证功能测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_validation()
'''
    
    with open(test_script, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✅ 验证测试脚本已创建: {test_script}")

def main():
    """主函数"""
    print("开始修复SSL验证问题...")
    
    # 修复训练器
    if fix_ssl_trainer_validation():
        print("SSL训练器验证方法修复完成")
    
    # 创建测试脚本
    create_validation_test_script()
    
    print("\n修复完成！建议执行以下步骤:")
    print("1. 运行诊断脚本: python debug_validation.py")
    print("2. 运行验证测试: python test_validation.py")
    print("3. 如果测试通过，重新开始SSL训练")

if __name__ == "__main__":
    main()
