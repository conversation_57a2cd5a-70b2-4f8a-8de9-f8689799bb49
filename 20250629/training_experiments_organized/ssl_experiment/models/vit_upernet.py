#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ViT + UperNet模型架构
使用MAE预训练的ViT作为backbone，UperNet作为分割头
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple
import math
import logging
from pathlib import Path

class PatchEmbed(nn.Module):
    """图像到patch嵌入"""
    
    def __init__(self, img_size=224, patch_size=16, in_chans=3, embed_dim=768):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2
        
        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
    
    def forward(self, x):
        B, C, H, W = x.shape
        x = self.proj(x).flatten(2).transpose(1, 2)  # (B, num_patches, embed_dim)
        return x

class Attention(nn.Module):
    """多头自注意力机制"""
    
    def __init__(self, dim, num_heads=8, qkv_bias=False, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5
        
        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)
    
    def forward(self, x):
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x

class MLP(nn.Module):
    """MLP模块"""
    
    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)
    
    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x

class Block(nn.Module):
    """Transformer块"""
    
    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, drop=0., attn_drop=0.):
        super().__init__()
        self.norm1 = nn.LayerNorm(dim)
        self.attn = Attention(dim, num_heads=num_heads, qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)
        self.norm2 = nn.LayerNorm(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = MLP(in_features=dim, hidden_features=mlp_hidden_dim, act_layer=nn.GELU, drop=drop)
    
    def forward(self, x):
        x = x + self.attn(self.norm1(x))
        x = x + self.mlp(self.norm2(x))
        return x

class VisionTransformer(nn.Module):
    """Vision Transformer backbone"""
    
    def __init__(
        self,
        img_size=224,
        patch_size=16,
        in_chans=3,
        embed_dim=768,
        depth=12,
        num_heads=12,
        mlp_ratio=4.,
        qkv_bias=True,
        drop_rate=0.,
        attn_drop_rate=0.
    ):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        self.num_patches = (img_size // patch_size) ** 2
        
        # Patch embedding
        self.patch_embed = PatchEmbed(img_size, patch_size, in_chans, embed_dim)
        
        # Position embedding
        self.pos_embed = nn.Parameter(torch.zeros(1, self.num_patches + 1, embed_dim))
        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        self.pos_drop = nn.Dropout(p=drop_rate)
        
        # Transformer blocks
        self.blocks = nn.ModuleList([
            Block(
                dim=embed_dim,
                num_heads=num_heads,
                mlp_ratio=mlp_ratio,
                qkv_bias=qkv_bias,
                drop=drop_rate,
                attn_drop=attn_drop_rate
            )
            for _ in range(depth)
        ])
        
        self.norm = nn.LayerNorm(embed_dim)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        torch.nn.init.trunc_normal_(self.pos_embed, std=0.02)
        torch.nn.init.trunc_normal_(self.cls_token, std=0.02)
        
        for m in self.modules():
            if isinstance(m, nn.Linear):
                torch.nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def forward_features(self, x):
        """提取特征"""
        B = x.shape[0]
        
        # Patch embedding
        x = self.patch_embed(x)  # (B, num_patches, embed_dim)
        
        # Add cls token
        cls_tokens = self.cls_token.expand(B, -1, -1)
        x = torch.cat((cls_tokens, x), dim=1)
        
        # Add position embedding
        x = x + self.pos_embed
        x = self.pos_drop(x)
        
        # 收集多层特征用于分割
        features = []
        for i, blk in enumerate(self.blocks):
            x = blk(x)
            # 收集特定层的特征
            if i in [2, 5, 8, 11]:  # 收集第3、6、9、12层特征
                features.append(x)
        
        x = self.norm(x)
        return x, features
    
    def forward(self, x):
        """前向传播"""
        x, features = self.forward_features(x)
        return x, features

class ConvModule(nn.Module):
    """卷积模块"""
    
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1, norm_layer=nn.BatchNorm2d):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding, bias=False)
        self.norm = norm_layer(out_channels)
        self.act = nn.ReLU(inplace=True)
    
    def forward(self, x):
        x = self.conv(x)
        x = self.norm(x)
        x = self.act(x)
        return x

class PSPModule(nn.Module):
    """金字塔池化模块"""
    
    def __init__(self, in_channels, out_channels, pool_scales=(1, 2, 3, 6)):
        super().__init__()
        self.pool_scales = pool_scales
        self.in_channels = in_channels
        self.out_channels = out_channels
        
        self.psp_modules = nn.ModuleList()
        for scale in pool_scales:
            self.psp_modules.append(
                nn.Sequential(
                    nn.AdaptiveAvgPool2d(scale),
                    ConvModule(in_channels, out_channels, 1, padding=0)
                )
            )
        
        self.bottleneck = ConvModule(
            in_channels + len(pool_scales) * out_channels,
            out_channels,
            3
        )
    
    def forward(self, x):
        """前向传播"""
        psp_outs = [x]
        for psp_module in self.psp_modules:
            psp_out = psp_module(x)
            psp_out = F.interpolate(psp_out, size=x.shape[2:], mode='bilinear', align_corners=False)
            psp_outs.append(psp_out)
        
        psp_outs = torch.cat(psp_outs, dim=1)
        output = self.bottleneck(psp_outs)
        return output

class UperNetHead(nn.Module):
    """UperNet分割头"""
    
    def __init__(
        self,
        in_channels_list: List[int],
        channels: int = 512,
        num_classes: int = 3,
        pool_scales: Tuple[int, ...] = (1, 2, 3, 6)
    ):
        super().__init__()
        self.in_channels_list = in_channels_list
        self.channels = channels
        self.num_classes = num_classes
        
        # PSP模块
        self.psp_modules = PSPModule(
            in_channels_list[-1],
            channels // len(pool_scales),
            pool_scales
        )
        
        # 侧边连接
        self.lateral_convs = nn.ModuleList()
        for in_channels in in_channels_list[:-1]:
            self.lateral_convs.append(
                ConvModule(in_channels, channels, 1, padding=0)
            )
        
        # FPN卷积
        self.fpn_convs = nn.ModuleList()
        for _ in range(len(in_channels_list) - 1):
            self.fpn_convs.append(
                ConvModule(channels, channels, 3)
            )
        
        # 最终分类器
        self.classifier = nn.Conv2d(channels, num_classes, 1)
        
        # Dropout
        self.dropout = nn.Dropout2d(0.1)
    
    def forward(self, features: List[torch.Tensor]) -> torch.Tensor:
        """前向传播"""
        # 获取最高层特征并应用PSP
        psp_out = self.psp_modules(features[-1])
        
        # FPN自顶向下路径
        fpn_outs = [psp_out]
        for i in range(len(features) - 2, -1, -1):
            lateral_out = self.lateral_convs[i](features[i])
            
            # 上采样并融合
            prev_out = F.interpolate(
                fpn_outs[-1],
                size=lateral_out.shape[2:],
                mode='bilinear',
                align_corners=False
            )
            
            fpn_out = lateral_out + prev_out
            fpn_out = self.fpn_convs[i](fpn_out)
            fpn_outs.append(fpn_out)
        
        # 融合所有FPN输出
        fpn_outs = fpn_outs[::-1]  # 反转顺序
        output_size = fpn_outs[0].shape[2:]
        
        fused_out = fpn_outs[0]
        for fpn_out in fpn_outs[1:]:
            fused_out = fused_out + F.interpolate(
                fpn_out,
                size=output_size,
                mode='bilinear',
                align_corners=False
            )
        
        # 应用dropout和分类器
        fused_out = self.dropout(fused_out)
        output = self.classifier(fused_out)

        return output

class ViTUperNet(nn.Module):
    """ViT + UperNet分割模型"""

    def __init__(self, config: Dict):
        super().__init__()
        self.config = config

        # ViT backbone
        self.backbone = VisionTransformer(
            img_size=config['model']['img_size'],
            patch_size=config['model']['patch_size'],
            embed_dim=config['model']['embed_dim'],
            depth=config['model']['depth'],
            num_heads=config['model']['num_heads']
        )

        # 特征维度转换
        self.feature_dims = [config['model']['embed_dim']] * 4  # 4个特征层

        # 特征重塑模块
        self.feature_reshape = nn.ModuleList()
        for dim in self.feature_dims:
            self.feature_reshape.append(
                nn.Sequential(
                    nn.Linear(dim, dim),
                    nn.LayerNorm(dim)
                )
            )

        # UperNet分割头
        self.decode_head = UperNetHead(
            in_channels_list=self.feature_dims,
            channels=config['model']['decoder_channels'][0],
            num_classes=config['data']['num_classes']
        )

        # 辅助分割头（可选）
        self.aux_head = nn.Sequential(
            ConvModule(self.feature_dims[-2], 256, 3),
            nn.Dropout2d(0.1),
            nn.Conv2d(256, config['data']['num_classes'], 1)
        )

    def _reshape_features(self, features: List[torch.Tensor]) -> List[torch.Tensor]:
        """将ViT特征重塑为2D特征图"""
        reshaped_features = []

        for i, feat in enumerate(features):
            B, N, C = feat.shape
            # 移除cls token
            feat = feat[:, 1:, :]  # (B, num_patches, C)

            # 重塑为2D特征图
            num_patches = N - 1  # 减去cls token
            H = W = int(math.sqrt(num_patches))
            feat = feat.transpose(1, 2).reshape(B, C, H, W)

            # 应用特征变换
            feat_flat = feat.flatten(2).transpose(1, 2)  # (B, H*W, C)
            feat_flat = self.feature_reshape[i](feat_flat)
            feat = feat_flat.transpose(1, 2).reshape(B, C, H, W)

            reshaped_features.append(feat)

        return reshaped_features

    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """前向传播"""
        input_size = x.shape[2:]

        # ViT特征提取
        cls_token, features = self.backbone(x)

        # 重塑特征
        features = self._reshape_features(features)

        # 上采样特征到统一尺寸（使用最大的特征图尺寸）
        max_size = max([feat.shape[2] for feat in features])
        target_size = (max_size, max_size)

        upsampled_features = []
        for feat in features:
            if feat.shape[2:] != target_size:
                feat = F.interpolate(
                    feat,
                    size=target_size,
                    mode='bilinear',
                    align_corners=False
                )
            upsampled_features.append(feat)

        # UperNet解码
        main_out = self.decode_head(upsampled_features)

        # 上采样到输入尺寸
        main_out = F.interpolate(
            main_out,
            size=input_size,
            mode='bilinear',
            align_corners=False
        )

        # 辅助输出
        aux_out = self.aux_head(upsampled_features[-2])
        aux_out = F.interpolate(
            aux_out,
            size=input_size,
            mode='bilinear',
            align_corners=False
        )

        return {
            'main': main_out,
            'aux': aux_out,
            'features': features
        }

    def load_mae_weights(self, mae_checkpoint_path: str):
        """加载MAE预训练权重"""
        try:
            checkpoint = torch.load(mae_checkpoint_path, map_location='cpu')

            # 提取MAE模型权重
            if 'model' in checkpoint:
                mae_state_dict = checkpoint['model']
            else:
                mae_state_dict = checkpoint

            # 过滤并匹配权重
            vit_state_dict = {}
            for key, value in mae_state_dict.items():
                if key.startswith('patch_embed') or key.startswith('pos_embed') or key.startswith('cls_token'):
                    vit_state_dict[key] = value
                elif key.startswith('blocks'):
                    vit_state_dict[key] = value
                elif key.startswith('norm'):
                    vit_state_dict[key] = value

            # 加载权重
            missing_keys, unexpected_keys = self.backbone.load_state_dict(vit_state_dict, strict=False)

            logging.info(f"成功加载MAE预训练权重: {mae_checkpoint_path}")
            logging.info(f"缺失的键: {len(missing_keys)}")
            logging.info(f"意外的键: {len(unexpected_keys)}")

            return True

        except Exception as e:
            logging.error(f"加载MAE权重失败: {e}")
            return False

def create_vit_upernet_model(config: Dict) -> ViTUperNet:
    """创建ViT-UperNet模型"""
    model = ViTUperNet(config)

    # 加载MAE预训练权重
    if config['model']['mae_checkpoint']:
        model.load_mae_weights(config['model']['mae_checkpoint'])

    return model
