#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL模型 - 直接使用主实验的ViT backbone和UperNet头
"""

import sys
import torch
import torch.nn as nn
from pathlib import Path
from typing import Dict, Any
import logging

# 添加主实验路径
main_experiment_path = Path(__file__).parent.parent.parent
sys.path.append(str(main_experiment_path))

# 导入主实验的模型组件
from core_main.models.vit_backbone import ViTBackbone
from core_main.models.upernet_head import ViTUPerNetHead

class SSLSegmentationModel(nn.Module):
    """SSL分割模型 - 使用主实验的架构"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # 使用主实验的ViT backbone
        self.backbone = ViTBackbone(
            model_name=config['model']['vit_model'],
            img_size=config['model']['img_size'],
            patch_size=config['model']['patch_size'],
            embed_dim=config['model']['embed_dim'],
            depth=config['model']['depth'],
            num_heads=config['model']['num_heads'],
            mlp_ratio=config['model']['mlp_ratio'],
            pretrained=False,  # 我们会手动加载MAE权重
            pretrained_path=config['model']['mae_checkpoint'],
            drop_rate=config['model']['drop_rate']
        )
        
        # 使用主实验的ViT UperNet头
        self.segmentation_head = ViTUPerNetHead(
            feature_channels=config['model']['embed_dim'],
            fpn_dim=config['model']['decoder_channels'][0],
            num_classes=config['classes']['num_classes'],
            pool_scales=[1, 2, 3, 6]
        )
        
        # 特征提取层索引
        self.feature_layers = config['model']['feature_layers']
        
        logging.info("SSL分割模型初始化完成，使用主实验架构")
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """前向传播"""
        # ViT特征提取 - 获取多层特征
        features = self.backbone.get_intermediate_features(x, self.feature_layers)

        # 将所有特征重塑为空间特征图
        spatial_features = {}
        for layer_name, feat in features.items():
            if layer_name != 'final':  # 跳过final，因为它和layer_12相同
                spatial_features[layer_name] = self.backbone.reshape_to_spatial(feat)

        # 添加最终特征
        spatial_features['layer_12'] = self.backbone.reshape_to_spatial(features['final'])

        # 目标输出尺寸
        target_size = (x.shape[2], x.shape[3])  # (H, W)

        # UperNet分割
        outputs = self.segmentation_head(spatial_features, target_size)

        return {
            'main': outputs['main'],
            'aux': outputs['aux'],
            'features': features
        }
    
    def load_mae_weights(self, mae_checkpoint_path: str):
        """加载MAE预训练权重"""
        try:
            checkpoint = torch.load(mae_checkpoint_path, map_location='cpu')

            # 提取MAE模型权重
            if 'model' in checkpoint:
                mae_state_dict = checkpoint['model']
            else:
                mae_state_dict = checkpoint

            logging.info(f"MAE检查点包含 {len(mae_state_dict)} 个参数")

            # 获取backbone的参数名称
            backbone_params = set(self.backbone.state_dict().keys())
            logging.info(f"Backbone模型包含 {len(backbone_params)} 个参数")

            # 更精确的权重匹配
            vit_state_dict = {}
            matched_keys = []
            unmatched_mae_keys = []

            for key, value in mae_state_dict.items():
                # 跳过MAE解码器相关的权重
                if any(skip in key for skip in ['decoder', 'mask_token', 'norm_pix']):
                    unmatched_mae_keys.append(key)
                    continue

                # 处理参数名称映射：encoder.xxx -> vit.xxx
                mapped_key = key
                if key.startswith('encoder.'):
                    mapped_key = key.replace('encoder.', 'vit.')

                # 匹配ViT编码器权重
                if mapped_key in backbone_params:
                    vit_state_dict[mapped_key] = value
                    matched_keys.append(f"{key} -> {mapped_key}")
                else:
                    unmatched_mae_keys.append(key)

            logging.info(f"成功匹配 {len(matched_keys)} 个MAE权重")
            logging.info(f"未匹配的MAE权重: {len(unmatched_mae_keys)}")

            # 加载权重到backbone
            missing_keys, unexpected_keys = self.backbone.load_state_dict(vit_state_dict, strict=False)

            # 详细报告
            logging.info(f"成功加载MAE预训练权重: {mae_checkpoint_path}")
            logging.info(f"缺失的键: {len(missing_keys)} (这些是新增的分割头参数，正常)")
            logging.info(f"意外的键: {len(unexpected_keys)} (这些是MAE解码器参数，已跳过)")

            if len(missing_keys) > 0:
                logging.info(f"缺失键示例: {missing_keys[:5]}")
            if len(unexpected_keys) > 0:
                logging.info(f"意外键示例: {unexpected_keys[:5]}")

            return True

        except Exception as e:
            logging.error(f"加载MAE权重失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def create_ssl_model(config: Dict[str, Any]) -> SSLSegmentationModel:
    """创建SSL模型"""
    model = SSLSegmentationModel(config)
    
    # 加载MAE预训练权重
    if config['model']['mae_checkpoint']:
        model.load_mae_weights(config['model']['mae_checkpoint'])
    
    return model
