#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分割评估指标
包含mIoU、<PERSON><PERSON>、像素准确率等指标
"""

import torch
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
from sklearn.metrics import confusion_matrix
import logging

class SegmentationMetrics:
    """分割评估指标计算器"""
    
    def __init__(self, num_classes: int, ignore_index: int = -1):
        """
        初始化评估指标
        
        Args:
            num_classes: 类别数量
            ignore_index: 忽略的类别索引
        """
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        # 8类分割的类别名称 - 与主实验一致
        self.class_names = [
            'Background',     # ID=0
            'Capped_Brood',   # ID=1
            'Eggs',           # ID=2
            'Honey',          # ID=3
            'Honeycomb',      # ID=4
            'Larvae',         # ID=5
            'Nectar',         # ID=6
            'Pollen'          # ID=7
        ]
    
    def compute_metrics(
        self,
        predictions: torch.Tensor,
        targets: torch.Tensor
    ) -> Dict[str, float]:
        """
        计算所有评估指标
        
        Args:
            predictions: 预测结果 (N, H, W) 或 (N, C, H, W)
            targets: 真实标签 (N, H, W)
            
        Returns:
            包含各种指标的字典
        """
        # 确保预测是类别索引
        if predictions.dim() == 4:  # (N, C, H, W)
            predictions = torch.argmax(predictions, dim=1)
        
        # 转换为numpy数组
        pred_np = predictions.cpu().numpy().flatten()
        target_np = targets.cpu().numpy().flatten()
        
        # 过滤忽略的像素
        if self.ignore_index >= 0:
            valid_mask = target_np != self.ignore_index
            pred_np = pred_np[valid_mask]
            target_np = target_np[valid_mask]
        
        # 计算各种指标
        metrics = {}
        
        # 1. 像素准确率
        metrics['pixel_accuracy'] = self._compute_pixel_accuracy(pred_np, target_np)
        
        # 2. 平均像素准确率
        metrics['mean_pixel_accuracy'] = self._compute_mean_pixel_accuracy(pred_np, target_np)
        
        # 3. IoU和mIoU
        iou_per_class = self._compute_iou_per_class(pred_np, target_np)
        metrics['miou'] = np.nanmean(iou_per_class)
        
        for i, iou in enumerate(iou_per_class):
            if i < len(self.class_names):
                metrics[f'iou_{self.class_names[i].lower()}'] = iou
            else:
                metrics[f'iou_class_{i}'] = iou
        
        # 4. Dice系数
        dice_per_class = self._compute_dice_per_class(pred_np, target_np)
        metrics['mean_dice'] = np.nanmean(dice_per_class)
        
        for i, dice in enumerate(dice_per_class):
            if i < len(self.class_names):
                metrics[f'dice_{self.class_names[i].lower()}'] = dice
            else:
                metrics[f'dice_class_{i}'] = dice
        
        # 5. F1分数
        f1_per_class = self._compute_f1_per_class(pred_np, target_np)
        metrics['mean_f1'] = np.nanmean(f1_per_class)
        
        # 6. 频率加权IoU
        metrics['fwiou'] = self._compute_frequency_weighted_iou(pred_np, target_np)
        
        return metrics
    
    def _compute_pixel_accuracy(self, pred: np.ndarray, target: np.ndarray) -> float:
        """计算像素准确率"""
        correct = (pred == target).sum()
        total = len(target)
        return float(correct) / total if total > 0 else 0.0
    
    def _compute_mean_pixel_accuracy(self, pred: np.ndarray, target: np.ndarray) -> float:
        """计算平均像素准确率"""
        accuracies = []
        
        for class_id in range(self.num_classes):
            class_mask = (target == class_id)
            if class_mask.sum() > 0:
                class_pred = pred[class_mask]
                class_target = target[class_mask]
                accuracy = (class_pred == class_target).sum() / len(class_target)
                accuracies.append(accuracy)
        
        return np.mean(accuracies) if accuracies else 0.0
    
    def _compute_iou_per_class(self, pred: np.ndarray, target: np.ndarray) -> np.ndarray:
        """计算每个类别的IoU"""
        ious = []
        
        for class_id in range(self.num_classes):
            pred_mask = (pred == class_id)
            target_mask = (target == class_id)
            
            intersection = (pred_mask & target_mask).sum()
            union = (pred_mask | target_mask).sum()
            
            if union > 0:
                iou = intersection / union
            else:
                iou = np.nan  # 如果该类别不存在，设为NaN
            
            ious.append(iou)
        
        return np.array(ious)
    
    def _compute_dice_per_class(self, pred: np.ndarray, target: np.ndarray) -> np.ndarray:
        """计算每个类别的Dice系数"""
        dice_scores = []
        
        for class_id in range(self.num_classes):
            pred_mask = (pred == class_id)
            target_mask = (target == class_id)
            
            intersection = (pred_mask & target_mask).sum()
            total = pred_mask.sum() + target_mask.sum()
            
            if total > 0:
                dice = (2.0 * intersection) / total
            else:
                dice = np.nan
            
            dice_scores.append(dice)
        
        return np.array(dice_scores)
    
    def _compute_f1_per_class(self, pred: np.ndarray, target: np.ndarray) -> np.ndarray:
        """计算每个类别的F1分数"""
        f1_scores = []
        
        for class_id in range(self.num_classes):
            pred_mask = (pred == class_id)
            target_mask = (target == class_id)
            
            tp = (pred_mask & target_mask).sum()
            fp = (pred_mask & ~target_mask).sum()
            fn = (~pred_mask & target_mask).sum()
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            
            if precision + recall > 0:
                f1 = 2 * (precision * recall) / (precision + recall)
            else:
                f1 = np.nan
            
            f1_scores.append(f1)
        
        return np.array(f1_scores)
    
    def _compute_frequency_weighted_iou(self, pred: np.ndarray, target: np.ndarray) -> float:
        """计算频率加权IoU"""
        ious = self._compute_iou_per_class(pred, target)
        
        # 计算每个类别的频率
        frequencies = []
        total_pixels = len(target)
        
        for class_id in range(self.num_classes):
            class_pixels = (target == class_id).sum()
            frequency = class_pixels / total_pixels
            frequencies.append(frequency)
        
        frequencies = np.array(frequencies)
        
        # 计算加权IoU（忽略NaN值）
        valid_mask = ~np.isnan(ious)
        if valid_mask.sum() > 0:
            weighted_iou = np.sum(frequencies[valid_mask] * ious[valid_mask])
        else:
            weighted_iou = 0.0
        
        return weighted_iou
    
    def compute_confusion_matrix(
        self,
        predictions: torch.Tensor,
        targets: torch.Tensor
    ) -> np.ndarray:
        """计算混淆矩阵"""
        # 确保预测是类别索引
        if predictions.dim() == 4:  # (N, C, H, W)
            predictions = torch.argmax(predictions, dim=1)
        
        # 转换为numpy数组
        pred_np = predictions.cpu().numpy().flatten()
        target_np = targets.cpu().numpy().flatten()
        
        # 过滤忽略的像素
        if self.ignore_index >= 0:
            valid_mask = target_np != self.ignore_index
            pred_np = pred_np[valid_mask]
            target_np = target_np[valid_mask]
        
        # 计算混淆矩阵
        cm = confusion_matrix(
            target_np,
            pred_np,
            labels=list(range(self.num_classes))
        )
        
        return cm
    
    def print_metrics(self, metrics: Dict[str, float], title: str = "Evaluation Metrics"):
        """打印评估指标"""
        logging.info(f"\n{title}")
        logging.info("=" * 50)
        
        # 主要指标
        main_metrics = ['pixel_accuracy', 'miou', 'mean_dice', 'mean_f1']
        for metric in main_metrics:
            if metric in metrics:
                logging.info(f"{metric.replace('_', ' ').title()}: {metrics[metric]:.4f}")
        
        # 每个类别的IoU
        logging.info("\nPer-class IoU:")
        for i, class_name in enumerate(self.class_names):
            iou_key = f'iou_{class_name.lower()}'
            if iou_key in metrics:
                logging.info(f"  {class_name}: {metrics[iou_key]:.4f}")
        
        # 每个类别的Dice
        logging.info("\nPer-class Dice:")
        for i, class_name in enumerate(self.class_names):
            dice_key = f'dice_{class_name.lower()}'
            if dice_key in metrics:
                logging.info(f"  {class_name}: {metrics[dice_key]:.4f}")
        
        logging.info("=" * 50)

    def get_detailed_report(
        self,
        predictions: torch.Tensor,
        targets: torch.Tensor
    ) -> str:
        """
        生成详细的类别指标报告

        Args:
            predictions: 预测结果
            targets: 真实标签

        Returns:
            格式化的指标报告字符串
        """
        # 确保预测是类别索引
        if predictions.dim() == 4:  # (N, C, H, W)
            predictions = torch.argmax(predictions, dim=1)

        # 转换为numpy数组
        pred_np = predictions.cpu().numpy().flatten()
        target_np = targets.cpu().numpy().flatten()

        # 计算每个类别的指标
        iou_per_class = self._compute_iou_per_class(pred_np, target_np)
        dice_per_class = self._compute_dice_per_class(pred_np, target_np)
        f1_per_class = self._compute_f1_per_class(pred_np, target_np)

        # 计算每个类别的像素数量和比例
        class_counts = []
        class_ratios = []
        total_pixels = len(target_np)

        for class_id in range(self.num_classes):
            count = (target_np == class_id).sum()
            ratio = count / total_pixels * 100
            class_counts.append(count)
            class_ratios.append(ratio)

        # 生成报告
        report = "\n" + "="*80 + "\n"
        report += "                    DETAILED CLASS-WISE METRICS REPORT\n"
        report += "="*80 + "\n"

        # 表头
        report += f"{'Class':<15} {'Count':<10} {'Ratio%':<8} {'IoU':<8} {'Dice':<8} {'F1':<8}\n"
        report += "-"*80 + "\n"

        # 每个类别的指标
        for i in range(self.num_classes):
            class_name = self.class_names[i]
            count = class_counts[i]
            ratio = class_ratios[i]
            iou = iou_per_class[i] if not np.isnan(iou_per_class[i]) else 0.0
            dice = dice_per_class[i] if not np.isnan(dice_per_class[i]) else 0.0
            f1 = f1_per_class[i] if not np.isnan(f1_per_class[i]) else 0.0

            report += f"{class_name:<15} {count:<10} {ratio:<8.2f} {iou:<8.4f} {dice:<8.4f} {f1:<8.4f}\n"

        report += "-"*80 + "\n"

        # 平均指标
        mean_iou = np.nanmean(iou_per_class)
        mean_dice = np.nanmean(dice_per_class)
        mean_f1 = np.nanmean(f1_per_class)

        report += f"{'MEAN':<15} {'':<10} {'':<8} {mean_iou:<8.4f} {mean_dice:<8.4f} {mean_f1:<8.4f}\n"
        report += "="*80 + "\n"

        # 重要类别的特别关注
        important_classes = [1, 2, 4, 5, 6, 7]  # 除了背景和蜂蜜的其他类别
        report += "\nIMPORTANT CLASSES (excluding Background & Honey):\n"
        report += "-"*50 + "\n"

        for class_id in important_classes:
            class_name = self.class_names[class_id]
            iou = iou_per_class[class_id] if not np.isnan(iou_per_class[class_id]) else 0.0
            dice = dice_per_class[class_id] if not np.isnan(dice_per_class[class_id]) else 0.0
            ratio = class_ratios[class_id]

            report += f"{class_name:<15}: IoU={iou:.4f}, Dice={dice:.4f}, Ratio={ratio:.2f}%\n"

        # 稀有类别特别关注
        rare_classes = [2, 5, 6, 7]  # Eggs, Larvae, Nectar, Pollen
        rare_ious = [iou_per_class[i] for i in rare_classes if not np.isnan(iou_per_class[i])]
        if rare_ious:
            rare_mean_iou = np.mean(rare_ious)
            report += f"\nRare Classes Mean IoU: {rare_mean_iou:.4f}\n"

        return report

class MetricsTracker:
    """指标跟踪器"""
    
    def __init__(self, num_classes: int):
        """初始化指标跟踪器"""
        self.metrics_calculator = SegmentationMetrics(num_classes)
        self.history = []
    
    def update(self, predictions: torch.Tensor, targets: torch.Tensor) -> Dict[str, float]:
        """更新指标"""
        metrics = self.metrics_calculator.compute_metrics(predictions, targets)
        self.history.append(metrics)
        return metrics
    
    def get_best_metrics(self, metric_name: str = 'miou') -> Dict[str, float]:
        """获取最佳指标"""
        if not self.history:
            return {}
        
        best_idx = max(range(len(self.history)), key=lambda i: self.history[i].get(metric_name, 0))
        return self.history[best_idx]
    
    def get_average_metrics(self, last_n: int = None) -> Dict[str, float]:
        """获取平均指标"""
        if not self.history:
            return {}
        
        history_subset = self.history[-last_n:] if last_n else self.history
        
        # 计算平均值
        avg_metrics = {}
        for key in history_subset[0].keys():
            values = [metrics[key] for metrics in history_subset if not np.isnan(metrics[key])]
            if values:
                avg_metrics[key] = np.mean(values)
            else:
                avg_metrics[key] = 0.0
        
        return avg_metrics

    def get_detailed_report(
        self,
        predictions: torch.Tensor,
        targets: torch.Tensor
    ) -> str:
        """
        生成详细的类别指标报告

        Args:
            predictions: 预测结果
            targets: 真实标签

        Returns:
            格式化的指标报告字符串
        """
        # 使用指标计算器生成详细报告
        return self.metrics_calculator.get_detailed_report(predictions, targets)
