# 学生-教师半监督学习配置文件
# 使用MAE预训练的ViT + UperNet进行蜂巢分割

# 基础配置
experiment_name: "ssl_student_teacher_mae_vit"
device: "cuda:1"  # 使用RTX 2080
seed: 42
num_workers: 8

# 数据配置 - 直接使用主实验的预处理数据和配置
data:
  # 有标注数据（使用主实验的预处理数据）
  labeled_train_images: "/home/<USER>/PycharmProjects/20250629/training_data/honeycomb_8class_patches/train/images"
  labeled_train_masks: "/home/<USER>/PycharmProjects/20250629/training_data/honeycomb_8class_patches/train/masks"
  labeled_val_images: "/home/<USER>/PycharmProjects/20250629/training_data/honeycomb_8class_patches/val/images"
  labeled_val_masks: "/home/<USER>/PycharmProjects/20250629/training_data/honeycomb_8class_patches/val/masks"

  # 无标注数据（MAE训练的图块）
  unlabeled_images: "/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/prediction_patches"

  # SSL特定配置
  labeled_batch_size: 4      # 有标注数据批次大小（适应RTX 2080）
  unlabeled_batch_size: 16   # 无标注数据批次大小 (4倍)
  unlabeled_ratio: 4         # 无标注:有标注 = 4:1

  # 与主实验完全一致的配置
  image_size: [512, 512]
  num_classes: 8
  batch_size: 16             # 主实验的批次大小
  num_workers: 10            # 主实验的工作线程数
  pin_memory: true

  # 数据增强配置 - 与主实验一致
  augmentation:
    enable: true
    random_rotate90: 0.5
    horizontal_flip: 0.5
    vertical_flip: 0.5
    color_jitter:
      brightness: 0.2
      contrast: 0.2
      saturation: 0.2
      hue: 0.1
    elastic_transform: 0.3
    gaussian_noise: 0.2

  # 数据标准化配置 - 与主实验一致
  normalize:
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]

# 类别配置 - 与主实验完全一致
classes:
  num_classes: 8
  # 按真实数据中的ID顺序排列
  class_names:
    - "background"    # ID=0, 39.39%像素
    - "capped_brood"  # ID=1, 10.81%像素
    - "eggs"          # ID=2, 0.34%像素
    - "honey"         # ID=3, 2.86%像素
    - "honeycomb"     # ID=4, 42.37%像素 - 最重要！
    - "larvae"        # ID=5, 0.65%像素
    - "nectar"        # ID=6, 2.44%像素
    - "pollen"        # ID=7, 1.14%像素

  # 类别权重 - 与主实验一致
  class_weights: [2.3, 4.4, 50.0, 8.6, 2.2, 36.2, 9.3, 13.7]
  
  # 数据增强
  weak_augmentation:
    horizontal_flip: 0.5
    vertical_flip: 0.5
    rotation: 15
    brightness: 0.1
    contrast: 0.1
    
  strong_augmentation:
    horizontal_flip: 0.5
    vertical_flip: 0.5
    rotation: 30
    brightness: 0.2
    contrast: 0.2
    gaussian_blur: 0.3
    color_jitter: 0.4
    cutout: 0.2

# 模型配置 - 使用主实验的架构
model:
  # MAE预训练权重
  mae_checkpoint: "/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base_backup_20250726_231529/best_checkpoint_epoch_1556.pth"

  # ViT backbone配置 - 与主实验一致
  vit_model: "vit_base_patch16_224"
  img_size: 512
  patch_size: 16
  embed_dim: 768
  depth: 12
  num_heads: 12
  mlp_ratio: 4
  qkv_bias: true
  drop_rate: 0.0
  attn_drop_rate: 0.0

  # UperNet decoder配置 - 与主实验一致
  decoder_channels: [512, 256, 128, 64]
  decoder_use_batchnorm: true
  decoder_attention_type: "scse"

  # 特征提取层配置 - 与主实验一致
  feature_layers: [3, 6, 9, 12]  # 提取第3、6、9、12层特征

# 半监督学习配置
ssl:
  # 教师模型配置
  teacher_ema_alpha: 0.999  # EMA更新系数
  confidence_threshold: 0.95  # 伪标签置信度阈值
  
  # 损失权重
  supervised_loss_weight: 1.0
  unsupervised_loss_weight: 0.5
  
  # 损失函数
  loss_type: "focal_dice"  # focal + dice
  focal_alpha: 0.25
  focal_gamma: 2.0
  dice_smooth: 1.0

# 训练配置
training:
  epochs: 200
  learning_rate: 1e-4
  weight_decay: 1e-4
  optimizer: "adamw"
  
  # 学习率调度
  scheduler: "cosine"
  warmup_epochs: 10
  min_lr: 1e-6
  
  # 验证和保存
  val_interval: 5
  save_interval: 10
  early_stopping_patience: 30
  
  # 梯度配置
  gradient_clip_val: 1.0
  accumulate_grad_batches: 1

# 评估配置
evaluation:
  metrics: ["miou", "dice", "pixel_accuracy"]
  save_predictions: true
  save_visualizations: true

# 输出配置
output:
  base_dir: "/home/<USER>/PycharmProjects/20250629/training_refactored/ssl_student_teacher/outputs"
  checkpoints_dir: "checkpoints"
  logs_dir: "logs"
  visualizations_dir: "visualizations"
  predictions_dir: "predictions"

# 日志配置
logging:
  level: "INFO"
  save_logs: true
  tensorboard: true
  wandb: false
