#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指数移动平均(EMA)教师模型更新机制
"""

import torch
import torch.nn as nn
from typing import Dict, Any
import copy
import logging

class EMATeacher:
    """EMA教师模型"""
    
    def __init__(self, student_model: nn.Module, alpha: float = 0.999):
        """
        初始化EMA教师模型
        
        Args:
            student_model: 学生模型
            alpha: EMA衰减系数，越接近1更新越慢
        """
        self.alpha = alpha
        self.student_model = student_model
        
        # 创建教师模型（深拷贝学生模型）
        self.teacher_model = copy.deepcopy(student_model)
        
        # 冻结教师模型参数（不需要梯度）
        for param in self.teacher_model.parameters():
            param.requires_grad = False
        
        # 设置为评估模式
        self.teacher_model.eval()
        
        logging.info(f"EMA教师模型初始化完成，alpha={alpha}")
    
    def update(self, step: int = None):
        """
        使用EMA更新教师模型权重
        
        Args:
            step: 当前训练步数（可选，用于动态调整alpha）
        """
        # 可选：动态调整alpha
        if step is not None:
            # 在训练初期使用较小的alpha，后期使用较大的alpha
            alpha = min(self.alpha, (1 + step) / (10 + step))
        else:
            alpha = self.alpha
        
        # EMA更新：teacher = alpha * teacher + (1 - alpha) * student
        with torch.no_grad():
            for teacher_param, student_param in zip(
                self.teacher_model.parameters(),
                self.student_model.parameters()
            ):
                teacher_param.data.mul_(alpha).add_(
                    student_param.data, alpha=1 - alpha
                )
    
    def get_teacher_model(self) -> nn.Module:
        """获取教师模型"""
        return self.teacher_model
    
    def save_teacher_state(self, path: str):
        """保存教师模型状态"""
        torch.save({
            'teacher_state_dict': self.teacher_model.state_dict(),
            'alpha': self.alpha
        }, path)
        logging.info(f"教师模型状态已保存到: {path}")
    
    def load_teacher_state(self, path: str):
        """加载教师模型状态"""
        checkpoint = torch.load(path, map_location='cpu')
        self.teacher_model.load_state_dict(checkpoint['teacher_state_dict'])
        self.alpha = checkpoint.get('alpha', self.alpha)
        logging.info(f"教师模型状态已从 {path} 加载")

class ConsistencyRegularization:
    """一致性正则化"""
    
    def __init__(self, consistency_weight: float = 1.0, ramp_up_epochs: int = 50):
        """
        初始化一致性正则化
        
        Args:
            consistency_weight: 一致性损失权重
            ramp_up_epochs: 权重渐增的轮数
        """
        self.consistency_weight = consistency_weight
        self.ramp_up_epochs = ramp_up_epochs
    
    def get_current_weight(self, epoch: int) -> float:
        """
        获取当前轮次的一致性权重
        
        Args:
            epoch: 当前轮数
            
        Returns:
            当前权重
        """
        if epoch < self.ramp_up_epochs:
            # 使用sigmoid函数进行平滑渐增
            ramp_up_factor = self._sigmoid_ramp_up(epoch, self.ramp_up_epochs)
        else:
            ramp_up_factor = 1.0
        
        return self.consistency_weight * ramp_up_factor
    
    def _sigmoid_ramp_up(self, current_epoch: int, ramp_up_epochs: int) -> float:
        """Sigmoid渐增函数"""
        if ramp_up_epochs == 0:
            return 1.0
        
        phase = 1.0 - current_epoch / ramp_up_epochs
        return float(torch.exp(-5.0 * phase * phase))

class ConfidenceBasedMasking:
    """基于置信度的掩码生成"""
    
    def __init__(
        self,
        confidence_threshold: float = 0.95,
        temperature: float = 1.0,
        use_dynamic_threshold: bool = True
    ):
        """
        初始化置信度掩码
        
        Args:
            confidence_threshold: 置信度阈值
            temperature: 温度参数，用于软化概率分布
            use_dynamic_threshold: 是否使用动态阈值
        """
        self.confidence_threshold = confidence_threshold
        self.temperature = temperature
        self.use_dynamic_threshold = use_dynamic_threshold
        self.threshold_history = []
    
    def generate_mask(
        self,
        predictions: torch.Tensor,
        epoch: int = 0,
        adaptive_threshold: bool = True
    ) -> tuple:
        """
        生成置信度掩码和伪标签
        
        Args:
            predictions: 教师模型预测 (N, C, H, W)
            epoch: 当前轮数
            adaptive_threshold: 是否使用自适应阈值
            
        Returns:
            pseudo_labels: 伪标签 (N, H, W)
            confidence_mask: 置信度掩码 (N, H, W)
            mask_ratio: 掩码比例
        """
        with torch.no_grad():
            # 应用温度缩放
            scaled_predictions = predictions / self.temperature
            
            # 转换为概率
            probs = torch.softmax(scaled_predictions, dim=1)
            
            # 获取最大概率和对应类别
            max_probs, pseudo_labels = torch.max(probs, dim=1)
            
            # 动态调整阈值
            if self.use_dynamic_threshold and adaptive_threshold:
                current_threshold = self._get_adaptive_threshold(max_probs, epoch)
            else:
                current_threshold = self.confidence_threshold
            
            # 生成置信度掩码
            confidence_mask = (max_probs >= current_threshold).float()
            
            # 计算掩码比例
            mask_ratio = confidence_mask.mean().item()
            
            # 记录阈值历史
            self.threshold_history.append(current_threshold)
            if len(self.threshold_history) > 100:  # 只保留最近100个值
                self.threshold_history.pop(0)
        
        return pseudo_labels, confidence_mask, mask_ratio, current_threshold
    
    def _get_adaptive_threshold(self, max_probs: torch.Tensor, epoch: int) -> float:
        """
        获取自适应阈值
        
        Args:
            max_probs: 最大概率张量
            epoch: 当前轮数
            
        Returns:
            自适应阈值
        """
        # 计算当前批次的概率分布统计
        mean_prob = max_probs.mean().item()
        std_prob = max_probs.std().item()
        
        # 基于统计信息调整阈值
        # 在训练初期使用较低的阈值，随着训练进行逐渐提高
        base_threshold = self.confidence_threshold
        
        # 根据概率分布调整
        if mean_prob > 0.8:  # 如果平均置信度较高
            adaptive_threshold = min(base_threshold + 0.02, 0.98)
        elif mean_prob < 0.6:  # 如果平均置信度较低
            adaptive_threshold = max(base_threshold - 0.05, 0.7)
        else:
            adaptive_threshold = base_threshold
        
        # 根据训练进度调整
        progress_factor = min(1.0, epoch / 50)  # 前50轮逐渐提高
        adaptive_threshold = (
            self.confidence_threshold * 0.8 * (1 - progress_factor) +
            adaptive_threshold * progress_factor
        )
        
        return adaptive_threshold
    
    def get_threshold_stats(self) -> Dict[str, float]:
        """获取阈值统计信息"""
        if not self.threshold_history:
            return {}
        
        return {
            'mean_threshold': sum(self.threshold_history) / len(self.threshold_history),
            'current_threshold': self.threshold_history[-1],
            'min_threshold': min(self.threshold_history),
            'max_threshold': max(self.threshold_history)
        }

class PseudoLabelQualityMonitor:
    """伪标签质量监控"""
    
    def __init__(self, window_size: int = 100):
        """
        初始化质量监控器
        
        Args:
            window_size: 滑动窗口大小
        """
        self.window_size = window_size
        self.mask_ratios = []
        self.confidence_scores = []
        self.consistency_scores = []
    
    def update(
        self,
        mask_ratio: float,
        confidence_score: float,
        consistency_score: float = None
    ):
        """更新监控指标"""
        self.mask_ratios.append(mask_ratio)
        self.confidence_scores.append(confidence_score)
        
        if consistency_score is not None:
            self.consistency_scores.append(consistency_score)
        
        # 保持窗口大小
        if len(self.mask_ratios) > self.window_size:
            self.mask_ratios.pop(0)
            self.confidence_scores.pop(0)
            if self.consistency_scores:
                self.consistency_scores.pop(0)
    
    def get_stats(self) -> Dict[str, float]:
        """获取统计信息"""
        if not self.mask_ratios:
            return {}
        
        stats = {
            'avg_mask_ratio': sum(self.mask_ratios) / len(self.mask_ratios),
            'avg_confidence': sum(self.confidence_scores) / len(self.confidence_scores),
            'current_mask_ratio': self.mask_ratios[-1],
            'current_confidence': self.confidence_scores[-1]
        }
        
        if self.consistency_scores:
            stats['avg_consistency'] = sum(self.consistency_scores) / len(self.consistency_scores)
            stats['current_consistency'] = self.consistency_scores[-1]
        
        return stats
