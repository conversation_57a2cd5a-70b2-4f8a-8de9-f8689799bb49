#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
损失函数模块
包含Focal Loss、Dice Loss和半监督学习损失
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Tuple
import numpy as np

class FocalLoss(nn.Module):
    """Focal Loss用于处理类别不平衡"""
    
    def __init__(self, alpha: float = 0.25, gamma: float = 2.0, reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            inputs: (N, C, H, W) 预测logits
            targets: (N, H, W) 真实标签
        """
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class DiceLoss(nn.Module):
    """Dice Loss用于分割任务"""
    
    def __init__(self, smooth: float = 1.0, reduction: str = 'mean'):
        super().__init__()
        self.smooth = smooth
        self.reduction = reduction
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            inputs: (N, C, H, W) 预测logits
            targets: (N, H, W) 真实标签
        """
        # 转换为概率
        inputs = F.softmax(inputs, dim=1)
        
        # 转换targets为one-hot编码
        num_classes = inputs.shape[1]
        targets_one_hot = F.one_hot(targets, num_classes).permute(0, 3, 1, 2).float()
        
        # 计算Dice系数
        intersection = (inputs * targets_one_hot).sum(dim=(2, 3))
        union = inputs.sum(dim=(2, 3)) + targets_one_hot.sum(dim=(2, 3))
        
        dice = (2.0 * intersection + self.smooth) / (union + self.smooth)
        dice_loss = 1 - dice
        
        if self.reduction == 'mean':
            return dice_loss.mean()
        elif self.reduction == 'sum':
            return dice_loss.sum()
        else:
            return dice_loss

class FocalDiceLoss(nn.Module):
    """Focal + Dice组合损失"""
    
    def __init__(
        self,
        focal_alpha: float = 0.25,
        focal_gamma: float = 2.0,
        dice_smooth: float = 1.0,
        focal_weight: float = 0.5,
        dice_weight: float = 0.5
    ):
        super().__init__()
        self.focal_loss = FocalLoss(focal_alpha, focal_gamma)
        self.dice_loss = DiceLoss(dice_smooth)
        self.focal_weight = focal_weight
        self.dice_weight = dice_weight
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> Dict[str, torch.Tensor]:
        """计算组合损失"""
        focal = self.focal_loss(inputs, targets)
        dice = self.dice_loss(inputs, targets)
        
        total_loss = self.focal_weight * focal + self.dice_weight * dice
        
        return {
            'total': total_loss,
            'focal': focal,
            'dice': dice
        }

class ConsistencyLoss(nn.Module):
    """一致性损失用于半监督学习"""
    
    def __init__(self, consistency_type: str = 'mse', temperature: float = 1.0):
        super().__init__()
        self.consistency_type = consistency_type
        self.temperature = temperature
    
    def forward(self, pred_weak: torch.Tensor, pred_strong: torch.Tensor) -> torch.Tensor:
        """
        计算弱增强和强增强预测之间的一致性损失
        
        Args:
            pred_weak: 弱增强预测 (N, C, H, W)
            pred_strong: 强增强预测 (N, C, H, W)
        """
        if self.consistency_type == 'mse':
            # MSE一致性损失
            pred_weak_soft = F.softmax(pred_weak / self.temperature, dim=1)
            pred_strong_soft = F.softmax(pred_strong / self.temperature, dim=1)
            return F.mse_loss(pred_strong_soft, pred_weak_soft)
        
        elif self.consistency_type == 'kl':
            # KL散度一致性损失
            pred_weak_log = F.log_softmax(pred_weak / self.temperature, dim=1)
            pred_strong_soft = F.softmax(pred_strong / self.temperature, dim=1)
            return F.kl_div(pred_weak_log, pred_strong_soft, reduction='batchmean')
        
        else:
            raise ValueError(f"Unknown consistency type: {self.consistency_type}")

class PseudoLabelLoss(nn.Module):
    """伪标签损失"""
    
    def __init__(self, confidence_threshold: float = 0.95):
        super().__init__()
        self.confidence_threshold = confidence_threshold
        self.ce_loss = nn.CrossEntropyLoss(reduction='none')
    
    def forward(
        self,
        pred_strong: torch.Tensor,
        pseudo_labels: torch.Tensor,
        confidence_mask: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        计算伪标签损失
        
        Args:
            pred_strong: 强增强预测 (N, C, H, W)
            pseudo_labels: 伪标签 (N, H, W)
            confidence_mask: 置信度掩码 (N, H, W)
        
        Returns:
            loss: 伪标签损失
            mask_ratio: 有效像素比例
        """
        # 计算交叉熵损失
        ce_loss = self.ce_loss(pred_strong, pseudo_labels)
        
        # 应用置信度掩码
        masked_loss = ce_loss * confidence_mask
        
        # 计算平均损失（只考虑高置信度像素）
        valid_pixels = confidence_mask.sum()
        if valid_pixels > 0:
            loss = masked_loss.sum() / valid_pixels
            mask_ratio = valid_pixels / confidence_mask.numel()
        else:
            loss = torch.tensor(0.0, device=pred_strong.device)
            mask_ratio = torch.tensor(0.0, device=pred_strong.device)
        
        return loss, mask_ratio

class SSLLoss(nn.Module):
    """半监督学习总损失"""
    
    def __init__(self, config: Dict):
        super().__init__()
        self.config = config
        
        # 监督损失
        self.supervised_loss = FocalDiceLoss(
            focal_alpha=config['ssl']['focal_alpha'],
            focal_gamma=config['ssl']['focal_gamma'],
            dice_smooth=config['ssl']['dice_smooth']
        )
        
        # 无监督损失
        self.pseudo_label_loss = PseudoLabelLoss(
            confidence_threshold=config['ssl']['confidence_threshold']
        )
        
        self.consistency_loss = ConsistencyLoss()
        
        # 损失权重
        self.supervised_weight = config['ssl']['supervised_loss_weight']
        self.unsupervised_weight = config['ssl']['unsupervised_loss_weight']
    
    def generate_pseudo_labels(
        self,
        pred_weak: torch.Tensor,
        confidence_threshold: float = 0.95
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        生成伪标签
        
        Args:
            pred_weak: 教师模型弱增强预测 (N, C, H, W)
            confidence_threshold: 置信度阈值
        
        Returns:
            pseudo_labels: 伪标签 (N, H, W)
            confidence_mask: 置信度掩码 (N, H, W)
        """
        with torch.no_grad():
            # 转换为概率
            pred_soft = F.softmax(pred_weak, dim=1)
            
            # 获取最大概率和对应的类别
            max_probs, pseudo_labels = torch.max(pred_soft, dim=1)
            
            # 生成置信度掩码
            confidence_mask = (max_probs >= confidence_threshold).float()
        
        return pseudo_labels, confidence_mask
    
    def forward(
        self,
        # 监督数据
        pred_labeled: torch.Tensor,
        targets_labeled: torch.Tensor,
        # 无监督数据
        pred_weak_unlabeled: torch.Tensor,
        pred_strong_unlabeled: torch.Tensor,
        # 其他参数
        epoch: int = 0,
        max_epochs: int = 200
    ) -> Dict[str, torch.Tensor]:
        """
        计算总损失
        
        Args:
            pred_labeled: 有标注数据预测 (N1, C, H, W)
            targets_labeled: 有标注数据标签 (N1, H, W)
            pred_weak_unlabeled: 无标注数据弱增强预测 (N2, C, H, W)
            pred_strong_unlabeled: 无标注数据强增强预测 (N2, C, H, W)
            epoch: 当前轮数
            max_epochs: 总轮数
        """
        losses = {}
        
        # 1. 监督损失 - 使用主实验的CombinedLoss
        if not hasattr(self, '_combined_loss'):
            from pathlib import Path
            import sys
            main_experiment_path = Path(__file__).parent.parent.parent
            sys.path.append(str(main_experiment_path))
            from core_main.losses.combined_loss import CombinedLoss

            self._combined_loss = CombinedLoss(
                dice_weight=0.5,
                ce_weight=0.3,
                focal_weight=0.2,
                focal_alpha=None,
                focal_gamma=2.0,
                class_weights=torch.tensor([2.3, 4.4, 50.0, 8.6, 2.2, 36.2, 9.3, 13.7]).to(pred_labeled.device),
                ignore_index=-100
            )

        supervised_loss_dict = self._combined_loss(pred_labeled, targets_labeled)
        losses['supervised_total'] = supervised_loss_dict['total_loss']
        losses['supervised_focal'] = supervised_loss_dict['focal_loss']
        losses['supervised_dice'] = supervised_loss_dict['dice_loss']
        
        # 2. 无监督损失
        if pred_weak_unlabeled is not None and pred_strong_unlabeled is not None:
            # 生成伪标签
            pseudo_labels, confidence_mask = self.generate_pseudo_labels(
                pred_weak_unlabeled,
                self.config['ssl']['confidence_threshold']
            )
            
            # 伪标签损失
            pseudo_loss, mask_ratio = self.pseudo_label_loss(
                pred_strong_unlabeled,
                pseudo_labels,
                confidence_mask
            )
            
            # 一致性损失
            consistency_loss = self.consistency_loss(pred_weak_unlabeled, pred_strong_unlabeled)
            
            # 无监督总损失
            unsupervised_loss = pseudo_loss + 0.1 * consistency_loss
            
            losses['unsupervised_total'] = unsupervised_loss
            losses['pseudo_label'] = pseudo_loss
            losses['consistency'] = consistency_loss
            losses['mask_ratio'] = mask_ratio
        else:
            losses['unsupervised_total'] = torch.tensor(0.0, device=pred_labeled.device)
            losses['pseudo_label'] = torch.tensor(0.0, device=pred_labeled.device)
            losses['consistency'] = torch.tensor(0.0, device=pred_labeled.device)
            losses['mask_ratio'] = torch.tensor(0.0, device=pred_labeled.device)
        
        # 3. 总损失
        # 使用渐进式权重调整
        ramp_up_factor = min(1.0, epoch / (max_epochs * 0.1))  # 前10%轮数逐渐增加无监督权重
        unsupervised_weight = self.unsupervised_weight * ramp_up_factor
        
        total_loss = (
            self.supervised_weight * losses['supervised_total'] +
            unsupervised_weight * losses['unsupervised_total']
        )
        
        losses['total'] = total_loss
        losses['unsupervised_weight'] = unsupervised_weight
        
        return losses
