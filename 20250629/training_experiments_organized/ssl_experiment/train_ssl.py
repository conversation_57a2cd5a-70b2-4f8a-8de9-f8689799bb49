#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
半监督学习主训练脚本
使用学生-教师模型进行蜂巢分割
"""

import os
import sys
import argparse
import logging
from pathlib import Path
import torch
import yaml

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from training.ssl_trainer import SSLTrainer

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='半监督学习训练脚本')
    
    parser.add_argument(
        '--config',
        type=str,
        default='configs/ssl_config.yaml',
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--fold',
        type=int,
        default=0,
        choices=[0, 1, 2, 3, 4],
        help='K-fold交叉验证的fold编号'
    )
    
    parser.add_argument(
        '--resume',
        type=str,
        default=None,
        help='恢复训练的检查点路径'
    )
    
    parser.add_argument(
        '--device',
        type=str,
        default=None,
        help='训练设备 (cuda:0, cuda:1, cpu)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='调试模式，使用较少的数据'
    )
    
    return parser.parse_args()

def setup_environment():
    """设置环境"""
    # 设置CUDA环境变量
    os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        logging.info(f"CUDA可用，设备数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            logging.info(f"GPU {i}: {props.name}, 内存: {props.total_memory/1024**3:.1f}GB")
    else:
        logging.info("CUDA不可用，将使用CPU")

def validate_config(config: dict, args):
    """验证配置"""
    # 检查必要的路径
    required_paths = [
        config['data']['labeled_train_images'],
        config['data']['labeled_train_masks'],
        config['data']['labeled_val_images'],
        config['data']['labeled_val_masks'],
        config['data']['unlabeled_images']
    ]
    
    for path in required_paths:
        if not Path(path).exists():
            raise FileNotFoundError(f"路径不存在: {path}")
    
    # 检查MAE预训练权重
    if config['model']['mae_checkpoint']:
        mae_path = Path(config['model']['mae_checkpoint'])
        if not mae_path.exists():
            logging.warning(f"MAE预训练权重不存在: {mae_path}")
            config['model']['mae_checkpoint'] = None
    
    # 覆盖设备设置
    if args.device:
        config['device'] = args.device
        logging.info(f"使用命令行指定的设备: {args.device}")
    
    # 调试模式设置
    if args.debug:
        config['training']['epochs'] = 10
        config['training']['val_interval'] = 2
        config['training']['save_interval'] = 5
        config['data']['labeled_batch_size'] = 1  # RTX 2080显存限制
        config['data']['unlabeled_batch_size'] = 1  # 进一步减少以节省显存
        logging.info("调试模式已启用，批次大小已调整以适应RTX 2080显存")

def main():
    """主函数"""
    args = parse_args()
    
    # 设置基础日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 设置环境
    setup_environment()
    
    # 加载配置
    config_path = Path(args.config)
    if not config_path.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 验证配置
    validate_config(config, args)
    
    # 打印配置信息
    logging.info("=" * 60)
    logging.info("半监督学习训练配置")
    logging.info("=" * 60)
    logging.info(f"实验名称: {config['experiment_name']}")
    logging.info(f"设备: {config['device']}")
    logging.info(f"Fold: {args.fold}")
    logging.info(f"训练轮数: {config['training']['epochs']}")
    logging.info(f"学习率: {config['training']['learning_rate']}")
    logging.info(f"有标注批次大小: {config['data']['labeled_batch_size']}")
    logging.info(f"无标注批次大小: {config['data']['unlabeled_batch_size']}")
    logging.info(f"置信度阈值: {config['ssl']['confidence_threshold']}")
    logging.info(f"EMA系数: {config['ssl']['teacher_ema_alpha']}")
    logging.info("=" * 60)
    
    try:
        # 创建训练器
        trainer = SSLTrainer(config, fold=args.fold)
        
        # 恢复训练（如果指定）
        if args.resume:
            resume_path = Path(args.resume)
            if resume_path.exists():
                trainer.load_checkpoint(str(resume_path))
                logging.info(f"从检查点恢复训练: {resume_path}")
            else:
                logging.warning(f"检查点文件不存在: {resume_path}")
        
        # 开始训练
        trainer.train()
        
        logging.info("训练成功完成！")
        
    except KeyboardInterrupt:
        logging.info("训练被用户中断")
        sys.exit(0)
        
    except Exception as e:
        logging.error(f"训练过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
