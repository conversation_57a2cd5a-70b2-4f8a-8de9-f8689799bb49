#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理和划分脚本
将图片分为有标注和无标注两个数据集，并实现K-Fold交叉验证
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, List, Tuple
import pandas as pd
from sklearn.model_selection import KFold
import yaml
import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def load_config(config_path: str) -> Dict:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def get_annotated_images(annotation_dir: str) -> List[str]:
    """获取有标注的图片列表"""
    annotation_dir = Path(annotation_dir)
    annotated_images = []

    for json_file in annotation_dir.glob("*.json"):
        image_name = json_file.stem  # 去掉.json后缀
        annotated_images.append(image_name)

    logging.info(f"找到 {len(annotated_images)} 张有标注的图片")
    return annotated_images

def get_all_images(image_dir: str) -> List[str]:
    """获取所有图片列表"""
    image_dir = Path(image_dir)
    all_images = []

    # 支持的图片格式
    image_extensions = ['.png', '.jpg', '.jpeg', '.JPG', '.bmp', '.tif', '.tiff']

    for ext in image_extensions:
        for img_file in image_dir.glob(f"*{ext}"):
            # 提取图片名称（不含扩展名）
            image_name = img_file.stem
            all_images.append(image_name)

    # 去重并排序
    all_images = sorted(list(set(all_images)))
    logging.info(f"找到 {len(all_images)} 张图片")
    return all_images

def separate_labeled_unlabeled(all_images: List[str], annotated_images: List[str]) -> Tuple[List[str], List[str]]:
    """分离有标注和无标注的图片"""
    labeled_images = []
    unlabeled_images = []

    for img_name in all_images:
        if img_name in annotated_images:
            labeled_images.append(img_name)
        else:
            unlabeled_images.append(img_name)

    logging.info(f"有标注图片: {len(labeled_images)} 张")
    logging.info(f"无标注图片: {len(unlabeled_images)} 张")

    return labeled_images, unlabeled_images

def create_kfold_splits(labeled_images: List[str], k_folds: int, seed: int) -> List[Dict]:
    """创建K-Fold交叉验证划分"""
    kfold = KFold(n_splits=k_folds, shuffle=True, random_state=seed)
    splits = []
    
    for fold, (train_idx, val_idx) in enumerate(kfold.split(labeled_images)):
        train_images = [labeled_images[i] for i in train_idx]
        val_images = [labeled_images[i] for i in val_idx]
        
        splits.append({
            'fold': fold,
            'train': train_images,
            'val': val_images
        })
        
        logging.info(f"Fold {fold}: 训练集 {len(train_images)} 张, 验证集 {len(val_images)} 张")
    
    return splits

def create_data_directories(output_dir: str, k_folds: int):
    """创建数据目录结构"""
    output_dir = Path(output_dir)
    
    # 创建主目录
    directories = [
        "labeled_images",
        "unlabeled_images", 
        "annotations",
        "splits"
    ]
    
    for dir_name in directories:
        (output_dir / dir_name).mkdir(parents=True, exist_ok=True)
    
    # 创建每个fold的目录
    for fold in range(k_folds):
        fold_dir = output_dir / "splits" / f"fold_{fold}"
        for split in ["train", "val"]:
            (fold_dir / split).mkdir(parents=True, exist_ok=True)

def copy_images_and_annotations(
    source_image_dir: str,
    source_annotation_dir: str,
    output_dir: str,
    labeled_images: List[str],
    unlabeled_images: List[str],
    annotated_images: List[str]
):
    """复制图片和标注文件到目标目录"""
    source_image_dir = Path(source_image_dir)
    source_annotation_dir = Path(source_annotation_dir)
    output_dir = Path(output_dir)
    
    # 复制有标注的图片
    labeled_dir = output_dir / "labeled_images"
    for img in labeled_images:
        src_path = source_image_dir / img
        if src_path.exists():
            shutil.copy2(src_path, labeled_dir / img)
    
    # 复制无标注的图片
    unlabeled_dir = output_dir / "unlabeled_images"
    for img in unlabeled_images:
        src_path = source_image_dir / img
        if src_path.exists():
            shutil.copy2(src_path, unlabeled_dir / img)
    
    # 复制标注文件
    annotation_dir = output_dir / "annotations"
    for ann_name in annotated_images:
        # 复制JSON文件
        json_src = source_annotation_dir / f"{ann_name}.json"
        if json_src.exists():
            shutil.copy2(json_src, annotation_dir / f"{ann_name}.json")
        
        # 复制CSV文件（如果存在）
        csv_src = source_annotation_dir / f"{ann_name}.csv"
        if csv_src.exists():
            shutil.copy2(csv_src, annotation_dir / f"{ann_name}.csv")

def save_splits_info(splits: List[Dict], output_dir: str):
    """保存划分信息"""
    output_dir = Path(output_dir)
    
    # 保存完整的划分信息
    splits_file = output_dir / "splits" / "kfold_splits.json"
    with open(splits_file, 'w', encoding='utf-8') as f:
        json.dump(splits, f, indent=2, ensure_ascii=False)
    
    # 为每个fold创建单独的文件
    for split in splits:
        fold = split['fold']
        fold_dir = output_dir / "splits" / f"fold_{fold}"
        
        # 保存训练集列表
        train_file = fold_dir / "train_images.txt"
        with open(train_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(split['train']))
        
        # 保存验证集列表
        val_file = fold_dir / "val_images.txt"
        with open(val_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(split['val']))

def main():
    """主函数"""
    setup_logging()
    
    # 加载配置
    config_path = "/home/<USER>/PycharmProjects/20250629/training_refactored/ssl_student_teacher/configs/ssl_config.yaml"
    config = load_config(config_path)
    
    # 获取配置参数
    image_dir = config['data']['all_images_dir']
    annotation_dir = config['data']['annotations_dir']
    k_folds = config['data']['k_folds']
    seed = config['seed']
    output_dir = config['output']['base_dir'] + "/data"
    
    logging.info("开始数据预处理和划分...")
    
    # 1. 获取图片列表
    all_images = get_all_images(image_dir)
    annotated_images = get_annotated_images(annotation_dir)
    
    # 2. 分离有标注和无标注图片
    labeled_images, unlabeled_images = separate_labeled_unlabeled(all_images, annotated_images)
    
    # 3. 创建K-Fold划分
    splits = create_kfold_splits(labeled_images, k_folds, seed)
    
    # 4. 创建目录结构
    create_data_directories(output_dir, k_folds)
    
    # 5. 复制文件
    copy_images_and_annotations(
        image_dir, annotation_dir, output_dir,
        labeled_images, unlabeled_images, annotated_images
    )
    
    # 6. 保存划分信息
    save_splits_info(splits, output_dir)
    
    # 7. 生成统计报告
    stats = {
        'total_images': len(all_images),
        'labeled_images': len(labeled_images),
        'unlabeled_images': len(unlabeled_images),
        'annotated_base_images': len(annotated_images),
        'k_folds': k_folds,
        'labeled_unlabeled_ratio': f"1:{len(unlabeled_images)//len(labeled_images) if labeled_images else 0}"
    }
    
    stats_file = Path(output_dir) / "data_statistics.json"
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2, ensure_ascii=False)
    
    logging.info("数据预处理完成！")
    logging.info(f"统计信息: {stats}")

if __name__ == "__main__":
    main()
