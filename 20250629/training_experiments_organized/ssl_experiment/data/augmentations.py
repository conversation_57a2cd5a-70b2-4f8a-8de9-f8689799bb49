#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据增强模块
实现强弱数据增强策略用于半监督学习
"""

import torch
import torch.nn as nn
import torchvision.transforms as T
import torchvision.transforms.functional as TF
from torchvision.transforms import InterpolationMode
import albumentations as A
from albumentations.pytorch import ToTensorV2
import numpy as np
import random
from typing import Dict, Any, Tuple
import cv2

class WeakAugmentation:
    """弱增强：用于教师模型生成伪标签"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.transform = A.<PERSON>mpose([
            # 基础几何变换
            A.HorizontalFlip(p=config.get('horizontal_flip', 0.5)),
            A.VerticalFlip(p=config.get('vertical_flip', 0.5)),
            A.Rotate(
                limit=config.get('rotation', 15),
                interpolation=cv2.INTER_LINEAR,
                border_mode=cv2.BORDER_REFLECT_101,
                p=0.7
            ),
            
            # 轻微的颜色变换
            A.RandomBrightnessContrast(
                brightness_limit=config.get('brightness', 0.1),
                contrast_limit=config.get('contrast', 0.1),
                p=0.5
            ),
            
            # 归一化和转换为tensor
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])
    
    def __call__(self, image: np.ndarray, mask: np.ndarray = None) -> Dict[str, torch.Tensor]:
        """应用弱增强"""
        if mask is not None:
            augmented = self.transform(image=image, mask=mask)
            return {
                'image': augmented['image'],
                'mask': augmented['mask']
            }
        else:
            augmented = self.transform(image=image)
            return {'image': augmented['image']}

class StrongAugmentation:
    """强增强：用于学生模型训练"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.transform = A.Compose([
            # 几何变换
            A.HorizontalFlip(p=config.get('horizontal_flip', 0.5)),
            A.VerticalFlip(p=config.get('vertical_flip', 0.5)),
            A.Rotate(
                limit=config.get('rotation', 30),
                interpolation=cv2.INTER_LINEAR,
                border_mode=cv2.BORDER_REFLECT_101,
                p=0.8
            ),
            
            # 强烈的颜色变换
            A.RandomBrightnessContrast(
                brightness_limit=config.get('brightness', 0.2),
                contrast_limit=config.get('contrast', 0.2),
                p=0.7
            ),
            
            # 颜色抖动
            A.ColorJitter(
                brightness=0.2,
                contrast=0.2,
                saturation=0.2,
                hue=0.1,
                p=config.get('color_jitter', 0.4)
            ),
            
            # 高斯模糊
            A.GaussianBlur(
                blur_limit=(3, 7),
                p=config.get('gaussian_blur', 0.3)
            ),
            
            # 随机擦除
            A.CoarseDropout(
                max_holes=8,
                max_height=32,
                max_width=32,
                min_holes=1,
                min_height=8,
                min_width=8,
                fill_value=0,
                p=config.get('cutout', 0.2)
            ),
            
            # 弹性变换
            A.ElasticTransform(
                alpha=50,
                sigma=5,
                alpha_affine=5,
                interpolation=cv2.INTER_LINEAR,
                border_mode=cv2.BORDER_REFLECT_101,
                p=0.3
            ),
            
            # 网格扭曲
            A.GridDistortion(
                num_steps=5,
                distort_limit=0.1,
                interpolation=cv2.INTER_LINEAR,
                border_mode=cv2.BORDER_REFLECT_101,
                p=0.3
            ),
            
            # 归一化和转换为tensor
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])
    
    def __call__(self, image: np.ndarray, mask: np.ndarray = None) -> Dict[str, torch.Tensor]:
        """应用强增强"""
        if mask is not None:
            augmented = self.transform(image=image, mask=mask)
            return {
                'image': augmented['image'],
                'mask': augmented['mask']
            }
        else:
            augmented = self.transform(image=image)
            return {'image': augmented['image']}

class ValidationTransform:
    """验证时的变换：只进行归一化"""
    
    def __init__(self):
        self.transform = A.Compose([
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])
    
    def __call__(self, image: np.ndarray, mask: np.ndarray = None) -> Dict[str, torch.Tensor]:
        """应用验证变换"""
        if mask is not None:
            augmented = self.transform(image=image, mask=mask)
            return {
                'image': augmented['image'],
                'mask': augmented['mask']
            }
        else:
            augmented = self.transform(image=image)
            return {'image': augmented['image']}

class MixUp:
    """MixUp数据增强"""
    
    def __init__(self, alpha: float = 0.2):
        self.alpha = alpha
    
    def __call__(self, x1: torch.Tensor, x2: torch.Tensor, y1: torch.Tensor = None, y2: torch.Tensor = None):
        """应用MixUp"""
        if self.alpha > 0:
            lam = np.random.beta(self.alpha, self.alpha)
        else:
            lam = 1
        
        mixed_x = lam * x1 + (1 - lam) * x2
        
        if y1 is not None and y2 is not None:
            mixed_y = lam * y1 + (1 - lam) * y2
            return mixed_x, mixed_y, lam
        
        return mixed_x, lam

class CutMix:
    """CutMix数据增强"""
    
    def __init__(self, alpha: float = 1.0):
        self.alpha = alpha
    
    def __call__(self, x1: torch.Tensor, x2: torch.Tensor, y1: torch.Tensor = None, y2: torch.Tensor = None):
        """应用CutMix"""
        lam = np.random.beta(self.alpha, self.alpha)
        
        batch_size, _, H, W = x1.shape
        cut_rat = np.sqrt(1. - lam)
        cut_w = int(W * cut_rat)
        cut_h = int(H * cut_rat)
        
        # 随机选择切割位置
        cx = np.random.randint(W)
        cy = np.random.randint(H)
        
        bbx1 = np.clip(cx - cut_w // 2, 0, W)
        bby1 = np.clip(cy - cut_h // 2, 0, H)
        bbx2 = np.clip(cx + cut_w // 2, 0, W)
        bby2 = np.clip(cy + cut_h // 2, 0, H)
        
        # 应用CutMix
        mixed_x = x1.clone()
        mixed_x[:, :, bby1:bby2, bbx1:bbx2] = x2[:, :, bby1:bby2, bbx1:bbx2]
        
        # 调整lambda
        lam = 1 - ((bbx2 - bbx1) * (bby2 - bby1) / (W * H))
        
        if y1 is not None and y2 is not None:
            mixed_y = y1.clone()
            mixed_y[:, bby1:bby2, bbx1:bbx2] = y2[:, bby1:bby2, bbx1:bbx2]
            return mixed_x, mixed_y, lam
        
        return mixed_x, lam

def get_augmentation_transforms(config: Dict[str, Any]) -> Tuple[WeakAugmentation, StrongAugmentation, ValidationTransform]:
    """获取所有增强变换"""
    weak_aug = WeakAugmentation(config['data']['weak_augmentation'])
    strong_aug = StrongAugmentation(config['data']['strong_augmentation'])
    val_transform = ValidationTransform()
    
    return weak_aug, strong_aug, val_transform

class ConsistencyLoss(nn.Module):
    """一致性损失：用于半监督学习"""
    
    def __init__(self, consistency_type: str = "mse"):
        super().__init__()
        self.consistency_type = consistency_type
        
        if consistency_type == "mse":
            self.loss_fn = nn.MSELoss()
        elif consistency_type == "kl":
            self.loss_fn = nn.KLDivLoss(reduction='batchmean')
        else:
            raise ValueError(f"Unknown consistency type: {consistency_type}")
    
    def forward(self, pred1: torch.Tensor, pred2: torch.Tensor) -> torch.Tensor:
        """计算一致性损失"""
        if self.consistency_type == "mse":
            return self.loss_fn(pred1, pred2)
        elif self.consistency_type == "kl":
            log_pred1 = torch.log_softmax(pred1, dim=1)
            pred2_softmax = torch.softmax(pred2, dim=1)
            return self.loss_fn(log_pred1, pred2_softmax)

def denormalize_image(tensor: torch.Tensor) -> torch.Tensor:
    """反归一化图像用于可视化"""
    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
    
    if tensor.is_cuda:
        mean = mean.cuda()
        std = std.cuda()
    
    denormalized = tensor * std + mean
    return torch.clamp(denormalized, 0, 1)
