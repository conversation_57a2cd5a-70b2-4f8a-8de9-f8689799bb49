#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集和数据加载器
支持有标注和无标注数据的加载
"""

import os
import json
import cv2
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import pandas as pd
from PIL import Image
import logging

class LabeledDataset(Dataset):
    """有标注数据集 - 使用原始图片和JSON标注，动态生成滑动窗口"""

    def __init__(
        self,
        images_dir: str,
        annotations_dir: str,
        image_list: List[str],
        transform=None,
        image_size: int = 512,
        patch_size: int = 512,
        overlap_ratio: float = 0.25,
        num_classes: int = 3
    ):
        self.images_dir = Path(images_dir)
        self.annotations_dir = Path(annotations_dir)
        self.image_list = image_list
        self.transform = transform
        self.image_size = image_size
        self.patch_size = patch_size
        self.overlap_ratio = overlap_ratio
        self.num_classes = num_classes

        # 生成所有有效的图块
        self.patches = self._generate_patches()

        logging.info(f"LabeledDataset: {len(self.patches)} 个图块来自 {len(image_list)} 张图片")

    def _generate_patches(self) -> List[Dict]:
        """生成所有图块的信息"""
        patches = []

        for img_name in self.image_list:
            # 检查图片和标注是否存在
            img_extensions = ['.jpg', '.JPG', '.png', '.jpeg']
            img_path = None
            for ext in img_extensions:
                potential_path = self.images_dir / f"{img_name}{ext}"
                if potential_path.exists():
                    img_path = potential_path
                    break

            ann_path = self.annotations_dir / f"{img_name}.json"

            if img_path and ann_path.exists():
                # 读取图片尺寸
                import cv2
                img = cv2.imread(str(img_path))
                if img is not None:
                    h, w = img.shape[:2]

                    # 计算滑动窗口
                    stride = int(self.patch_size * (1 - self.overlap_ratio))

                    for y in range(0, h - self.patch_size + 1, stride):
                        for x in range(0, w - self.patch_size + 1, stride):
                            patches.append({
                                'image_name': img_name,
                                'image_path': img_path,
                                'annotation_path': ann_path,
                                'x': x,
                                'y': y,
                                'patch_size': self.patch_size
                            })

        return patches
    
    def __len__(self) -> int:
        return len(self.patches)

    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        patch_info = self.patches[idx]

        # 加载原始图片
        image = cv2.imread(str(patch_info['image_path']))
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # 提取图块
        x, y, patch_size = patch_info['x'], patch_info['y'], patch_info['patch_size']
        image_patch = image[y:y+patch_size, x:x+patch_size]

        # 生成掩码
        mask = self._generate_mask(patch_info)

        # 确保尺寸一致
        if image_patch.shape[:2] != (self.image_size, self.image_size):
            image_patch = cv2.resize(image_patch, (self.image_size, self.image_size))
        if mask.shape != (self.image_size, self.image_size):
            mask = cv2.resize(mask, (self.image_size, self.image_size), interpolation=cv2.INTER_NEAREST)

        # 应用变换
        if self.transform:
            augmented = self.transform(image=image_patch, mask=mask)
            image_patch = augmented['image']
            mask = augmented['mask']
        else:
            image_patch = torch.from_numpy(image_patch.transpose(2, 0, 1)).float() / 255.0
            mask = torch.from_numpy(mask).long()

        return {
            'image': image_patch,
            'mask': mask,
            'image_name': f"{patch_info['image_name']}_patch_{x}_{y}"
        }

    def _generate_mask(self, patch_info: Dict) -> np.ndarray:
        """从JSON标注生成掩码"""
        import json

        # 创建空白掩码
        mask = np.zeros((patch_info['patch_size'], patch_info['patch_size']), dtype=np.uint8)

        try:
            with open(patch_info['annotation_path'], 'r', encoding='utf-8') as f:
                annotations = json.load(f)

            if 'shapes' in annotations:
                for shape in annotations['shapes']:
                    label = shape.get('label', 'unknown').lower()
                    points = shape.get('points', [])

                    if len(points) > 2:  # 多边形
                        # 转换坐标到图块坐标系
                        patch_points = []
                        for point in points:
                            px, py = point
                            # 转换到图块坐标系
                            patch_x = px - patch_info['x']
                            patch_y = py - patch_info['y']

                            # 检查点是否在图块内
                            if 0 <= patch_x < patch_info['patch_size'] and 0 <= patch_y < patch_info['patch_size']:
                                patch_points.append([patch_x, patch_y])

                        if len(patch_points) > 2:
                            pts = np.array(patch_points, dtype=np.int32)

                            # 根据标签设置类别
                            if 'honeycomb' in label or 'cell' in label:
                                class_id = 1  # 蜂巢
                            elif 'honey' in label:
                                class_id = 2  # 蜂蜜
                            else:
                                class_id = 1  # 默认为蜂巢

                            # 填充多边形
                            cv2.fillPoly(mask, [pts], class_id)

        except Exception as e:
            logging.warning(f"生成掩码失败 {patch_info['annotation_path']}: {e}")

        return mask
    


class UnlabeledDataset(Dataset):
    """无标注数据集 - 使用原始图片，动态生成滑动窗口"""

    def __init__(
        self,
        images_dir: str,
        image_list: List[str],
        transform=None,
        image_size: int = 512,
        patch_size: int = 512,
        overlap_ratio: float = 0.25
    ):
        self.images_dir = Path(images_dir)
        self.image_list = image_list
        self.transform = transform
        self.image_size = image_size
        self.patch_size = patch_size
        self.overlap_ratio = overlap_ratio

        # 生成所有有效的图块
        self.patches = self._generate_patches()

        logging.info(f"UnlabeledDataset: {len(self.patches)} 个图块来自 {len(image_list)} 张图片")

    def _generate_patches(self) -> List[Dict]:
        """生成所有图块的信息"""
        patches = []

        for img_name in self.image_list:
            # 检查图片是否存在
            img_extensions = ['.jpg', '.JPG', '.png', '.jpeg']
            img_path = None
            for ext in img_extensions:
                potential_path = self.images_dir / f"{img_name}{ext}"
                if potential_path.exists():
                    img_path = potential_path
                    break

            if img_path:
                # 读取图片尺寸
                import cv2
                img = cv2.imread(str(img_path))
                if img is not None:
                    h, w = img.shape[:2]

                    # 计算滑动窗口
                    stride = int(self.patch_size * (1 - self.overlap_ratio))

                    for y in range(0, h - self.patch_size + 1, stride):
                        for x in range(0, w - self.patch_size + 1, stride):
                            patches.append({
                                'image_name': img_name,
                                'image_path': img_path,
                                'x': x,
                                'y': y,
                                'patch_size': self.patch_size
                            })

        return patches
    
    def __len__(self) -> int:
        return len(self.patches)

    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        patch_info = self.patches[idx]

        # 加载原始图片
        image = cv2.imread(str(patch_info['image_path']))
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # 提取图块
        x, y, patch_size = patch_info['x'], patch_info['y'], patch_info['patch_size']
        image_patch = image[y:y+patch_size, x:x+patch_size]

        # 确保尺寸正确
        if image_patch.shape[:2] != (self.image_size, self.image_size):
            image_patch = cv2.resize(image_patch, (self.image_size, self.image_size))

        # 应用变换
        if self.transform:
            augmented = self.transform(image=image_patch)
            image_patch = augmented['image']
        else:
            image_patch = torch.from_numpy(image_patch.transpose(2, 0, 1)).float() / 255.0

        return {
            'image': image_patch,
            'image_name': f"{patch_info['image_name']}_patch_{x}_{y}"
        }

class SSLDataLoader:
    """半监督学习数据加载器"""
    
    def __init__(self, config: Dict[str, Any], fold: int = 0):
        self.config = config
        self.fold = fold

        # 数据路径
        self.images_dir = Path(config['data']['all_images_dir'])
        self.annotations_dir = Path(config['data']['annotations_dir'])

        # 加载数据划分
        self.splits = self._load_splits()

        # 导入增强变换
        try:
            from .augmentations import get_augmentation_transforms
        except ImportError:
            import sys
            from pathlib import Path
            sys.path.append(str(Path(__file__).parent))
            from augmentations import get_augmentation_transforms

        self.weak_aug, self.strong_aug, self.val_transform = get_augmentation_transforms(config)
    
    def _load_splits(self) -> Dict[str, List[str]]:
        """加载数据划分"""
        splits_file = self.data_dir / "splits" / "kfold_splits.json"
        
        with open(splits_file, 'r', encoding='utf-8') as f:
            all_splits = json.load(f)
        
        # 获取当前fold的划分
        current_split = all_splits[self.fold]
        return {
            'train': current_split['train'],
            'val': current_split['val']
        }
    
    def _get_unlabeled_images(self) -> List[str]:
        """获取无标注图片列表"""
        unlabeled_images = []
        for img_file in self.unlabeled_dir.glob("*.png"):
            unlabeled_images.append(img_file.name)
        return unlabeled_images
    
    def get_train_loaders(self) -> Tuple[DataLoader, DataLoader]:
        """获取训练数据加载器（有标注 + 无标注）"""
        # 有标注训练数据
        labeled_dataset = LabeledDataset(
            image_dir=str(self.labeled_dir),
            annotation_dir=str(self.annotation_dir),
            image_list=self.splits['train'],
            transform=self.strong_aug,  # 学生模型使用强增强
            image_size=self.config['data']['image_size'],
            num_classes=self.config['data']['num_classes']
        )
        
        labeled_loader = DataLoader(
            labeled_dataset,
            batch_size=self.config['data']['labeled_batch_size'],
            shuffle=True,
            num_workers=self.config['num_workers'],
            pin_memory=True,
            drop_last=True
        )
        
        # 无标注训练数据
        unlabeled_images = self._get_unlabeled_images()
        unlabeled_dataset = UnlabeledDataset(
            image_dir=str(self.unlabeled_dir),
            image_list=unlabeled_images,
            transform=self.weak_aug,  # 教师模型使用弱增强
            image_size=self.config['data']['image_size']
        )
        
        unlabeled_loader = DataLoader(
            unlabeled_dataset,
            batch_size=self.config['data']['unlabeled_batch_size'],
            shuffle=True,
            num_workers=self.config['num_workers'],
            pin_memory=True,
            drop_last=True
        )
        
        return labeled_loader, unlabeled_loader
    
    def get_val_loader(self) -> DataLoader:
        """获取验证数据加载器"""
        val_dataset = LabeledDataset(
            image_dir=str(self.labeled_dir),
            annotation_dir=str(self.annotation_dir),
            image_list=self.splits['val'],
            transform=self.val_transform,
            image_size=self.config['data']['image_size'],
            num_classes=self.config['data']['num_classes']
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config['data']['labeled_batch_size'],
            shuffle=False,
            num_workers=self.config['num_workers'],
            pin_memory=True
        )
        
        return val_loader
    
    def get_unlabeled_strong_loader(self) -> DataLoader:
        """获取无标注数据的强增强加载器（用于学生模型）"""
        unlabeled_images = self._get_unlabeled_images()
        unlabeled_dataset = UnlabeledDataset(
            image_dir=str(self.unlabeled_dir),
            image_list=unlabeled_images,
            transform=self.strong_aug,  # 学生模型使用强增强
            image_size=self.config['data']['image_size']
        )
        
        unlabeled_strong_loader = DataLoader(
            unlabeled_dataset,
            batch_size=self.config['data']['unlabeled_batch_size'],
            shuffle=True,
            num_workers=self.config['num_workers'],
            pin_memory=True,
            drop_last=True
        )
        
        return unlabeled_strong_loader
