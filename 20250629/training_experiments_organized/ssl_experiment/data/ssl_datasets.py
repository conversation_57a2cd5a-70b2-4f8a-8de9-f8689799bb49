#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL数据集 - 直接使用主实验的数据结构
"""

import sys
import cv2
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
import random

# 添加主实验路径
main_experiment_path = Path(__file__).parent.parent.parent
sys.path.append(str(main_experiment_path))

# 导入主实验的数据集
from core_main.datasets.honeycomb_dataset import HoneycombDataset

class SSLLabeledDataset(HoneycombDataset):
    """SSL有标注数据集 - 继承主实验的数据集"""

    def __init__(
        self,
        images_dir: str,
        masks_dir: str,
        image_size: int = 512,
        use_augmentation: bool = True,
        normalize_mean: List[float] = None,
        normalize_std: List[float] = None,
        augmentation_config: Dict = None,
        **kwargs
    ):
        # 使用主实验的默认参数
        if normalize_mean is None:
            normalize_mean = [0.485, 0.456, 0.406]
        if normalize_std is None:
            normalize_std = [0.229, 0.224, 0.225]

        # 直接使用主实验的数据集
        super().__init__(
            images_dir=images_dir,
            masks_dir=masks_dir,
            image_size=image_size,
            normalize_mean=normalize_mean,
            normalize_std=normalize_std,
            use_augmentation=use_augmentation,
            augmentation_config=augmentation_config or {},
            **kwargs
        )

        logging.info(f"SSL有标注数据集: {len(self)} 个样本")

class SSLUnlabeledDataset(Dataset):
    """SSL无标注数据集 - 使用MAE训练的图块"""
    
    def __init__(
        self,
        images_dir: str,
        image_size: int = 512,
        transform=None,
        max_samples: Optional[int] = None
    ):
        self.images_dir = Path(images_dir)
        self.image_size = image_size
        self.transform = transform
        
        # 获取所有图像文件
        self.image_files = []
        for ext in ['.png', '.jpg', '.jpeg']:
            self.image_files.extend(list(self.images_dir.glob(f"*{ext}")))
        
        # 限制样本数量（如果指定）
        if max_samples and len(self.image_files) > max_samples:
            self.image_files = random.sample(self.image_files, max_samples)
        
        logging.info(f"SSL无标注数据集: {len(self.image_files)} 个样本")
    
    def __len__(self) -> int:
        return len(self.image_files)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        img_path = self.image_files[idx]
        
        # 加载图像
        image = cv2.imread(str(img_path))
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 调整尺寸
        if image.shape[:2] != (self.image_size, self.image_size):
            image = cv2.resize(image, (self.image_size, self.image_size))
        
        # 应用变换
        if self.transform:
            transformed = self.transform(image=image)
            image = transformed['image']
        else:
            # 默认归一化
            image = image.astype(np.float32) / 255.0
            image = torch.from_numpy(image.transpose(2, 0, 1))
        
        return {
            'image': image,
            'image_name': img_path.name
        }

class SSLDataLoader:
    """SSL数据加载器"""
    
    def __init__(self, config: Dict[str, Any], fold: int = 0):
        self.config = config
        self.fold = fold

        # 使用主实验的增强配置
        self.augmentation_config = config['data'].get('augmentation', {})
        self.normalize_config = config['data']['normalize']
    
    def get_train_loaders(self) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """获取训练数据加载器（有标注 + 无标注弱增强 + 无标注强增强）"""

        # 1. 有标注训练数据（使用主实验的增强）
        labeled_dataset = SSLLabeledDataset(
            images_dir=self.config['data']['labeled_train_images'],
            masks_dir=self.config['data']['labeled_train_masks'],
            image_size=self.config['data']['image_size'][0],
            normalize_mean=self.normalize_config['mean'],
            normalize_std=self.normalize_config['std'],
            use_augmentation=True,
            augmentation_config=self.augmentation_config
        )

        labeled_loader = DataLoader(
            labeled_dataset,
            batch_size=self.config['data']['labeled_batch_size'],
            shuffle=True,
            num_workers=self.config['data']['num_workers'],
            pin_memory=self.config['data']['pin_memory'],
            drop_last=True
        )

        # 2. 无标注数据（弱增强 - 用于教师模型）
        # 创建弱增强变换（只有基本的几何变换）
        weak_aug_config = {
            'enable': True,
            'horizontal_flip': 0.5,
            'vertical_flip': 0.5,
            'random_rotate90': 0.5
        }

        unlabeled_weak_dataset = SSLUnlabeledDataset(
            images_dir=self.config['data']['unlabeled_images'],
            image_size=self.config['data']['image_size'][0],
            transform=None,  # 将在内部创建
            max_samples=10000
        )

        unlabeled_weak_loader = DataLoader(
            unlabeled_weak_dataset,
            batch_size=self.config['data']['unlabeled_batch_size'],
            shuffle=True,
            num_workers=self.config['data']['num_workers'],
            pin_memory=self.config['data']['pin_memory'],
            drop_last=True
        )

        # 3. 无标注数据（强增强 - 用于学生模型）
        unlabeled_strong_dataset = SSLUnlabeledDataset(
            images_dir=self.config['data']['unlabeled_images'],
            image_size=self.config['data']['image_size'][0],
            transform=None,  # 将在内部创建
            max_samples=10000
        )

        unlabeled_strong_loader = DataLoader(
            unlabeled_strong_dataset,
            batch_size=self.config['data']['unlabeled_batch_size'],
            shuffle=True,
            num_workers=self.config['data']['num_workers'],
            pin_memory=self.config['data']['pin_memory'],
            drop_last=True
        )

        return labeled_loader, unlabeled_weak_loader, unlabeled_strong_loader
    
    def get_val_loader(self) -> DataLoader:
        """获取验证数据加载器"""
        val_dataset = SSLLabeledDataset(
            images_dir=self.config['data']['labeled_val_images'],
            masks_dir=self.config['data']['labeled_val_masks'],
            image_size=self.config['data']['image_size'][0],
            normalize_mean=self.normalize_config['mean'],
            normalize_std=self.normalize_config['std'],
            use_augmentation=False  # 验证时不使用增强
        )

        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config['data']['labeled_batch_size'],
            shuffle=False,
            num_workers=self.config['data']['num_workers'],
            pin_memory=self.config['data']['pin_memory']
        )

        return val_loader

def test_ssl_datasets():
    """测试SSL数据集"""
    import yaml
    
    # 加载配置
    config_path = Path(__file__).parent.parent / "configs" / "ssl_config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建数据加载器
    ssl_data_loader = SSLDataLoader(config, fold=0)
    
    # 测试数据加载
    labeled_loader, unlabeled_weak_loader, unlabeled_strong_loader = ssl_data_loader.get_train_loaders()
    val_loader = ssl_data_loader.get_val_loader()
    
    print(f"有标注训练数据: {len(labeled_loader)} 批次")
    print(f"无标注弱增强数据: {len(unlabeled_weak_loader)} 批次")
    print(f"无标注强增强数据: {len(unlabeled_strong_loader)} 批次")
    print(f"验证数据: {len(val_loader)} 批次")
    
    # 测试一个批次
    labeled_batch = next(iter(labeled_loader))
    unlabeled_weak_batch = next(iter(unlabeled_weak_loader))
    unlabeled_strong_batch = next(iter(unlabeled_strong_loader))
    
    print(f"有标注批次形状: 图像{labeled_batch[0].shape}, 掩码{labeled_batch[1].shape}")
    print(f"无标注弱增强批次形状: {unlabeled_weak_batch['image'].shape}")
    print(f"无标注强增强批次形状: {unlabeled_strong_batch['image'].shape}")

if __name__ == "__main__":
    test_ssl_datasets()
