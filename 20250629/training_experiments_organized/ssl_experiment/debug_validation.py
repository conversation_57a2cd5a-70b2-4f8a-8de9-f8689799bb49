#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证数据诊断脚本
检查SSL实验中验证部分的潜在问题
"""

import sys
import torch
import numpy as np
import yaml
from pathlib import Path
from torch.utils.data import DataLoader
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from data.ssl_datasets import SSLDataLoader
from models.ssl_model import create_ssl_model

def check_validation_data():
    """检查验证数据的完整性和格式"""
    print("="*60)
    print("验证数据诊断开始")
    print("="*60)
    
    # 加载配置
    config_path = Path(__file__).parent / "configs" / "ssl_config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建数据加载器
    ssl_data_loader = SSLDataLoader(config, fold=0)
    val_loader = ssl_data_loader.get_val_loader()
    
    print(f"验证数据集大小: {len(val_loader)} 批次")
    print(f"批次大小: {val_loader.batch_size}")
    
    # 检查几个批次的数据
    for i, batch in enumerate(val_loader):
        if i >= 3:  # 只检查前3个批次
            break
            
        print(f"\n--- 批次 {i+1} ---")
        print(f"批次类型: {type(batch)}")
        print(f"批次长度: {len(batch)}")
        
        # 检查数据格式
        if isinstance(batch, (list, tuple)):
            images = batch[0]
            masks = batch[1]
            
            print(f"图像形状: {images.shape}")
            print(f"图像数据类型: {images.dtype}")
            print(f"图像值范围: [{images.min():.3f}, {images.max():.3f}]")
            
            print(f"掩码形状: {masks.shape}")
            print(f"掩码数据类型: {masks.dtype}")
            print(f"掩码值范围: [{masks.min()}, {masks.max()}]")
            
            # 检查类别分布
            unique_labels = torch.unique(masks)
            print(f"掩码中的唯一标签: {unique_labels.tolist()}")
            
            # 检查是否有超出范围的标签
            max_label = masks.max().item()
            min_label = masks.min().item()
            expected_max = config['data']['num_classes'] - 1
            
            if max_label > expected_max:
                print(f"⚠️  警告: 发现超出范围的标签值 {max_label} (期望最大值: {expected_max})")
            if min_label < 0:
                print(f"⚠️  警告: 发现负标签值 {min_label}")
                
            # 统计每个类别的像素数量
            print("类别像素统计:")
            for class_id in range(config['data']['num_classes']):
                count = (masks == class_id).sum().item()
                total = masks.numel()
                percentage = count / total * 100
                print(f"  类别 {class_id}: {count} 像素 ({percentage:.2f}%)")
        else:
            print(f"⚠️  意外的批次格式: {type(batch)}")

def check_model_output():
    """检查模型输出格式"""
    print("\n" + "="*60)
    print("模型输出格式检查")
    print("="*60)
    
    # 加载配置
    config_path = Path(__file__).parent / "configs" / "ssl_config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建模型
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
    model = create_ssl_model(config)
    model = model.to(device)
    model.eval()
    
    # 创建测试输入
    batch_size = 2
    test_input = torch.randn(batch_size, 3, 512, 512).to(device)
    
    print(f"测试输入形状: {test_input.shape}")
    
    with torch.no_grad():
        outputs = model(test_input)
        
    print(f"模型输出类型: {type(outputs)}")
    
    if isinstance(outputs, dict):
        print("输出字典键:")
        for key, value in outputs.items():
            print(f"  {key}: {value.shape if hasattr(value, 'shape') else type(value)}")
            
        if 'main' in outputs:
            main_output = outputs['main']
            print(f"\n主输出形状: {main_output.shape}")
            print(f"主输出数据类型: {main_output.dtype}")
            
            # 检查输出维度是否正确
            expected_shape = (batch_size, config['data']['num_classes'], 512, 512)
            if main_output.shape == expected_shape:
                print("✅ 输出形状正确")
            else:
                print(f"⚠️  输出形状不匹配，期望: {expected_shape}")
                
            # 检查输出值范围
            print(f"输出值范围: [{main_output.min():.3f}, {main_output.max():.3f}]")
            
            # 应用softmax后检查
            softmax_output = torch.softmax(main_output, dim=1)
            print(f"Softmax后值范围: [{softmax_output.min():.3f}, {softmax_output.max():.3f}]")
            
            # 检查每个通道的和是否接近1
            channel_sums = softmax_output.sum(dim=1)
            print(f"每像素概率和范围: [{channel_sums.min():.3f}, {channel_sums.max():.3f}]")
    else:
        print(f"⚠️  意外的输出格式: {type(outputs)}")

def check_metrics_calculation():
    """检查指标计算"""
    print("\n" + "="*60)
    print("指标计算检查")
    print("="*60)
    
    from evaluation.metrics import SegmentationMetrics
    
    # 创建测试数据
    num_classes = 8
    batch_size = 2
    height, width = 512, 512
    
    # 模拟预测和真实标签
    predictions = torch.randn(batch_size, num_classes, height, width)
    targets = torch.randint(0, num_classes, (batch_size, height, width))
    
    print(f"测试预测形状: {predictions.shape}")
    print(f"测试标签形状: {targets.shape}")
    print(f"标签值范围: [{targets.min()}, {targets.max()}]")
    
    # 创建指标计算器
    metrics_calculator = SegmentationMetrics(num_classes)
    
    try:
        # 计算指标
        metrics = metrics_calculator.compute_metrics(predictions, targets)
        print("✅ 指标计算成功")
        print("计算得到的指标:")
        for key, value in metrics.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
        
        # 生成详细报告
        detailed_report = metrics_calculator.get_detailed_report(predictions, targets)
        print("\n详细报告生成成功")
        
    except Exception as e:
        print(f"❌ 指标计算失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    logging.basicConfig(level=logging.INFO)
    
    try:
        check_validation_data()
        check_model_output()
        check_metrics_calculation()
        
        print("\n" + "="*60)
        print("诊断完成")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
