#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K-Fold交叉验证训练脚本
对所有fold进行训练并汇总结果
"""

import os
import sys
import argparse
import logging
import json
import time
from pathlib import Path
from typing import Dict, List
import torch
import yaml
import numpy as np
import pandas as pd

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from training.ssl_trainer import SS<PERSON>rainer

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='K-Fold交叉验证训练脚本')
    
    parser.add_argument(
        '--config',
        type=str,
        default='configs/ssl_config.yaml',
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--start_fold',
        type=int,
        default=0,
        help='开始的fold编号'
    )
    
    parser.add_argument(
        '--end_fold',
        type=int,
        default=4,
        help='结束的fold编号'
    )
    
    parser.add_argument(
        '--device',
        type=str,
        default=None,
        help='训练设备 (cuda:0, cuda:1, cpu)'
    )
    
    parser.add_argument(
        '--parallel',
        action='store_true',
        help='是否并行训练多个fold（需要多GPU）'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='调试模式，使用较少的数据'
    )
    
    return parser.parse_args()

def setup_logging(output_dir: Path):
    """设置日志"""
    log_file = output_dir / 'kfold_training.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )

def train_single_fold(config_path: str, fold: int, device: str = None) -> Dict:
    """训练单个fold"""
    logging.info(f"开始训练 Fold {fold}")
    start_time = time.time()
    
    try:
        # 创建训练器
        trainer = SSLTrainer(config_path, fold=fold)
        
        # 覆盖设备设置
        if device:
            trainer.device = torch.device(device)
            trainer.student_model = trainer.student_model.to(trainer.device)
            if trainer.teacher_model:
                trainer.teacher_model = trainer.teacher_model.to(trainer.device)
        
        # 开始训练
        trainer.train()
        
        # 获取最佳结果
        best_metrics = {
            'fold': fold,
            'best_miou': trainer.best_miou,
            'training_time': time.time() - start_time,
            'total_epochs': trainer.current_epoch + 1,
            'status': 'completed'
        }
        
        # 获取最终验证指标
        if trainer.training_stats:
            final_stats = trainer.training_stats[-1]
            if 'val' in final_stats and final_stats['val']:
                best_metrics.update(final_stats['val'])
        
        logging.info(f"Fold {fold} 训练完成，最佳mIoU: {trainer.best_miou:.4f}")
        
        return best_metrics
        
    except Exception as e:
        logging.error(f"Fold {fold} 训练失败: {e}")
        return {
            'fold': fold,
            'best_miou': 0.0,
            'training_time': time.time() - start_time,
            'total_epochs': 0,
            'status': 'failed',
            'error': str(e)
        }

def aggregate_results(fold_results: List[Dict], output_dir: Path):
    """汇总K-Fold结果"""
    logging.info("汇总K-Fold训练结果...")
    
    # 过滤成功的fold
    successful_folds = [r for r in fold_results if r['status'] == 'completed']
    failed_folds = [r for r in fold_results if r['status'] == 'failed']
    
    if not successful_folds:
        logging.error("没有成功完成的fold！")
        return
    
    # 计算统计信息
    mious = [r['best_miou'] for r in successful_folds]
    training_times = [r['training_time'] for r in successful_folds]
    
    summary = {
        'total_folds': len(fold_results),
        'successful_folds': len(successful_folds),
        'failed_folds': len(failed_folds),
        'miou_mean': np.mean(mious),
        'miou_std': np.std(mious),
        'miou_min': np.min(mious),
        'miou_max': np.max(mious),
        'total_training_time': sum(training_times),
        'avg_training_time': np.mean(training_times),
        'fold_results': fold_results
    }
    
    # 计算其他指标的统计（如果有的话）
    metric_names = ['pixel_accuracy', 'mean_dice', 'mean_f1']
    for metric in metric_names:
        values = [r.get(metric, np.nan) for r in successful_folds]
        valid_values = [v for v in values if not np.isnan(v)]
        
        if valid_values:
            summary[f'{metric}_mean'] = np.mean(valid_values)
            summary[f'{metric}_std'] = np.std(valid_values)
            summary[f'{metric}_min'] = np.min(valid_values)
            summary[f'{metric}_max'] = np.max(valid_values)
    
    # 保存详细结果
    results_file = output_dir / 'kfold_results.json'
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False, default=str)
    
    # 创建结果表格
    df_data = []
    for result in fold_results:
        row = {
            'Fold': result['fold'],
            'Status': result['status'],
            'Best_mIoU': result['best_miou'],
            'Training_Time(h)': result['training_time'] / 3600,
            'Epochs': result['total_epochs']
        }
        
        # 添加其他指标
        for metric in metric_names:
            if metric in result:
                row[metric] = result[metric]
        
        df_data.append(row)
    
    # 添加汇总行
    if successful_folds:
        summary_row = {
            'Fold': 'Mean±Std',
            'Status': f"{len(successful_folds)}/{len(fold_results)}",
            'Best_mIoU': f"{summary['miou_mean']:.4f}±{summary['miou_std']:.4f}",
            'Training_Time(h)': f"{summary['avg_training_time']/3600:.2f}",
            'Epochs': '-'
        }
        
        for metric in metric_names:
            if f'{metric}_mean' in summary:
                summary_row[metric] = f"{summary[f'{metric}_mean']:.4f}±{summary[f'{metric}_std']:.4f}"
        
        df_data.append(summary_row)
    
    # 保存表格
    df = pd.DataFrame(df_data)
    csv_file = output_dir / 'kfold_results.csv'
    df.to_csv(csv_file, index=False)
    
    # 打印结果
    logging.info("=" * 80)
    logging.info("K-FOLD交叉验证结果汇总")
    logging.info("=" * 80)
    logging.info(f"总fold数: {summary['total_folds']}")
    logging.info(f"成功fold数: {summary['successful_folds']}")
    logging.info(f"失败fold数: {summary['failed_folds']}")
    logging.info(f"平均mIoU: {summary['miou_mean']:.4f} ± {summary['miou_std']:.4f}")
    logging.info(f"最佳mIoU: {summary['miou_max']:.4f}")
    logging.info(f"最差mIoU: {summary['miou_min']:.4f}")
    logging.info(f"总训练时间: {summary['total_training_time']/3600:.2f} 小时")
    logging.info(f"平均训练时间: {summary['avg_training_time']/3600:.2f} 小时/fold")
    
    # 打印每个指标的统计
    for metric in metric_names:
        if f'{metric}_mean' in summary:
            logging.info(f"平均{metric}: {summary[f'{metric}_mean']:.4f} ± {summary[f'{metric}_std']:.4f}")
    
    logging.info("=" * 80)
    logging.info(f"详细结果已保存到: {results_file}")
    logging.info(f"结果表格已保存到: {csv_file}")
    
    # 如果有失败的fold，打印失败信息
    if failed_folds:
        logging.warning("失败的fold:")
        for fold_result in failed_folds:
            logging.warning(f"  Fold {fold_result['fold']}: {fold_result.get('error', 'Unknown error')}")

def main():
    """主函数"""
    args = parse_args()
    
    # 加载配置
    config_path = Path(args.config)
    if not config_path.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建输出目录
    output_dir = Path(config['output']['base_dir'])
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    setup_logging(output_dir)
    
    # 覆盖设备设置
    if args.device:
        config['device'] = args.device
    
    # 调试模式设置
    if args.debug:
        config['training']['epochs'] = 5
        config['training']['val_interval'] = 2
        config['training']['save_interval'] = 3
    
    # 保存修改后的配置
    modified_config_path = output_dir / 'kfold_config.yaml'
    with open(modified_config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    logging.info("开始K-Fold交叉验证训练")
    logging.info(f"配置文件: {config_path}")
    logging.info(f"输出目录: {output_dir}")
    logging.info(f"训练fold: {args.start_fold} 到 {args.end_fold}")
    logging.info(f"设备: {config['device']}")
    
    # 训练所有fold
    fold_results = []
    total_start_time = time.time()
    
    for fold in range(args.start_fold, args.end_fold + 1):
        logging.info(f"\n{'='*20} 开始训练 Fold {fold} {'='*20}")
        
        # 训练单个fold
        fold_result = train_single_fold(str(modified_config_path), fold, args.device)
        fold_results.append(fold_result)
        
        # 保存中间结果
        intermediate_file = output_dir / f'intermediate_results_fold_{fold}.json'
        with open(intermediate_file, 'w', encoding='utf-8') as f:
            json.dump(fold_results, f, indent=2, ensure_ascii=False, default=str)
    
    # 汇总结果
    total_time = time.time() - total_start_time
    logging.info(f"\n所有fold训练完成，总耗时: {total_time/3600:.2f} 小时")
    
    aggregate_results(fold_results, output_dir)

if __name__ == '__main__':
    main()
