#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SSL系统与主实验的兼容性
"""

import sys
import torch
import yaml
import logging
from pathlib import Path

# 添加主实验路径
main_experiment_path = Path(__file__).parent.parent
sys.path.append(str(main_experiment_path))

def test_main_experiment_imports():
    """测试主实验模块导入"""
    print("🔍 测试主实验模块导入...")
    
    try:
        from core.models.vit_backbone import ViTBackbone
        print("✅ ViT Backbone导入成功")
    except Exception as e:
        print(f"❌ ViT Backbone导入失败: {e}")
        return False
    
    try:
        from core.models.upernet_head import ViTUPerNetHead
        print("✅ ViT UperNet Head导入成功")
    except Exception as e:
        print(f"❌ ViT UperNet Head导入失败: {e}")
        return False
    
    try:
        from core.datasets.honeycomb_dataset import HoneycombDataset
        print("✅ HoneycombDataset导入成功")
    except Exception as e:
        print(f"❌ HoneycombDataset导入失败: {e}")
        return False
    
    return True

def test_ssl_model_creation():
    """测试SSL模型创建"""
    print("\n🏗️ 测试SSL模型创建...")
    
    try:
        # 加载配置
        config_path = Path(__file__).parent / "configs" / "ssl_config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        from models.ssl_model import create_ssl_model
        
        # 创建模型
        model = create_ssl_model(config)
        print("✅ SSL模型创建成功")
        
        # 测试前向传播
        device = torch.device('cpu')
        model = model.to(device)
        
        batch_size = 2
        channels = 3
        height = width = config['data']['image_size'][0]
        
        test_input = torch.randn(batch_size, channels, height, width).to(device)
        
        with torch.no_grad():
            outputs = model(test_input)
        
        print(f"✅ 模型前向传播成功")
        print(f"   输入形状: {test_input.shape}")
        print(f"   输出形状: {outputs['main'].shape}")
        
        # 检查输出维度
        expected_classes = config['classes']['num_classes']
        actual_classes = outputs['main'].shape[1]
        
        if actual_classes == expected_classes:
            print(f"✅ 输出类别数正确: {actual_classes}")
        else:
            print(f"❌ 输出类别数错误: 期望{expected_classes}, 实际{actual_classes}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ SSL模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_paths():
    """测试数据路径"""
    print("\n📁 测试数据路径...")
    
    # 加载配置
    config_path = Path(__file__).parent / "configs" / "ssl_config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 检查有标注数据路径
    labeled_train_images = Path(config['data']['labeled_train_images'])
    labeled_train_masks = Path(config['data']['labeled_train_masks'])
    labeled_val_images = Path(config['data']['labeled_val_images'])
    labeled_val_masks = Path(config['data']['labeled_val_masks'])
    
    if labeled_train_images.exists():
        train_count = len(list(labeled_train_images.glob("*.png")))
        print(f"✅ 训练图像目录存在: {train_count} 张图片")
    else:
        print(f"❌ 训练图像目录不存在: {labeled_train_images}")
        return False
    
    if labeled_train_masks.exists():
        mask_count = len(list(labeled_train_masks.glob("*.png")))
        print(f"✅ 训练掩码目录存在: {mask_count} 张掩码")
    else:
        print(f"❌ 训练掩码目录不存在: {labeled_train_masks}")
        return False
    
    if labeled_val_images.exists():
        val_count = len(list(labeled_val_images.glob("*.png")))
        print(f"✅ 验证图像目录存在: {val_count} 张图片")
    else:
        print(f"❌ 验证图像目录不存在: {labeled_val_images}")
        return False
    
    # 检查无标注数据路径
    unlabeled_images = Path(config['data']['unlabeled_images'])
    if unlabeled_images.exists():
        unlabeled_count = len(list(unlabeled_images.glob("*.png")))
        print(f"✅ 无标注图像目录存在: {unlabeled_count} 张图片")
    else:
        print(f"❌ 无标注图像目录不存在: {unlabeled_images}")
        return False
    
    return True

def test_honeycomb_dataset():
    """测试主实验的HoneycombDataset"""
    print("\n📊 测试HoneycombDataset...")
    
    try:
        # 加载配置
        config_path = Path(__file__).parent / "configs" / "ssl_config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        from core.datasets.honeycomb_dataset import HoneycombDataset
        
        # 创建数据集
        dataset = HoneycombDataset(
            images_dir=config['data']['labeled_train_images'],
            masks_dir=config['data']['labeled_train_masks'],
            image_size=config['data']['image_size'][0],
            normalize_mean=config['data']['normalize']['mean'],
            normalize_std=config['data']['normalize']['std'],
            use_augmentation=False
        )
        
        print(f"✅ HoneycombDataset创建成功: {len(dataset)} 个样本")
        
        # 测试数据加载
        if len(dataset) > 0:
            sample = dataset[0]
            image, mask, metadata = sample
            
            print(f"✅ 数据加载成功")
            print(f"   图像形状: {image.shape}")
            print(f"   掩码形状: {mask.shape}")
            print(f"   掩码类别: {torch.unique(mask)}")
            
            # 检查类别范围
            max_class = torch.max(mask).item()
            expected_max = config['classes']['num_classes'] - 1
            
            if max_class <= expected_max:
                print(f"✅ 掩码类别范围正确: 0-{max_class}")
            else:
                print(f"❌ 掩码类别范围错误: 最大类别{max_class}, 期望最大{expected_max}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ HoneycombDataset测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ssl_data_loader():
    """测试SSL数据加载器"""
    print("\n🔄 测试SSL数据加载器...")
    
    try:
        # 加载配置
        config_path = Path(__file__).parent / "configs" / "ssl_config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        from data.ssl_datasets import SSLDataLoader
        
        # 创建数据加载器
        ssl_data_loader = SSLDataLoader(config, fold=0)
        print("✅ SSL数据加载器创建成功")
        
        # 获取验证数据加载器（最简单的测试）
        val_loader = ssl_data_loader.get_val_loader()
        print(f"✅ 验证数据加载器创建成功: {len(val_loader)} 批次")
        
        # 测试一个批次
        if len(val_loader) > 0:
            val_batch = next(iter(val_loader))
            image, mask, metadata = val_batch
            
            print(f"✅ 验证数据加载成功")
            print(f"   批次图像形状: {image.shape}")
            print(f"   批次掩码形状: {mask.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ SSL数据加载器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始SSL系统与主实验兼容性测试")
    print("=" * 60)
    
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)
    
    all_tests_passed = True
    
    # 1. 测试主实验模块导入
    if not test_main_experiment_imports():
        all_tests_passed = False
    
    # 2. 测试数据路径
    if not test_data_paths():
        all_tests_passed = False
    
    # 3. 测试HoneycombDataset
    if not test_honeycomb_dataset():
        all_tests_passed = False
    
    # 4. 测试SSL模型创建
    if not test_ssl_model_creation():
        all_tests_passed = False
    
    # 5. 测试SSL数据加载器
    if not test_ssl_data_loader():
        all_tests_passed = False
    
    # 总结
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 所有兼容性测试通过！SSL系统与主实验兼容。")
        print("\n💡 可以开始SSL训练:")
        print("   python train_ssl.py --fold 0")
    else:
        print("❌ 部分兼容性测试失败，请检查上述错误信息。")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
