# SSL半监督学习实验 (Teacher-Student)

## 概述
基于Teacher-Student架构的半监督学习实验，利用大量无标注数据提升蜂巢语义分割性能。

## 实验架构
- **Student Model**: MAE预训练的ViT-Base + UperNet
- **Teacher Model**: EMA更新的Student模型副本
- **SSL策略**: 伪标签 + 一致性正则化 + 置信度过滤
- **数据**: 5,502个有标注样本 + 10,000个无标注样本

## SSL核心技术
1. **伪标签生成**: Teacher模型对无标注数据生成伪标签
2. **置信度过滤**: 只使用高置信度(>0.95)的伪标签
3. **一致性正则化**: 弱增强和强增强的预测一致性
4. **EMA Teacher**: 指数移动平均更新Teacher权重
5. **渐进式权重**: 无监督损失权重逐渐增加

## 目录结构
```
ssl_experiment/
├── train_ssl.py                   # 主训练脚本
├── train_kfold.py                 # K-fold交叉验证
├── configs/                       # 配置文件
│   └── ssl_config.yaml
├── core/                          # SSL核心模块
│   ├── ema.py                     # EMA Teacher实现
│   └── losses.py                  # SSL损失函数
├── models/                        # SSL模型
│   └── ssl_model.py
├── data/                          # SSL数据加载器
│   └── ssl_datasets.py
├── training/                      # SSL训练器
│   └── ssl_trainer.py
├── evaluation/                    # 评估模块
├── utils/                         # 工具函数
├── visualization/                 # 可视化
└── outputs/                       # 输出结果
    └── fold_0/                    # Fold 0的训练结果
```

## 快速开始

### 1. 单Fold训练
```bash
# 使用RTX 3090训练
python train_ssl.py --fold 0 --device cuda:0

# 使用RTX 2080训练（调试模式）
python train_ssl.py --fold 0 --device cuda:1 --debug
```

### 2. K-fold交叉验证
```bash
# 运行5-fold交叉验证
python train_kfold.py --device cuda:0 --folds 5
```

### 3. 自定义配置
```bash
# 使用自定义配置文件
python train_ssl.py --config configs/custom_ssl_config.yaml
```

## SSL配置参数

### 核心SSL参数
- **confidence_threshold**: 0.95 (伪标签置信度阈值)
- **teacher_ema_alpha**: 0.999 (EMA更新系数)
- **supervised_weight**: 1.0 (监督损失权重)
- **unsupervised_weight**: 1.0 (无监督损失权重)
- **consistency_weight**: 0.1 (一致性损失权重)

### 数据增强
- **弱增强**: 随机翻转 + 颜色抖动
- **强增强**: 弱增强 + RandAugment + 随机擦除

### 训练参数
- **学习率**: 1e-4
- **优化器**: AdamW
- **调度器**: CosineAnnealingLR
- **批次大小**: 有标注=4, 无标注=8 (RTX 3090)

## 数据配置
```yaml
data:
  labeled_train_images: "path/to/labeled/train/images"
  labeled_train_masks: "path/to/labeled/train/masks"
  labeled_val_images: "path/to/labeled/val/images"
  labeled_val_masks: "path/to/labeled/val/masks"
  unlabeled_images: "path/to/unlabeled/images"
  
  labeled_batch_size: 4
  unlabeled_batch_size: 8
  num_workers: 4
```

## SSL损失函数
总损失 = 监督损失 + 无监督损失

### 监督损失
- **组合损失**: Dice + CrossEntropy + Focal Loss
- **类别权重**: 针对稀有类别的权重平衡

### 无监督损失
- **伪标签损失**: Student对强增强数据的预测 vs Teacher伪标签
- **一致性损失**: 弱增强和强增强预测的一致性
- **置信度掩码**: 只计算高置信度区域的损失

## 训练监控
- **TensorBoard**: 实时监控训练指标
- **伪标签质量**: 置信度分布、使用率统计
- **Teacher-Student差异**: EMA更新效果
- **类别平衡**: 各类别的学习进度

## 评估指标
1. **标准分割指标**: mIoU, Dice, F1
2. **SSL特定指标**:
   - 伪标签使用率
   - Teacher-Student一致性
   - 无标注数据利用效果
3. **类别分析**: 稀有类别的改善程度

## 实验结果
预期SSL相比监督学习的改进：
- **Overall mIoU**: +2-5%
- **稀有类别**: +5-10%
- **数据效率**: 用更少标注达到相同性能

## 调试模式
```bash
# 快速调试（10轮，小批次）
python train_ssl.py --fold 0 --device cuda:1 --debug
```
调试模式配置：
- 训练轮数: 10
- 批次大小: 1 (适应小显存)
- 验证间隔: 2轮
- 保存间隔: 5轮

## 注意事项
1. **显存需求**: SSL需要同时加载Student和Teacher模型
2. **训练时间**: 比监督学习慢2-3倍
3. **超参数敏感**: 置信度阈值和EMA系数需要调优
4. **数据质量**: 无标注数据质量影响SSL效果
