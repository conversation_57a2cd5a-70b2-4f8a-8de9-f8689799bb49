#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
半监督学习训练器
实现学生-教师模型的训练流程
"""

import os
import time
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import yaml
import json

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm
import numpy as np

# 导入自定义模块
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent.parent
sys.path.insert(0, str(current_dir))  # 优先使用我们的模块

# 直接导入我们的模块
import importlib.util

# 导入SSL模型
spec = importlib.util.spec_from_file_location("ssl_model", current_dir / "models" / "ssl_model.py")
ssl_model_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(ssl_model_module)
create_ssl_model = ssl_model_module.create_ssl_model

# 导入SSL损失函数
spec = importlib.util.spec_from_file_location("ssl_losses", current_dir / "core" / "losses.py")
ssl_losses_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(ssl_losses_module)
SSLLoss = ssl_losses_module.SSLLoss

# 导入其他模块
spec = importlib.util.spec_from_file_location("ssl_ema", current_dir / "core" / "ema.py")
ssl_ema_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(ssl_ema_module)
EMATeacher = ssl_ema_module.EMATeacher
ConsistencyRegularization = ssl_ema_module.ConsistencyRegularization
ConfidenceBasedMasking = ssl_ema_module.ConfidenceBasedMasking
PseudoLabelQualityMonitor = ssl_ema_module.PseudoLabelQualityMonitor

# 导入数据加载器
spec = importlib.util.spec_from_file_location("ssl_datasets", current_dir / "data" / "ssl_datasets.py")
ssl_datasets_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(ssl_datasets_module)
SSLDataLoader = ssl_datasets_module.SSLDataLoader

# 导入评估指标
spec = importlib.util.spec_from_file_location("ssl_metrics", current_dir / "evaluation" / "metrics.py")
ssl_metrics_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(ssl_metrics_module)
SegmentationMetrics = ssl_metrics_module.SegmentationMetrics

# 导入可视化
spec = importlib.util.spec_from_file_location("ssl_visualization", current_dir / "utils" / "visualization.py")
ssl_visualization_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(ssl_visualization_module)
save_training_visualizations = ssl_visualization_module.save_training_visualizations

# 导入主实验的损失函数
main_experiment_path = Path(__file__).parent.parent.parent
sys.path.append(str(main_experiment_path))
from core_main.losses.combined_loss import CombinedLoss

class SSLTrainer:
    """半监督学习训练器"""
    
    def __init__(self, config_path_or_dict, fold: int = 0):
        """
        初始化训练器

        Args:
            config_path_or_dict: 配置文件路径或配置字典
            fold: K-fold交叉验证的fold编号
        """
        # 加载配置
        if isinstance(config_path_or_dict, str):
            with open(config_path_or_dict, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        else:
            self.config = config_path_or_dict
        
        self.fold = fold
        self.device = torch.device(self.config['device'])
        
        # 设置随机种子
        self._set_seed(self.config['seed'])
        
        # 创建输出目录
        self.output_dir = self._create_output_dirs()
        
        # 设置日志
        self._setup_logging()
        
        # 初始化模型
        self.student_model = self._create_model()
        self.teacher_model = None  # 将在训练开始时初始化
        
        # 初始化数据加载器
        self.data_loader = SSLDataLoader(self.config, fold)
        
        # 初始化损失函数
        # 使用主实验的CombinedLoss作为监督损失
        self.supervised_criterion = CombinedLoss(
            dice_weight=0.5,
            ce_weight=0.3,
            focal_weight=0.2,
            focal_alpha=None,
            focal_gamma=2.0,
            class_weights=torch.tensor(self.config['classes']['class_weights']).to(self.device),
            ignore_index=-100
        )

        # SSL特定损失函数
        self.ssl_criterion = SSLLoss(self.config)
        
        # 初始化优化器和调度器
        self.optimizer = self._create_optimizer()
        self.scheduler = self._create_scheduler()
        
        # 初始化EMA和其他组件
        self.ema_teacher = None
        self.consistency_reg = ConsistencyRegularization(
            consistency_weight=self.config['ssl']['unsupervised_loss_weight'],
            ramp_up_epochs=self.config['training']['warmup_epochs']
        )
        self.confidence_masking = ConfidenceBasedMasking(
            confidence_threshold=self.config['ssl']['confidence_threshold']
        )
        self.quality_monitor = PseudoLabelQualityMonitor()
        
        # 初始化评估指标
        self.metrics = SegmentationMetrics(self.config['data']['num_classes'])
        
        # 初始化TensorBoard
        self.writer = SummaryWriter(self.output_dir / 'tensorboard')
        
        # 训练状态
        self.current_epoch = 0
        self.best_miou = 0.0
        self.training_stats = []
        
        logging.info(f"SSL训练器初始化完成 - Fold {fold}")
        logging.info(f"设备: {self.device}")
        logging.info(f"输出目录: {self.output_dir}")
    
    def _set_seed(self, seed: int):
        """设置随机种子"""
        torch.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        np.random.seed(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    
    def _create_output_dirs(self) -> Path:
        """创建输出目录"""
        base_dir = Path(self.config['output']['base_dir'])
        fold_dir = base_dir / f"fold_{self.fold}"
        
        # 创建子目录
        subdirs = ['checkpoints', 'logs', 'visualizations', 'predictions', 'tensorboard']
        for subdir in subdirs:
            (fold_dir / subdir).mkdir(parents=True, exist_ok=True)
        
        return fold_dir
    
    def _setup_logging(self):
        """设置日志"""
        log_file = self.output_dir / 'logs' / 'training.log'
        
        # 配置日志格式
        logging.basicConfig(
            level=getattr(logging, self.config['logging']['level']),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    
    def _create_model(self) -> nn.Module:
        """创建学生模型"""
        model = create_ssl_model(self.config)
        model = model.to(self.device)
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        logging.info(f"模型参数总数: {total_params:,}")
        logging.info(f"可训练参数: {trainable_params:,}")
        
        return model
    
    def _create_optimizer(self) -> optim.Optimizer:
        """创建优化器"""
        # 确保学习率是数字类型
        lr = float(self.config['training']['learning_rate'])
        weight_decay = float(self.config['training']['weight_decay'])

        if self.config['training']['optimizer'].lower() == 'adamw':
            optimizer = optim.AdamW(
                self.student_model.parameters(),
                lr=lr,
                weight_decay=weight_decay
            )
        elif self.config['training']['optimizer'].lower() == 'sgd':
            optimizer = optim.SGD(
                self.student_model.parameters(),
                lr=lr,
                momentum=0.9,
                weight_decay=weight_decay
            )
        else:
            raise ValueError(f"不支持的优化器: {self.config['training']['optimizer']}")
        
        return optimizer
    
    def _create_scheduler(self):
        """创建学习率调度器"""
        if self.config['training']['scheduler'] == 'cosine':
            scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.config['training']['epochs'],
                eta_min=self.config['training']['min_lr']
            )
        elif self.config['training']['scheduler'] == 'step':
            scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=50,
                gamma=0.1
            )
        else:
            scheduler = None
        
        return scheduler
    
    def train(self):
        """开始训练"""
        logging.info("开始半监督学习训练...")
        
        # 初始化EMA教师模型
        self.ema_teacher = EMATeacher(
            self.student_model,
            alpha=self.config['ssl']['teacher_ema_alpha']
        )
        self.teacher_model = self.ema_teacher.get_teacher_model()
        
        # 获取数据加载器
        labeled_loader, unlabeled_weak_loader, unlabeled_strong_loader = self.data_loader.get_train_loaders()
        val_loader = self.data_loader.get_val_loader()
        
        # 训练循环
        start_time = time.time()

        for epoch in range(self.config['training']['epochs']):
            self.current_epoch = epoch

            # 训练一个epoch
            train_stats = self._train_epoch(
                labeled_loader,
                unlabeled_weak_loader,
                unlabeled_strong_loader
            )
            
            # 验证
            if epoch % self.config['training']['val_interval'] == 0:
                val_stats = self._validate_epoch(val_loader)
                
                # 保存最佳模型
                if val_stats['miou'] > self.best_miou:
                    self.best_miou = val_stats['miou']
                    self._save_checkpoint('best_model.pth', is_best=True)
                    logging.info(f"新的最佳mIoU: {self.best_miou:.4f}")
            else:
                val_stats = {}
            
            # 更新学习率
            if self.scheduler:
                self.scheduler.step()
            
            # 记录统计信息
            epoch_stats = {
                'epoch': epoch,
                'train': train_stats,
                'val': val_stats,
                'lr': self.optimizer.param_groups[0]['lr'],
                'time': time.time() - start_time
            }
            self.training_stats.append(epoch_stats)
            
            # 记录到TensorBoard
            self._log_to_tensorboard(epoch_stats)
            
            # 定期保存检查点
            if epoch % self.config['training']['save_interval'] == 0:
                self._save_checkpoint(f'checkpoint_epoch_{epoch:04d}.pth')
            
            # 打印进度
            self._print_progress(epoch_stats)
        
        # 训练完成
        total_time = time.time() - start_time
        logging.info(f"训练完成！总耗时: {total_time/3600:.2f} 小时")
        logging.info(f"最佳mIoU: {self.best_miou:.4f}")
        
        # 保存最终统计信息
        self._save_training_stats()
        
        # 关闭TensorBoard
        self.writer.close()
    
    def _train_epoch(
        self,
        labeled_loader: DataLoader,
        unlabeled_weak_loader: DataLoader,
        unlabeled_strong_loader: DataLoader
    ) -> Dict[str, float]:
        """训练一个epoch"""
        self.student_model.train()
        self.teacher_model.eval()
        
        epoch_losses = {}
        num_batches = 0
        
        # 创建数据迭代器
        labeled_iter = iter(labeled_loader)
        unlabeled_weak_iter = iter(unlabeled_weak_loader)
        unlabeled_strong_iter = iter(unlabeled_strong_loader)

        # 计算每个epoch的批次数（以较小的数据集为准）
        max_batches = min(len(labeled_loader), len(unlabeled_weak_loader))

        # 创建进度条
        pbar = tqdm(range(max_batches),
                   desc=f"Epoch {self.current_epoch+1}/{self.config['training']['epochs']}",
                   unit="batch")

        for batch_idx in pbar:
            # 获取有标注数据
            try:
                labeled_batch = next(labeled_iter)
            except StopIteration:
                labeled_iter = iter(labeled_loader)
                labeled_batch = next(labeled_iter)
            
            # 获取无标注数据（弱增强）
            try:
                unlabeled_weak_batch = next(unlabeled_weak_iter)
            except StopIteration:
                unlabeled_weak_iter = iter(unlabeled_weak_loader)
                unlabeled_weak_batch = next(unlabeled_weak_iter)
            
            # 获取无标注数据（强增强）
            try:
                unlabeled_strong_batch = next(unlabeled_strong_iter)
            except StopIteration:
                unlabeled_strong_iter = iter(unlabeled_strong_loader)
                unlabeled_strong_batch = next(unlabeled_strong_iter)
            
            # 训练一个批次
            batch_losses = self._train_batch(
                labeled_batch,
                unlabeled_weak_batch,
                unlabeled_strong_batch
            )
            
            # 累积损失
            for key, value in batch_losses.items():
                if key not in epoch_losses:
                    epoch_losses[key] = 0.0
                epoch_losses[key] += value

            num_batches += 1

            # 更新进度条显示
            if num_batches > 0:
                avg_total_loss = epoch_losses.get('total', 0.0) / num_batches
                current_lr = self.optimizer.param_groups[0]['lr']
                pbar.set_postfix({
                    'loss': f'{avg_total_loss:.3f}',
                    'lr': f'{current_lr:.6f}'
                })
        
        # 计算平均损失
        for key in epoch_losses:
            epoch_losses[key] /= num_batches
        
        return epoch_losses

    def _train_batch(
        self,
        labeled_batch,
        unlabeled_batch: Dict[str, torch.Tensor],
        unlabeled_strong_batch: Dict[str, torch.Tensor]
    ) -> Dict[str, float]:
        """训练一个批次"""
        # 移动数据到设备
        # 有标注数据是元组格式 (image, mask, metadata)
        labeled_images = labeled_batch[0].to(self.device)
        labeled_masks = labeled_batch[1].to(self.device)

        # 无标注数据是字典格式
        unlabeled_images = unlabeled_batch['image'].to(self.device)
        unlabeled_strong_images = unlabeled_strong_batch['image'].to(self.device)

        # 1. 学生模型前向传播（有标注数据）
        labeled_outputs = self.student_model(labeled_images)
        labeled_pred = labeled_outputs['main']

        # 2. 教师模型前向传播（无标注数据，弱增强）
        with torch.no_grad():
            teacher_outputs = self.teacher_model(unlabeled_images)
            teacher_pred = teacher_outputs['main']

        # 3. 学生模型前向传播（无标注数据，强增强）
        unlabeled_outputs = self.student_model(unlabeled_strong_images)
        unlabeled_pred = unlabeled_outputs['main']

        # 4. 计算损失
        losses = self.ssl_criterion(
            pred_labeled=labeled_pred,
            targets_labeled=labeled_masks,
            pred_weak_unlabeled=teacher_pred,
            pred_strong_unlabeled=unlabeled_pred,
            epoch=self.current_epoch,
            max_epochs=self.config['training']['epochs']
        )

        # 5. 反向传播和优化
        self.optimizer.zero_grad()
        losses['total'].backward()

        # 梯度裁剪
        if self.config['training']['gradient_clip_val'] > 0:
            torch.nn.utils.clip_grad_norm_(
                self.student_model.parameters(),
                self.config['training']['gradient_clip_val']
            )

        self.optimizer.step()

        # 6. 更新EMA教师模型
        self.ema_teacher.update()

        # 7. 更新质量监控
        if 'mask_ratio' in losses:
            self.quality_monitor.update(
                mask_ratio=losses['mask_ratio'].item(),
                confidence_score=losses.get('confidence_score', 0.0)
            )

        # 转换损失为标量
        batch_losses = {}
        for key, value in losses.items():
            if isinstance(value, torch.Tensor):
                if value.numel() == 1:  # 标量张量
                    batch_losses[key] = value.item()
                else:
                    batch_losses[key] = value.mean().item()  # 多元素张量取平均
            elif isinstance(value, (int, float)):
                batch_losses[key] = float(value)
            else:
                batch_losses[key] = 0.0  # 默认值

        # 调试输出 - 检查损失值
        if hasattr(self, '_debug_batch_count'):
            self._debug_batch_count += 1
        else:
            self._debug_batch_count = 1

        if self._debug_batch_count % 50 == 0:  # 每50个批次打印一次
            print(f"Debug - Batch {self._debug_batch_count}: total_loss={batch_losses.get('total', 0):.6f}, "
                  f"supervised={batch_losses.get('supervised_total', 0):.6f}, "
                  f"unsupervised={batch_losses.get('unsupervised_total', 0):.6f}")

        return batch_losses

    def _validate_epoch(self, val_loader: DataLoader) -> Dict[str, float]:
        """验证一个epoch"""
        self.student_model.eval()

        total_loss = 0.0
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for batch in val_loader:
                # 验证数据也是元组格式 (image, mask, metadata)
                images = batch[0].to(self.device)
                masks = batch[1].to(self.device)

                # 前向传播
                outputs = self.student_model(images)
                predictions = outputs['main']

                # 计算监督损失
                supervised_loss = self.supervised_criterion(predictions, masks)

                # 处理损失格式 - CombinedLoss返回字典
                if isinstance(supervised_loss, dict):
                    # 使用总损失
                    loss_value = supervised_loss.get('total', supervised_loss.get('loss', 0.0))
                    if hasattr(loss_value, 'item'):
                        total_loss += loss_value.item()
                    else:
                        total_loss += float(loss_value)
                else:
                    # 单个张量损失
                    total_loss += supervised_loss.item()

                # 收集预测和真实标签
                pred_classes = torch.argmax(predictions, dim=1)
                all_predictions.append(pred_classes.cpu())
                all_targets.append(masks.cpu())

        # 计算指标
        all_predictions = torch.cat(all_predictions, dim=0)
        all_targets = torch.cat(all_targets, dim=0)

        metrics = self.metrics.compute_metrics(all_predictions, all_targets)
        metrics['loss'] = total_loss / len(val_loader)

        # 生成并打印详细的类别指标报告
        detailed_report = self.metrics.get_detailed_report(all_predictions, all_targets)
        print(detailed_report)
        logging.info(detailed_report)

        return metrics

    def _save_checkpoint(self, filename: str, is_best: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': self.current_epoch,
            'student_state_dict': self.student_model.state_dict(),
            'teacher_state_dict': self.teacher_model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'best_miou': self.best_miou,
            'config': self.config,
            'fold': self.fold
        }

        checkpoint_path = self.output_dir / 'checkpoints' / filename
        torch.save(checkpoint, checkpoint_path)

        if is_best:
            # 同时保存为best模型
            best_path = self.output_dir / 'checkpoints' / 'best_model.pth'
            torch.save(checkpoint, best_path)

        logging.info(f"检查点已保存: {checkpoint_path}")

    def _log_to_tensorboard(self, stats: Dict[str, Any]):
        """记录到TensorBoard"""
        epoch = stats['epoch']

        # 训练损失
        if 'train' in stats:
            for key, value in stats['train'].items():
                self.writer.add_scalar(f'Train/{key}', value, epoch)

        # 验证指标
        if 'val' in stats and stats['val']:
            for key, value in stats['val'].items():
                self.writer.add_scalar(f'Val/{key}', value, epoch)

        # 学习率
        self.writer.add_scalar('Learning_Rate', stats['lr'], epoch)

        # 质量监控统计
        quality_stats = self.quality_monitor.get_stats()
        for key, value in quality_stats.items():
            self.writer.add_scalar(f'Quality/{key}', value, epoch)

    def _print_progress(self, stats: Dict[str, Any]):
        """打印训练进度 - 标准深度学习格式"""
        epoch = stats['epoch']
        train_stats = stats.get('train', {})
        val_stats = stats.get('val', {})

        # 打印训练损失
        if train_stats:
            total_loss = train_stats.get('total', 0.0)
            supervised_loss = train_stats.get('supervised_total', 0.0)
            unsupervised_loss = train_stats.get('unsupervised_total', 0.0)

            print(f"Epoch {epoch+1} | 平均训练损失: {total_loss:.4f} "
                  f"(监督: {supervised_loss:.4f}, 无监督: {unsupervised_loss:.4f})")

        # 打印验证损失和指标
        if val_stats:
            val_loss = val_stats.get('loss', 0.0)
            val_miou = val_stats.get('miou', 0.0)

            print(f"Epoch {epoch+1} | 平均验证损失: {val_loss:.4f} | 验证mIoU: {val_miou:.4f}")

            # 如果是新的最佳模型
            if val_miou > self.best_miou:
                print(f"🎯 新的最佳验证mIoU: {val_miou:.4f}")
                logging.info(f"检查点已保存至: {self.output_dir}/checkpoints/best_model.pth")

    def _save_training_stats(self):
        """保存训练统计信息"""
        stats_file = self.output_dir / 'logs' / 'training_stats.json'

        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(self.training_stats, f, indent=2, ensure_ascii=False)

        logging.info(f"训练统计信息已保存: {stats_file}")

    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        # 加载模型状态
        self.student_model.load_state_dict(checkpoint['student_state_dict'])

        # 初始化EMA教师模型（如果还没有）
        if self.ema_teacher is None:
            self.ema_teacher = EMATeacher(
                self.student_model,
                alpha=self.config['ssl']['teacher_ema_alpha']
            )

        self.teacher_model = self.ema_teacher.get_teacher_model()
        self.teacher_model.load_state_dict(checkpoint['teacher_state_dict'])

        # 加载优化器状态
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        # 加载调度器状态
        if self.scheduler and checkpoint['scheduler_state_dict']:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        # 加载其他状态
        self.current_epoch = checkpoint['epoch']
        self.best_miou = checkpoint['best_miou']

        logging.info(f"检查点已加载: {checkpoint_path}")
        logging.info(f"恢复到epoch {self.current_epoch}, 最佳mIoU: {self.best_miou:.4f}")
