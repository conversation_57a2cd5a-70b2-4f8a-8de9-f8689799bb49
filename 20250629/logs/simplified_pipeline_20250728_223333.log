2025-07-28 22:33:33,173 - __main__ - INFO - 日志文件: /home/<USER>/PycharmProjects/20250629/logs/simplified_pipeline_20250728_223333.log
2025-07-28 22:33:33,173 - __main__ - INFO - 🚀 开始执行简化的数据处理流水线
2025-07-28 22:33:33,173 - __main__ - INFO - === 验证现有数据 ===
2025-07-28 22:33:33,229 - __main__ - INFO - ✅ 找到图像文件: 7208 个
2025-07-28 22:33:33,229 - __main__ - INFO - ✅ 找到掩码文件: 7208 个
2025-07-28 22:33:33,231 - __main__ - INFO - === 执行7:3分层数据划分 ===
2025-07-28 22:33:39,510 - __main__ - INFO - 创建输出目录: /home/<USER>/PycharmProjects/20250629/processed_data/split_dataset
2025-07-28 22:33:39,510 - __main__ - INFO - 开始执行7:3分层数据划分...
2025-07-28 22:33:39,511 - training_refactored.core.datasets.data_splitter - INFO - 数据划分器初始化完成: 训练=0.7, 验证=0.3, 测试=0.0
2025-07-28 22:33:39,511 - training_refactored.core.datasets.data_splitter - INFO - 开始数据集划分...
2025-07-28 22:33:39,708 - training_refactored.core.datasets.data_splitter - INFO - 找到 7208 个有效的图像-掩码对
2025-07-28 22:33:39,708 - training_refactored.core.datasets.data_splitter - INFO - 执行分层划分...
2025-07-28 22:34:11,886 - training_refactored.core.datasets.data_splitter - INFO - 类别 1: 3725 样本 -> 训练:2607, 验证:1117, 测试:1
2025-07-28 22:34:11,887 - training_refactored.core.datasets.data_splitter - INFO - 类别 6: 371 样本 -> 训练:259, 验证:111, 测试:1
2025-07-28 22:34:11,888 - training_refactored.core.datasets.data_splitter - INFO - 类别 0: 1867 样本 -> 训练:1306, 验证:560, 测试:1
2025-07-28 22:34:11,889 - training_refactored.core.datasets.data_splitter - INFO - 类别 2: 887 样本 -> 训练:620, 验证:266, 测试:1
2025-07-28 22:34:11,889 - training_refactored.core.datasets.data_splitter - INFO - 类别 4: 242 样本 -> 训练:169, 验证:72, 测试:1
2025-07-28 22:34:11,890 - training_refactored.core.datasets.data_splitter - INFO - 类别 5: 116 样本 -> 训练:81, 验证:34, 测试:1
2025-07-28 22:34:43,069 - training_refactored.core.datasets.data_splitter - INFO - 实际划分比例: 训练=0.700, 验证=0.300, 测试=0.001
2025-07-28 22:34:43,069 - training_refactored.core.datasets.data_splitter - INFO - 数据集划分完成
2025-07-28 22:34:43,069 - training_refactored.core.datasets.data_splitter - INFO - 数据集划分摘要:
2025-07-28 22:34:43,069 - training_refactored.core.datasets.data_splitter - INFO -   训练集: 5042 样本
2025-07-28 22:34:43,069 - training_refactored.core.datasets.data_splitter - INFO -   验证集: 2160 样本
2025-07-28 22:34:43,069 - training_refactored.core.datasets.data_splitter - INFO -   测试集: 6 样本
2025-07-28 22:34:43,069 - training_refactored.core.datasets.data_splitter - INFO - 类别分布:
2025-07-28 22:34:43,069 - training_refactored.core.datasets.data_splitter - INFO -   类别 0: 训练=293386627, 验证=127584115, 测试=230050
2025-07-28 22:34:43,069 - training_refactored.core.datasets.data_splitter - INFO -   类别 1: 训练=685285715, 验证=291449789, 测试=479594
2025-07-28 22:34:43,069 - training_refactored.core.datasets.data_splitter - INFO -   类别 2: 训练=180596349, 验证=77151381, 测试=136587
2025-07-28 22:34:43,070 - training_refactored.core.datasets.data_splitter - INFO -   类别 4: 训练=48418221, 验证=22645842, 测试=137626
2025-07-28 22:34:43,070 - training_refactored.core.datasets.data_splitter - INFO -   类别 5: 训练=50793909, 验证=20793488, 测试=278774
2025-07-28 22:34:43,070 - training_refactored.core.datasets.data_splitter - INFO -   类别 6: 训练=63249227, 验证=26606425, 测试=310233
2025-07-28 22:34:43,080 - __main__ - ERROR - ❌ 数据划分失败: keys must be str, int, float, bool or None, not int64
2025-07-28 22:34:43,080 - __main__ - ERROR - Traceback (most recent call last):
  File "/home/<USER>/PycharmProjects/20250629/scripts/simplified_data_pipeline.py", line 91, in split_dataset
    split_result = create_stratified_split(
  File "/home/<USER>/PycharmProjects/20250629/training_refactored/core/datasets/data_splitter.py", line 377, in create_stratified_split
    splitter.save_split(split_result, output_dir)
  File "/home/<USER>/PycharmProjects/20250629/training_refactored/core/datasets/data_splitter.py", line 338, in save_split
    json.dump(split_result.to_dict(), f, indent=2, default=str)
  File "/home/<USER>/miniconda3/envs/bee_experiment/lib/python3.9/json/__init__.py", line 179, in dump
    for chunk in iterable:
  File "/home/<USER>/miniconda3/envs/bee_experiment/lib/python3.9/json/encoder.py", line 431, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "/home/<USER>/miniconda3/envs/bee_experiment/lib/python3.9/json/encoder.py", line 405, in _iterencode_dict
    yield from chunks
  File "/home/<USER>/miniconda3/envs/bee_experiment/lib/python3.9/json/encoder.py", line 376, in _iterencode_dict
    raise TypeError(f'keys must be str, int, float, bool or None, '
TypeError: keys must be str, int, float, bool or None, not int64

2025-07-28 22:34:43,080 - __main__ - ERROR - ❌ 数据划分失败，退出
