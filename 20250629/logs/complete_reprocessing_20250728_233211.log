2025-07-28 23:32:11,077 - __main__ - INFO - 日志文件: /home/<USER>/PycharmProjects/20250629/logs/complete_reprocessing_20250728_233211.log
2025-07-28 23:32:11,077 - __main__ - INFO - 🚀 开始完整的8类数据重新处理
2025-07-28 23:32:11,077 - __main__ - INFO - === 步骤1：处理JSON标注生成8类mask ===
2025-07-28 23:32:16,980 - training_refactored.utils.annotation_processor - INFO - 标注处理器初始化完成
2025-07-28 23:32:16,980 - training_refactored.utils.annotation_processor - INFO - 输入目录: /home/<USER>/PycharmProjects/20250629/bee_cell_annotation/data/annotations
2025-07-28 23:32:16,980 - training_refactored.utils.annotation_processor - INFO - 输出目录: /home/<USER>/PycharmProjects/20250629/reprocessed_data/masks
2025-07-28 23:32:16,980 - __main__ - INFO - 开始处理JSON标注...
2025-07-28 23:32:16,980 - training_refactored.utils.annotation_processor - INFO - 发现 68 个JSON标注文件
2025-07-28 23:32:59,617 - training_refactored.utils.annotation_processor - INFO - ==================================================
2025-07-28 23:32:59,617 - training_refactored.utils.annotation_processor - INFO - 标注数据处理统计
2025-07-28 23:32:59,617 - training_refactored.utils.annotation_processor - INFO - ==================================================
2025-07-28 23:32:59,617 - training_refactored.utils.annotation_processor - INFO - 总文件数: 68
2025-07-28 23:32:59,617 - training_refactored.utils.annotation_processor - INFO - 成功处理: 68
2025-07-28 23:32:59,618 - training_refactored.utils.annotation_processor - INFO - 处理失败: 0
2025-07-28 23:32:59,618 - training_refactored.utils.annotation_processor - INFO - 
类别像素统计:
2025-07-28 23:32:59,618 - training_refactored.utils.annotation_processor - INFO -   background: 546,854,068 像素 (39.39%)
2025-07-28 23:32:59,618 - training_refactored.utils.annotation_processor - INFO -   capped_brood: 150,408,743 像素 (10.83%)
2025-07-28 23:32:59,618 - training_refactored.utils.annotation_processor - INFO -   eggs: 14,003,906 像素 (1.01%)
2025-07-28 23:32:59,618 - training_refactored.utils.annotation_processor - INFO -   honey: 46,556,016 像素 (3.35%)
2025-07-28 23:32:59,618 - training_refactored.utils.annotation_processor - INFO -   honeycomb: 513,212,561 像素 (36.96%)
2025-07-28 23:32:59,618 - training_refactored.utils.annotation_processor - INFO -   larvae: 11,083,708 像素 (0.80%)
2025-07-28 23:32:59,618 - training_refactored.utils.annotation_processor - INFO -   nectar: 57,895,920 像素 (4.17%)
2025-07-28 23:32:59,618 - training_refactored.utils.annotation_processor - INFO -   pollen: 48,454,966 像素 (3.49%)
2025-07-28 23:32:59,618 - training_refactored.utils.annotation_processor - INFO - 
总像素数: 1,388,469,888
2025-07-28 23:32:59,618 - training_refactored.utils.annotation_processor - INFO - ==================================================
2025-07-28 23:32:59,618 - __main__ - INFO - ✅ 标注处理完成:
2025-07-28 23:32:59,618 - __main__ - INFO -    处理文件数: 68
2025-07-28 23:32:59,618 - __main__ - INFO -    总文件数: 68
2025-07-28 23:32:59,618 - __main__ - INFO -    类别统计: {'background': 546854068, 'capped_brood': 150408743, 'eggs': 14003906, 'honeycomb': 513212561, 'larvae': 11083708, 'honey': 46556016, 'pollen': 48454966, 'nectar': 57895920}
2025-07-28 23:32:59,618 - __main__ - WARNING - ⚠️ 缺失类别: {0, 1, 2, 3, 4, 5, 6, 7}
2025-07-28 23:32:59,619 - __main__ - INFO - === 步骤2：提取512x512训练图块 ===
2025-07-28 23:32:59,620 - training_refactored.data_preparation.create_training_patches - INFO - 🚀 使用 8 个线程进行并行处理
2025-07-28 23:32:59,620 - training_refactored.data_preparation.create_training_patches - INFO - 图块提取器初始化完成
2025-07-28 23:32:59,620 - training_refactored.data_preparation.create_training_patches - INFO - 图像目录: /home/<USER>/PycharmProjects/20250629/bee_cell_annotation/static/uploads
2025-07-28 23:32:59,620 - training_refactored.data_preparation.create_training_patches - INFO - Mask目录: /home/<USER>/PycharmProjects/20250629/reprocessed_data/masks
2025-07-28 23:32:59,620 - training_refactored.data_preparation.create_training_patches - INFO - 输出目录: /home/<USER>/PycharmProjects/20250629/reprocessed_data/patches
2025-07-28 23:32:59,620 - training_refactored.data_preparation.create_training_patches - INFO - 图块大小: 512x512, 步长: 384
2025-07-28 23:32:59,620 - training_refactored.data_preparation.create_training_patches - INFO - 最小蜂巢比例: 0.05
2025-07-28 23:32:59,620 - __main__ - INFO - 开始提取512x512训练图块...
2025-07-28 23:32:59,622 - training_refactored.data_preparation.create_training_patches - INFO - 找到 68 个有效的图像-mask对
2025-07-28 23:32:59,622 - training_refactored.data_preparation.create_training_patches - INFO - 开始分层采样划分训练集和验证集...
2025-07-28 23:32:59,622 - training_refactored.data_preparation.create_training_patches - INFO - 分析图像类别分布...
2025-07-28 23:33:25,295 - training_refactored.data_preparation.create_training_patches - INFO - 原始图像类别分布:
2025-07-28 23:33:25,296 - training_refactored.data_preparation.create_training_patches - INFO -   background  :  68 图像
2025-07-28 23:33:25,296 - training_refactored.data_preparation.create_training_patches - INFO -   capped_brood:  43 图像
2025-07-28 23:33:25,296 - training_refactored.data_preparation.create_training_patches - INFO -   eggs        :  28 图像
2025-07-28 23:33:25,296 - training_refactored.data_preparation.create_training_patches - INFO -   honey       :  17 图像
2025-07-28 23:33:25,296 - training_refactored.data_preparation.create_training_patches - INFO -   honeycomb   :  66 图像
2025-07-28 23:33:25,296 - training_refactored.data_preparation.create_training_patches - INFO -   larvae      :  36 图像
2025-07-28 23:33:25,296 - training_refactored.data_preparation.create_training_patches - INFO -   nectar      :  38 图像
2025-07-28 23:33:25,296 - training_refactored.data_preparation.create_training_patches - INFO -   pollen      :  47 图像
2025-07-28 23:33:25,296 - training_refactored.data_preparation.create_training_patches - INFO - 为稀有类别分配验证样本...
2025-07-28 23:33:25,296 - training_refactored.data_preparation.create_training_patches - INFO -   eggs        : 9/28 -> 验证集
2025-07-28 23:33:25,296 - training_refactored.data_preparation.create_training_patches - INFO -   larvae      : 12/36 -> 验证集
2025-07-28 23:33:25,297 - training_refactored.data_preparation.create_training_patches - INFO -   nectar      : 13/38 -> 验证集
2025-07-28 23:33:25,297 - training_refactored.data_preparation.create_training_patches - INFO -   pollen      : 16/47 -> 验证集
2025-07-28 23:33:25,297 - training_refactored.data_preparation.create_training_patches - INFO - 最终分配: 训练集 32, 验证集 36
2025-07-28 23:33:25,297 - training_refactored.data_preparation.create_training_patches - INFO - 验证分割结果的类别平衡性...
2025-07-28 23:33:50,997 - training_refactored.data_preparation.create_training_patches - INFO - 分割后类别分布:
2025-07-28 23:33:50,997 - training_refactored.data_preparation.create_training_patches - INFO - 类别           训练集      验证集      验证集比例      状态
2025-07-28 23:33:50,997 - training_refactored.data_preparation.create_training_patches - INFO - -------------------------------------------------------
2025-07-28 23:33:50,997 - training_refactored.data_preparation.create_training_patches - INFO - background   32       36         52.9%     ✅
2025-07-28 23:33:50,997 - training_refactored.data_preparation.create_training_patches - INFO - capped_brood 23       20         46.5%     ✅
2025-07-28 23:33:50,997 - training_refactored.data_preparation.create_training_patches - INFO - eggs         13       15         53.6%     ✅
2025-07-28 23:33:50,998 - training_refactored.data_preparation.create_training_patches - INFO - honey        8        9          52.9%     ✅
2025-07-28 23:33:50,998 - training_refactored.data_preparation.create_training_patches - INFO - honeycomb    30       36         54.5%     ✅
2025-07-28 23:33:50,998 - training_refactored.data_preparation.create_training_patches - INFO - larvae       15       21         58.3%     ✅
2025-07-28 23:33:50,998 - training_refactored.data_preparation.create_training_patches - INFO - nectar       14       24         63.2%     ✅
2025-07-28 23:33:50,998 - training_refactored.data_preparation.create_training_patches - INFO - pollen       17       30         63.8%     ✅
2025-07-28 23:33:50,998 - training_refactored.data_preparation.create_training_patches - INFO - ✅ 所有类别在验证集中都有代表
2025-07-28 23:33:50,998 - training_refactored.data_preparation.create_training_patches - INFO - 开始并行处理训练集 (32 个图像)...
2025-07-28 23:34:04,773 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: DSC_0114.JPG -> 66/96 有效图块
2025-07-28 23:34:06,113 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: DSC_0248.JPG -> 69/96 有效图块
2025-07-28 23:34:09,892 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0226.JPG -> 112/120 有效图块
2025-07-28 23:34:10,190 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0231.JPG -> 112/120 有效图块
2025-07-28 23:34:12,586 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0261.JPG -> 120/120 有效图块
2025-07-28 23:34:13,905 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0277.JPG -> 120/150 有效图块
2025-07-28 23:34:14,486 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0218.JPG -> 117/120 有效图块
2025-07-28 23:34:17,255 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0290.JPG -> 120/150 有效图块
2025-07-28 23:34:19,841 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0234.JPG -> 87/120 有效图块
2025-07-28 23:34:24,974 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0276.JPG -> 120/120 有效图块
2025-07-28 23:34:27,446 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: DSC_0125.JPG -> 70/96 有效图块
2025-07-28 23:34:29,060 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0236.JPG -> 103/120 有效图块
2025-07-28 23:34:30,665 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0239.JPG -> 98/120 有效图块
2025-07-28 23:34:32,505 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0255.JPG -> 118/120 有效图块
2025-07-28 23:34:33,430 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0215.JPG -> 102/120 有效图块
2025-07-28 23:34:40,025 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0227.JPG -> 104/120 有效图块
2025-07-28 23:34:41,211 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0294.JPG -> 120/150 有效图块
2025-07-28 23:34:42,559 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0220.JPG -> 105/120 有效图块
2025-07-28 23:34:44,021 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: DSC_0160.JPG -> 57/96 有效图块
2025-07-28 23:34:45,339 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0232.JPG -> 107/120 有效图块
2025-07-28 23:34:48,369 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0228.JPG -> 103/120 有效图块
2025-07-28 23:34:49,460 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0274.JPG -> 116/150 有效图块
2025-07-28 23:34:49,808 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0233.JPG -> 105/150 有效图块
2025-07-28 23:34:57,639 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: DSC_0203.JPG -> 71/96 有效图块
2025-07-28 23:34:58,834 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0238.JPG -> 102/120 有效图块
2025-07-28 23:34:59,803 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0259.JPG -> 120/120 有效图块
2025-07-28 23:35:02,076 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: DSC_0205.JPG -> 69/96 有效图块
2025-07-28 23:35:02,534 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: DSC_0162.JPG -> 69/96 有效图块
2025-07-28 23:35:04,243 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0297.JPG -> 120/150 有效图块
2025-07-28 23:35:05,256 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0221.JPG -> 105/120 有效图块
2025-07-28 23:35:07,611 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0214.JPG -> 106/120 有效图块
2025-07-28 23:35:10,093 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: DSC_0431.JPG -> 72/96 有效图块
2025-07-28 23:35:10,095 - training_refactored.data_preparation.create_training_patches - INFO - ✅ 训练集处理完成
2025-07-28 23:35:10,099 - training_refactored.data_preparation.create_training_patches - INFO - 开始并行处理验证集 (36 个图像)...
2025-07-28 23:35:24,727 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: DSC_0433.JPG -> 72/96 有效图块
2025-07-28 23:35:28,611 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0229.JPG -> 118/120 有效图块
2025-07-28 23:35:29,227 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0275.JPG -> 117/120 有效图块
2025-07-28 23:35:30,294 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0258.JPG -> 120/120 有效图块
2025-07-28 23:35:31,784 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0271.JPG -> 115/150 有效图块
2025-07-28 23:35:31,999 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0266.JPG -> 106/150 有效图块
2025-07-28 23:35:33,424 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0264.JPG -> 118/150 有效图块
2025-07-28 23:35:34,656 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0278.JPG -> 133/150 有效图块
2025-07-28 23:35:45,764 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: DSC_0231.JPG -> 71/96 有效图块
2025-07-28 23:35:46,912 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0216.JPG -> 111/120 有效图块
2025-07-28 23:35:47,219 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: DSC_0200.JPG -> 66/96 有效图块
2025-07-28 23:35:47,498 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0300.JPG -> 120/150 有效图块
2025-07-28 23:35:48,090 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0272.JPG -> 120/120 有效图块
2025-07-28 23:35:48,119 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0250.JPG -> 108/120 有效图块
2025-07-28 23:35:51,283 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0212.JPG -> 103/120 有效图块
2025-07-28 23:35:53,582 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0270.JPG -> 113/150 有效图块
2025-07-28 23:36:04,250 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0217.JPG -> 106/120 有效图块
2025-07-28 23:36:05,107 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: DSC_0430.JPG -> 73/96 有效图块
2025-07-28 23:36:05,236 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0251.JPG -> 106/120 有效图块
2025-07-28 23:36:05,424 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0224.JPG -> 114/120 有效图块
2025-07-28 23:36:06,889 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0243.JPG -> 109/120 有效图块
2025-07-28 23:36:07,846 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0268.JPG -> 109/150 有效图块
2025-07-28 23:36:08,100 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0269.JPG -> 108/150 有效图块
2025-07-28 23:36:14,493 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0254.JPG -> 120/120 有效图块
2025-07-28 23:36:21,996 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0235.JPG -> 99/120 有效图块
2025-07-28 23:36:25,481 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0213.JPG -> 119/120 有效图块
2025-07-28 23:36:28,031 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0222.JPG -> 120/120 有效图块
2025-07-28 23:36:28,880 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0223.JPG -> 119/120 有效图块
2025-07-28 23:36:29,531 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0291.JPG -> 120/150 有效图块
2025-07-28 23:36:29,694 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: _DSC0496.JPG -> 73/96 有效图块
2025-07-28 23:36:29,720 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0273.JPG -> 120/150 有效图块
2025-07-28 23:36:30,821 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0296.JPG -> 122/150 有效图块
2025-07-28 23:36:37,687 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: DSC_0432.JPG -> 71/96 有效图块
2025-07-28 23:36:43,855 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0219.JPG -> 117/120 有效图块
2025-07-28 23:36:46,223 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0267.JPG -> 108/150 有效图块
2025-07-28 23:36:47,312 - training_refactored.data_preparation.create_training_patches - INFO - 处理完成: IMG_0263.JPG -> 119/150 有效图块
2025-07-28 23:36:47,314 - training_refactored.data_preparation.create_training_patches - INFO - ✅ 验证集处理完成
2025-07-28 23:36:47,317 - training_refactored.data_preparation.create_training_patches - INFO - 数据集信息已保存: /home/<USER>/PycharmProjects/20250629/reprocessed_data/patches/dataset_info.json
2025-07-28 23:36:47,318 - training_refactored.data_preparation.create_training_patches - INFO - ============================================================
2025-07-28 23:36:47,318 - training_refactored.data_preparation.create_training_patches - INFO - 训练数据集创建统计
2025-07-28 23:36:47,318 - training_refactored.data_preparation.create_training_patches - INFO - ============================================================
2025-07-28 23:36:47,318 - training_refactored.data_preparation.create_training_patches - INFO - 总图像数: 1
2025-07-28 23:36:47,318 - training_refactored.data_preparation.create_training_patches - INFO - 总图块数: 8394
2025-07-28 23:36:47,318 - training_refactored.data_preparation.create_training_patches - INFO - 有效图块数: 7048
2025-07-28 23:36:47,318 - training_refactored.data_preparation.create_training_patches - INFO - 训练图块数: 3185
2025-07-28 23:36:47,318 - training_refactored.data_preparation.create_training_patches - INFO - 验证图块数: 3863
2025-07-28 23:36:47,318 - training_refactored.data_preparation.create_training_patches - INFO - 
类别像素分布:
2025-07-28 23:36:47,318 - training_refactored.data_preparation.create_training_patches - INFO -   0: background - 366,750,877 像素 (19.85%)
2025-07-28 23:36:47,318 - training_refactored.data_preparation.create_training_patches - INFO -   1: capped_brood - 267,510,181 像素 (14.48%)
2025-07-28 23:36:47,318 - training_refactored.data_preparation.create_training_patches - INFO -   2: eggs - 24,813,105 像素 (1.34%)
2025-07-28 23:36:47,318 - training_refactored.data_preparation.create_training_patches - INFO -   3: honey - 83,367,693 像素 (4.51%)
2025-07-28 23:36:47,319 - training_refactored.data_preparation.create_training_patches - INFO -   4: honeycomb - 897,857,192 像素 (48.60%)
2025-07-28 23:36:47,319 - training_refactored.data_preparation.create_training_patches - INFO -   5: larvae - 19,613,991 像素 (1.06%)
2025-07-28 23:36:47,319 - training_refactored.data_preparation.create_training_patches - INFO -   6: nectar - 101,916,231 像素 (5.52%)
2025-07-28 23:36:47,319 - training_refactored.data_preparation.create_training_patches - INFO -   7: pollen - 85,761,642 像素 (4.64%)
2025-07-28 23:36:47,319 - training_refactored.data_preparation.create_training_patches - INFO - 
图块主要类别分布:
2025-07-28 23:36:47,319 - training_refactored.data_preparation.create_training_patches - INFO -   0: background - 1699 个图块
2025-07-28 23:36:47,319 - training_refactored.data_preparation.create_training_patches - INFO -   1: capped_brood - 936 个图块
2025-07-28 23:36:47,319 - training_refactored.data_preparation.create_training_patches - INFO -   2: eggs - 73 个图块
2025-07-28 23:36:47,319 - training_refactored.data_preparation.create_training_patches - INFO -   3: honey - 295 个图块
2025-07-28 23:36:47,319 - training_refactored.data_preparation.create_training_patches - INFO -   4: honeycomb - 3526 个图块
2025-07-28 23:36:47,319 - training_refactored.data_preparation.create_training_patches - INFO -   5: larvae - 9 个图块
2025-07-28 23:36:47,320 - training_refactored.data_preparation.create_training_patches - INFO -   6: nectar - 387 个图块
2025-07-28 23:36:47,320 - training_refactored.data_preparation.create_training_patches - INFO -   7: pollen - 123 个图块
2025-07-28 23:36:47,320 - training_refactored.data_preparation.create_training_patches - INFO - 
输出目录: /home/<USER>/PycharmProjects/20250629/reprocessed_data/patches
2025-07-28 23:36:47,320 - training_refactored.data_preparation.create_training_patches - INFO - ============================================================
2025-07-28 23:36:47,321 - __main__ - ERROR - ❌ 输出目录不存在
2025-07-28 23:36:47,321 - __main__ - ERROR - ❌ 步骤2失败，退出
