2025-07-28 22:30:13,338 - __main__ - INFO - 日志文件: /home/<USER>/PycharmProjects/20250629/logs/data_pipeline_20250728_223013.log
2025-07-28 22:30:13,339 - __main__ - INFO - 🚀 开始执行完整的数据处理流水线
2025-07-28 22:30:13,339 - __main__ - INFO - === 验证原始数据源 ===
2025-07-28 22:30:13,339 - __main__ - INFO - ✅ 标注数据目录存在: /home/<USER>/PycharmProjects/20250629/bee_cell_annotation/data/annotations
2025-07-28 22:30:13,339 - __main__ - INFO -    包含 136 个文件/目录
2025-07-28 22:30:13,339 - __main__ - INFO - ✅ 标注图片目录存在: /home/<USER>/PycharmProjects/20250629/bee_cell_annotation/static/uploads
2025-07-28 22:30:13,340 - __main__ - INFO -    包含 68 个文件/目录
2025-07-28 22:30:13,340 - __main__ - INFO - ✅ 所有图片目录存在: /home/<USER>/PycharmProjects/20250629/temp_input_for_prediction
2025-07-28 22:30:13,341 - __main__ - INFO -    包含 508 个文件/目录
2025-07-28 22:30:13,342 - __main__ - INFO - 找到 68 个JSON标注文件
2025-07-28 22:30:13,344 - __main__ - INFO - uploads目录中找到 68 张图片
2025-07-28 22:30:13,350 - __main__ - INFO - temp_input_for_prediction目录中找到 508 张图片
2025-07-28 22:30:13,350 - __main__ - INFO - === 步骤1：JSON标注转换为语义分割mask ===
2025-07-28 22:30:19,800 - __main__ - ERROR - ❌ 标注转换失败: [Errno 2] No such file or directory: '/home/<USER>/PycharmProjects/20250629/processed_data/masks'
2025-07-28 22:30:19,801 - __main__ - ERROR - Traceback (most recent call last):
  File "/home/<USER>/PycharmProjects/20250629/scripts/complete_data_pipeline.py", line 100, in step1_convert_annotations
    processor = BeeAnnotationProcessor(
  File "/home/<USER>/PycharmProjects/20250629/training_refactored/utils/annotation_processor.py", line 66, in __init__
    self.output_dir.mkdir(exist_ok=True)
  File "/home/<USER>/miniconda3/envs/bee_experiment/lib/python3.9/pathlib.py", line 1251, in mkdir
    self._accessor.mkdir(self, mode)
FileNotFoundError: [Errno 2] No such file or directory: '/home/<USER>/PycharmProjects/20250629/processed_data/masks'

2025-07-28 22:30:19,801 - __main__ - ERROR - ❌ 步骤1失败，退出
