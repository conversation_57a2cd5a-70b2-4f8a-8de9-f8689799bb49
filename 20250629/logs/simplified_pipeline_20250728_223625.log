2025-07-28 22:36:25,564 - __main__ - INFO - 日志文件: /home/<USER>/PycharmProjects/20250629/logs/simplified_pipeline_20250728_223625.log
2025-07-28 22:36:25,564 - __main__ - INFO - 🚀 开始执行简化的数据处理流水线
2025-07-28 22:36:25,564 - __main__ - INFO - === 验证现有数据 ===
2025-07-28 22:36:25,616 - __main__ - INFO - ✅ 找到图像文件: 7208 个
2025-07-28 22:36:25,616 - __main__ - INFO - ✅ 找到掩码文件: 7208 个
2025-07-28 22:36:25,618 - __main__ - INFO - === 执行7:3分层数据划分 ===
2025-07-28 22:36:31,542 - __main__ - INFO - 创建输出目录: /home/<USER>/PycharmProjects/20250629/processed_data/split_dataset
2025-07-28 22:36:31,542 - __main__ - INFO - 开始执行7:3分层数据划分...
2025-07-28 22:36:31,542 - training_refactored.core.datasets.data_splitter - INFO - 数据划分器初始化完成: 训练=0.7, 验证=0.3, 测试=0.0
2025-07-28 22:36:31,542 - training_refactored.core.datasets.data_splitter - INFO - 开始数据集划分...
2025-07-28 22:36:31,722 - training_refactored.core.datasets.data_splitter - INFO - 找到 7208 个有效的图像-掩码对
2025-07-28 22:36:31,722 - training_refactored.core.datasets.data_splitter - INFO - 执行分层划分...
2025-07-28 22:37:00,566 - training_refactored.core.datasets.data_splitter - INFO - 类别 1: 3725 样本 -> 训练:2607, 验证:1117, 测试:1
2025-07-28 22:37:00,567 - training_refactored.core.datasets.data_splitter - INFO - 类别 6: 371 样本 -> 训练:259, 验证:111, 测试:1
2025-07-28 22:37:00,568 - training_refactored.core.datasets.data_splitter - INFO - 类别 0: 1867 样本 -> 训练:1306, 验证:560, 测试:1
2025-07-28 22:37:00,568 - training_refactored.core.datasets.data_splitter - INFO - 类别 2: 887 样本 -> 训练:620, 验证:266, 测试:1
2025-07-28 22:37:00,569 - training_refactored.core.datasets.data_splitter - INFO - 类别 4: 242 样本 -> 训练:169, 验证:72, 测试:1
2025-07-28 22:37:00,569 - training_refactored.core.datasets.data_splitter - INFO - 类别 5: 116 样本 -> 训练:81, 验证:34, 测试:1
2025-07-28 22:37:28,797 - training_refactored.core.datasets.data_splitter - INFO - 实际划分比例: 训练=0.700, 验证=0.300, 测试=0.001
2025-07-28 22:37:28,797 - training_refactored.core.datasets.data_splitter - INFO - 数据集划分完成
2025-07-28 22:37:28,797 - training_refactored.core.datasets.data_splitter - INFO - 数据集划分摘要:
2025-07-28 22:37:28,798 - training_refactored.core.datasets.data_splitter - INFO -   训练集: 5042 样本
2025-07-28 22:37:28,798 - training_refactored.core.datasets.data_splitter - INFO -   验证集: 2160 样本
2025-07-28 22:37:28,798 - training_refactored.core.datasets.data_splitter - INFO -   测试集: 6 样本
2025-07-28 22:37:28,798 - training_refactored.core.datasets.data_splitter - INFO - 类别分布:
2025-07-28 22:37:28,798 - training_refactored.core.datasets.data_splitter - INFO -   类别 0: 训练=293386627, 验证=127584115, 测试=230050
2025-07-28 22:37:28,798 - training_refactored.core.datasets.data_splitter - INFO -   类别 1: 训练=685285715, 验证=291449789, 测试=479594
2025-07-28 22:37:28,798 - training_refactored.core.datasets.data_splitter - INFO -   类别 2: 训练=180596349, 验证=77151381, 测试=136587
2025-07-28 22:37:28,798 - training_refactored.core.datasets.data_splitter - INFO -   类别 4: 训练=48418221, 验证=22645842, 测试=137626
2025-07-28 22:37:28,798 - training_refactored.core.datasets.data_splitter - INFO -   类别 5: 训练=50793909, 验证=20793488, 测试=278774
2025-07-28 22:37:28,798 - training_refactored.core.datasets.data_splitter - INFO -   类别 6: 训练=63249227, 验证=26606425, 测试=310233
2025-07-28 22:37:28,808 - training_refactored.core.datasets.data_splitter - INFO - 数据划分结果已保存到: /home/<USER>/PycharmProjects/20250629/processed_data/split_dataset
2025-07-28 22:37:28,808 - __main__ - INFO - ✅ 数据划分完成:
2025-07-28 22:37:28,808 - __main__ - INFO -    训练集: 5042 个样本
2025-07-28 22:37:28,808 - __main__ - INFO -    验证集: 2160 个样本
2025-07-28 22:37:28,808 - __main__ - INFO -    测试集: 6 个样本
2025-07-28 22:37:28,808 - __main__ - INFO - 训练集类别分布:
2025-07-28 22:37:28,808 - __main__ - INFO -    类别 1: 685285715 个样本
2025-07-28 22:37:28,808 - __main__ - INFO -    类别 5: 50793909 个样本
2025-07-28 22:37:28,808 - __main__ - INFO -    类别 6: 63249227 个样本
2025-07-28 22:37:28,808 - __main__ - INFO -    类别 2: 180596349 个样本
2025-07-28 22:37:28,808 - __main__ - INFO -    类别 0: 293386627 个样本
2025-07-28 22:37:28,808 - __main__ - INFO -    类别 4: 48418221 个样本
2025-07-28 22:37:28,808 - __main__ - INFO - 验证集类别分布:
2025-07-28 22:37:28,808 - __main__ - INFO -    类别 1: 291449789 个样本
2025-07-28 22:37:28,809 - __main__ - INFO -    类别 5: 20793488 个样本
2025-07-28 22:37:28,809 - __main__ - INFO -    类别 6: 26606425 个样本
2025-07-28 22:37:28,809 - __main__ - INFO -    类别 0: 127584115 个样本
2025-07-28 22:37:28,809 - __main__ - INFO -    类别 2: 77151381 个样本
2025-07-28 22:37:28,809 - __main__ - INFO -    类别 4: 22645842 个样本
2025-07-28 22:37:28,809 - __main__ - INFO - === 验证划分后的数据质量 ===
2025-07-28 22:37:28,809 - __main__ - INFO - 文件数量验证:
2025-07-28 22:37:28,809 - __main__ - INFO -    训练集图像: 0, 掩码: 0
2025-07-28 22:37:28,809 - __main__ - INFO -    验证集图像: 0, 掩码: 0
2025-07-28 22:37:28,809 - __main__ - INFO - ✅ 数据一致性验证通过
2025-07-28 22:37:28,809 - __main__ - ERROR - ❌ 训练集和验证集样本数量相同，划分可能有问题
2025-07-28 22:37:28,809 - __main__ - ERROR - ❌ 数据验证失败，退出
