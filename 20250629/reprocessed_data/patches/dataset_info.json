{"dataset_name": "honeycomb_8class_segmentation", "description": "蜂巢8类语义分割数据集（新honeycomb逻辑）", "classes": {"0": "background", "1": "capped_brood", "2": "eggs", "3": "honey", "4": "honeycomb", "5": "larvae", "6": "nectar", "7": "pollen"}, "num_classes": 8, "patch_size": 512, "stride": 384, "min_honeycomb_ratio": 0.05, "statistics": {"total_images": 1, "total_patches": 8394, "valid_patches": 7048, "train_patches": 3185, "val_patches": 3863, "class_pixel_counts": {"0": 366750877, "4": 897857192, "5": 19613991, "3": 83367693, "1": 267510181, "7": 85761642, "6": 101916231, "2": 24813105}, "patch_class_distribution": {"0": 1699, "3": 295, "4": 3526, "1": 936, "6": 387, "2": 73, "7": 123, "5": 9}}, "honeycomb_logic": {"background": "完全未被标注的区域", "honeycomb": "所有被标注覆盖的区域（基础蜂巢结构）", "content_classes": "在honeycomb基础上的具体内容物细分"}, "created_by": "HoneycombPatchExtractor"}