#!/usr/bin/env python3
"""
分析掩码文件的类别分布
"""
import os
import numpy as np
import cv2
from collections import Counter
import matplotlib.pyplot as plt

# 定义掩码目录
TRAIN_MASKS_DIR = '/home/<USER>/PycharmProjects/20250629/processed_data/split_dataset/train/masks'
VAL_MASKS_DIR = '/home/<USER>/PycharmProjects/20250629/processed_data/split_dataset/val/masks'

# 8类蜂巢语义分割类别映射
CLASS_MAP = {
    0: 'background',      # 背景
    1: 'capped_brood',    # 封盖子
    2: 'eggs',            # 卵
    3: 'honey',           # 蜂蜜
    4: 'honeycomb',       # 蜂巢结构
    5: 'larvae',          # 幼虫
    6: 'nectar',          # 花蜜
    7: 'pollen'           # 花粉
}

def analyze_masks_in_dir(masks_dir, split_name):
    """分析指定目录中掩码文件的类别分布"""
    print(f"开始分析{split_name}集掩码文件的类别分布...")

    # 获取所有掩码文件
    mask_files = [f for f in os.listdir(masks_dir) if f.endswith('.png')]
    print(f"找到 {len(mask_files)} 个掩码文件")

    # 用于存储所有掩码的像素值计数
    total_counts = Counter()
    sample_dominant_classes = []

    # 遍历每个掩码文件
    for i, mask_file in enumerate(mask_files):
        if i % 500 == 0:
            print(f"  处理进度: {i}/{len(mask_files)}")

        mask_path = os.path.join(masks_dir, mask_file)
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)

        if mask is None:
            print(f"无法读取掩码文件: {mask_path}")
            continue

        # 统计当前掩码的像素值
        unique, counts = np.unique(mask, return_counts=True)
        current_counts = dict(zip(unique, counts))

        # 找到主要类别（排除背景）
        non_bg_counts = {k: v for k, v in current_counts.items() if k != 0}
        if non_bg_counts:
            dominant_class = max(non_bg_counts.items(), key=lambda x: x[1])[0]
        else:
            dominant_class = 0
        sample_dominant_classes.append(dominant_class)

        # 更新总计数
        for pixel_value, count in current_counts.items():
            total_counts[pixel_value] += count
    
    # 计算总像素数
    total_pixels = sum(total_counts.values())
    
    # 打印结果
    print("\n类别分布:")
    print("-" * 40)
    print(f"{'类别ID':<8} {'类别名称':<10} {'像素数量':<15} {'百分比':<10}")
    print("-" * 40)
    
    for pixel_value, count in sorted(total_counts.items()):
        class_name = CLASS_MAP.get(pixel_value, f"未知类别 {pixel_value}")
        percentage = (count / total_pixels) * 100
        print(f"{pixel_value:<8} {class_name:<10} {count:<15,} {percentage:.2f}%")
    
    print("-" * 40)
    print(f"总像素数: {total_pixels:,}")
    
    # 绘制饼图
    labels = [f"{CLASS_MAP.get(pixel_value, f'未知类别 {pixel_value}')} ({percentage:.2f}%)" 
              for pixel_value, count in sorted(total_counts.items())
              for percentage in [(count / total_pixels) * 100]]
    
    sizes = [count for _, count in sorted(total_counts.items())]
    
    plt.figure(figsize=(10, 6))
    plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
    plt.axis('equal')
    plt.title('掩码类别分布')
    plt.tight_layout()
    plt.savefig('mask_distribution.png')
    print("\n饼图已保存为 'mask_distribution.png'")

if __name__ == '__main__':
    # 分析训练集
    print("分析训练集:")
    analyze_masks_in_dir(TRAIN_MASKS_DIR, "训练")

    print("\n" + "="*50)

    # 分析验证集
    print("分析验证集:")
    analyze_masks_in_dir(VAL_MASKS_DIR, "验证")
