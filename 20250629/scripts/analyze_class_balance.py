#!/usr/bin/env python3
"""
分析数据集类别平衡
检查训练集和验证集中8个蜂巢类别的分布

Author: AI Assistant
Date: 2024
"""

import os
import sys
import numpy as np
import cv2
import json
import logging
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def analyze_mask_classes(mask_path):
    """分析单个掩码文件的类别分布"""
    try:
        mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
        if mask is None:
            return None
        
        # 统计每个类别的像素数量
        unique_classes, counts = np.unique(mask, return_counts=True)
        class_distribution = dict(zip(unique_classes.astype(int), counts.astype(int)))
        
        # 计算主要类别（像素数量最多的类别，排除背景）
        non_bg_classes = {k: v for k, v in class_distribution.items() if k != 0}
        if non_bg_classes:
            dominant_class = max(non_bg_classes.items(), key=lambda x: x[1])[0]
        else:
            dominant_class = 0  # 如果只有背景，则为背景
        
        return {
            'class_distribution': class_distribution,
            'dominant_class': dominant_class,
            'total_pixels': mask.size
        }
    except Exception as e:
        print(f"Error analyzing {mask_path}: {e}")
        return None

def analyze_dataset_balance(data_dir, split_name, logger):
    """分析数据集的类别平衡"""
    logger.info(f"=== 分析{split_name}集类别平衡 ===")
    
    masks_dir = Path(data_dir) / split_name / "masks"
    if not masks_dir.exists():
        logger.error(f"掩码目录不存在: {masks_dir}")
        return None
    
    mask_files = list(masks_dir.glob("*.png"))
    logger.info(f"找到 {len(mask_files)} 个掩码文件")
    
    # 统计每个样本的主要类别
    sample_dominant_classes = []
    total_class_pixels = defaultdict(int)
    total_samples = 0
    
    for i, mask_file in enumerate(mask_files):
        if i % 500 == 0:
            logger.info(f"  处理进度: {i}/{len(mask_files)}")
        
        result = analyze_mask_classes(mask_file)
        if result is None:
            continue
        
        sample_dominant_classes.append(result['dominant_class'])
        
        # 累计每个类别的总像素数
        for class_id, pixel_count in result['class_distribution'].items():
            total_class_pixels[class_id] += pixel_count
        
        total_samples += 1
    
    # 统计样本级别的类别分布（按主要类别）
    sample_class_counts = Counter(sample_dominant_classes)
    
    # 计算像素级别的类别分布
    total_pixels = sum(total_class_pixels.values())
    pixel_class_ratios = {
        class_id: count / total_pixels 
        for class_id, count in total_class_pixels.items()
    }
    
    logger.info(f"{split_name}集分析结果:")
    logger.info(f"  总样本数: {total_samples}")
    logger.info(f"  总像素数: {total_pixels}")
    
    logger.info(f"样本级别类别分布（按主要类别）:")
    for class_id in sorted(sample_class_counts.keys()):
        count = sample_class_counts[class_id]
        ratio = count / total_samples
        logger.info(f"  类别 {class_id}: {count} 个样本 ({ratio:.3f})")
    
    logger.info(f"像素级别类别分布:")
    for class_id in sorted(pixel_class_ratios.keys()):
        ratio = pixel_class_ratios[class_id]
        count = total_class_pixels[class_id]
        logger.info(f"  类别 {class_id}: {count} 个像素 ({ratio:.3f})")
    
    return {
        'split_name': split_name,
        'total_samples': total_samples,
        'total_pixels': total_pixels,
        'sample_class_counts': dict(sample_class_counts),
        'pixel_class_counts': dict(total_class_pixels),
        'sample_class_ratios': {k: v/total_samples for k, v in sample_class_counts.items()},
        'pixel_class_ratios': pixel_class_ratios
    }

def compare_class_balance(train_stats, val_stats, logger):
    """比较训练集和验证集的类别平衡"""
    logger.info("=== 类别平衡比较 ===")
    
    # 获取所有类别
    all_classes = set(train_stats['sample_class_counts'].keys()) | set(val_stats['sample_class_counts'].keys())
    
    logger.info("样本级别类别分布比较:")
    logger.info(f"{'类别':<6} {'训练集样本':<10} {'训练集比例':<10} {'验证集样本':<10} {'验证集比例':<10} {'比例差异':<10}")
    logger.info("-" * 70)
    
    max_ratio_diff = 0
    for class_id in sorted(all_classes):
        train_count = train_stats['sample_class_counts'].get(class_id, 0)
        val_count = val_stats['sample_class_counts'].get(class_id, 0)
        train_ratio = train_stats['sample_class_ratios'].get(class_id, 0)
        val_ratio = val_stats['sample_class_ratios'].get(class_id, 0)
        ratio_diff = abs(train_ratio - val_ratio)
        max_ratio_diff = max(max_ratio_diff, ratio_diff)
        
        logger.info(f"{class_id:<6} {train_count:<10} {train_ratio:<10.3f} {val_count:<10} {val_ratio:<10.3f} {ratio_diff:<10.3f}")
    
    # 评估平衡性
    if max_ratio_diff < 0.05:
        logger.info("✅ 类别分布良好平衡（最大比例差异 < 5%）")
        balance_status = "良好"
    elif max_ratio_diff < 0.1:
        logger.info("⚠️ 类别分布轻微不平衡（最大比例差异 < 10%）")
        balance_status = "轻微不平衡"
    else:
        logger.info("❌ 类别分布严重不平衡（最大比例差异 >= 10%）")
        balance_status = "严重不平衡"
    
    return {
        'max_ratio_diff': max_ratio_diff,
        'balance_status': balance_status,
        'comparison_details': {
            class_id: {
                'train_count': train_stats['sample_class_counts'].get(class_id, 0),
                'val_count': val_stats['sample_class_counts'].get(class_id, 0),
                'train_ratio': train_stats['sample_class_ratios'].get(class_id, 0),
                'val_ratio': val_stats['sample_class_ratios'].get(class_id, 0),
                'ratio_diff': abs(train_stats['sample_class_ratios'].get(class_id, 0) - 
                                val_stats['sample_class_ratios'].get(class_id, 0))
            }
            for class_id in sorted(all_classes)
        }
    }

def save_analysis_results(train_stats, val_stats, comparison, output_file):
    """保存分析结果"""
    results = {
        'analysis_timestamp': datetime.now().isoformat(),
        'train_stats': train_stats,
        'val_stats': val_stats,
        'balance_comparison': comparison,
        'class_names': {
            0: "background",
            1: "capped_brood", 
            2: "eggs",
            3: "honey",
            4: "honeycomb",
            5: "larvae",
            6: "nectar",
            7: "pollen"
        }
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("🔍 开始分析数据集类别平衡")
    
    data_dir = "/home/<USER>/PycharmProjects/20250629/processed_data/split_dataset"
    
    # 分析训练集
    train_stats = analyze_dataset_balance(data_dir, "train", logger)
    if train_stats is None:
        logger.error("❌ 训练集分析失败")
        return False
    
    # 分析验证集
    val_stats = analyze_dataset_balance(data_dir, "val", logger)
    if val_stats is None:
        logger.error("❌ 验证集分析失败")
        return False
    
    # 比较类别平衡
    comparison = compare_class_balance(train_stats, val_stats, logger)
    
    # 保存结果
    output_file = Path(data_dir) / "class_balance_analysis.json"
    save_analysis_results(train_stats, val_stats, comparison, output_file)
    logger.info(f"✅ 分析结果已保存到: {output_file}")
    
    logger.info("🎉 类别平衡分析完成！")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ 类别平衡分析完成！")
    else:
        print("❌ 类别平衡分析失败！")
        sys.exit(1)
