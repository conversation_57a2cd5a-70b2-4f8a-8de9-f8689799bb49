#!/usr/bin/env python3
"""
简单的UperNet分割训练脚本
直接使用training_refactored中的训练器

Author: AI Assistant
Date: 2024
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "training_refactored"))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("🚀 开始UperNet分割训练")
    
    try:
        # 导入训练模块
        from training_refactored.training.trainer import SegmentationTrainer
        from training_refactored.core.config.base_config import BaseConfig
        
        # 创建配置
        config = BaseConfig()
        
        # 模型配置
        config.model.num_classes = 8
        config.model.image_size = 512
        config.model.patch_size = 16
        config.model.embed_dim = 768
        config.model.depth = 12
        config.model.num_heads = 12
        config.model.mlp_ratio = 4.0
        config.model.upernet_in_channels = [768, 768, 768, 768]
        config.model.upernet_pool_scales = [1, 2, 3, 6]
        config.model.upernet_channels = 512
        config.model.use_pretrained = True
        config.model.mae_checkpoint_path = "/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/best_checkpoint_epoch_0527.pth"
        
        # 数据配置
        config.data.data_root = "/home/<USER>/PycharmProjects/20250629/processed_data/split_dataset"
        config.data.train_images_dir = "train/images"
        config.data.train_masks_dir = "train/masks"
        config.data.val_images_dir = "val/images"
        config.data.val_masks_dir = "val/masks"
        config.data.image_size = 512
        config.data.batch_size = 8
        config.data.num_workers = 8
        config.data.pin_memory = True
        config.data.normalize_mean = [0.485, 0.456, 0.406]
        config.data.normalize_std = [0.229, 0.224, 0.225]
        config.data.use_augmentation = True
        config.data.horizontal_flip_prob = 0.5
        config.data.vertical_flip_prob = 0.5
        config.data.rotation_degrees = 15
        config.data.color_jitter_brightness = 0.2
        config.data.color_jitter_contrast = 0.2
        
        # 训练配置
        config.training.epochs = 50
        config.training.learning_rate = 0.0005
        config.training.weight_decay = 0.0001
        config.training.optimizer = "adamw"
        config.training.betas = [0.9, 0.999]
        config.training.lr_scheduler = "cosine"
        config.training.warmup_epochs = 0
        config.training.loss_function = "combined"
        config.training.dice_weight = 0.3
        config.training.ce_weight = 0.3
        config.training.focal_weight = 0.4
        config.training.focal_alpha = 0.25
        config.training.focal_gamma = 2.0
        config.training.gradient_clip_norm = 1.0
        config.training.mixed_precision = True
        config.training.early_stopping_patience = 20
        
        # 系统配置
        config.system.device = "cuda:1"  # 使用第二块显卡
        config.system.num_gpus = 1
        config.system.output_dir = "/home/<USER>/PycharmProjects/20250629/experiments"
        config.system.experiment_name = "upernet_mae_segmentation"
        config.system.save_top_k = 3
        config.system.save_every_n_epochs = 5
        config.system.log_every_n_steps = 10
        config.system.val_every_n_epochs = 2
        
        logger.info("配置创建完成")
        
        # 创建训练器
        trainer = SegmentationTrainer(config)
        logger.info("训练器创建完成")
        
        # 设置所有组件
        trainer.setup_all()
        logger.info("训练器设置完成")
        
        # 开始训练
        logger.info("开始训练...")
        trainer.train()
        
        logger.info("🎉 训练完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 训练失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ UperNet分割训练成功！")
    else:
        print("❌ UperNet分割训练失败！")
        sys.exit(1)
