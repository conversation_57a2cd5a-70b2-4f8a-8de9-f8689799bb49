#!/usr/bin/env python3
"""
完整的数据处理流水线
从原始标注数据到训练就绪的数据集

流程：
1. JSON标注转换为语义分割mask
2. 创建512x512训练图块
3. 7:3分层数据划分
4. 验证数据质量

Author: AI Assistant
Date: 2024
"""

import os
import sys
import logging
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置详细日志"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"data_pipeline_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志文件: {log_file}")
    return logger

def validate_source_data(logger):
    """验证原始数据源"""
    logger.info("=== 验证原始数据源 ===")
    
    # 数据路径
    annotation_dir = Path("/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/data/annotations")
    uploads_dir = Path("/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/static/uploads")
    all_images_dir = Path("/home/<USER>/PycharmProjects/20250629/temp_input_for_prediction")
    
    # 检查目录存在性
    for name, path in [("标注数据", annotation_dir), ("标注图片", uploads_dir), ("所有图片", all_images_dir)]:
        if path.exists():
            logger.info(f"✅ {name}目录存在: {path}")
            if path.is_dir():
                file_count = len(list(path.iterdir()))
                logger.info(f"   包含 {file_count} 个文件/目录")
        else:
            logger.error(f"❌ {name}目录不存在: {path}")
            return False
    
    # 检查标注文件
    annotation_files = list(annotation_dir.glob("*.json"))
    logger.info(f"找到 {len(annotation_files)} 个JSON标注文件")
    
    # 检查图片文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    uploads_images = []
    for ext in image_extensions:
        uploads_images.extend(list(uploads_dir.glob(f"*{ext}")))
        uploads_images.extend(list(uploads_dir.glob(f"*{ext.upper()}")))
    
    logger.info(f"uploads目录中找到 {len(uploads_images)} 张图片")
    
    all_images = []
    for ext in image_extensions:
        all_images.extend(list(all_images_dir.glob(f"*{ext}")))
        all_images.extend(list(all_images_dir.glob(f"*{ext.upper()}")))
    
    logger.info(f"temp_input_for_prediction目录中找到 {len(all_images)} 张图片")
    
    return len(annotation_files) > 0 and len(uploads_images) > 0

def step1_convert_annotations(logger):
    """步骤1：JSON标注转换为语义分割mask"""
    logger.info("=== 步骤1：JSON标注转换为语义分割mask ===")

    try:
        from training_refactored.utils.annotation_processor import BeeAnnotationProcessor

        annotation_dir = "/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/data/annotations"
        output_dir = "/home/<USER>/PycharmProjects/20250629/processed_data/masks"

        # 确保输出目录存在
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        logger.info(f"创建输出目录: {output_dir}")

        processor = BeeAnnotationProcessor(
            annotation_dir=annotation_dir,
            output_dir=output_dir
        )
        
        # 执行标注转换
        logger.info("开始转换JSON标注为语义分割mask...")
        results = processor.process_all_annotations()
        
        logger.info(f"✅ 标注转换完成:")
        logger.info(f"   处理文件数: {results.get('processed_files', 0)}")
        logger.info(f"   生成mask数: {results.get('total_masks', 0)}")
        logger.info(f"   类别统计: {results.get('class_counts', {})}")
        
        return True, output_dir
        
    except Exception as e:
        logger.error(f"❌ 标注转换失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, None

def step2_create_patches(logger, mask_dir):
    """步骤2：创建512x512训练图块"""
    logger.info("=== 步骤2：创建512x512训练图块 ===")

    try:
        from training_refactored.data_preparation.create_training_patches import HoneycombPatchExtractor

        image_dir = "/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/static/uploads"
        output_dir = "/home/<USER>/PycharmProjects/20250629/processed_data/patches"

        # 确保输出目录存在
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        logger.info(f"创建输出目录: {output_dir}")

        extractor = HoneycombPatchExtractor(
            image_dir=image_dir,
            mask_dir=mask_dir,
            output_dir=output_dir,
            patch_size=512,
            stride=384,  # 25%重叠
            min_honeycomb_ratio=0.1,
            num_workers=8
        )
        
        # 执行图块提取
        logger.info("开始提取512x512训练图块...")
        extractor.process_all_images()

        logger.info(f"✅ 图块提取完成:")
        logger.info(f"   输出目录: {output_dir}")

        # 检查生成的图块数量
        train_images_dir = Path(output_dir) / "train" / "images"
        val_images_dir = Path(output_dir) / "val" / "images"

        if train_images_dir.exists():
            train_count = len(list(train_images_dir.glob("*")))
            logger.info(f"   训练图块数: {train_count}")

        if val_images_dir.exists():
            val_count = len(list(val_images_dir.glob("*")))
            logger.info(f"   验证图块数: {val_count}")

        return True, output_dir
        
    except Exception as e:
        logger.error(f"❌ 图块提取失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, None

def step3_split_dataset(logger, patches_dir):
    """步骤3：7:3分层数据划分"""
    logger.info("=== 步骤3：7:3分层数据划分 ===")

    try:
        from training_refactored.core.datasets.data_splitter import create_stratified_split

        images_dir = f"{patches_dir}/images"
        masks_dir = f"{patches_dir}/masks"
        output_dir = "/home/<USER>/PycharmProjects/20250629/processed_data/split_dataset"

        # 确保输出目录存在
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        logger.info(f"创建输出目录: {output_dir}")

        # 执行分层划分
        logger.info("开始执行7:3分层数据划分...")
        split_result = create_stratified_split(
            images_dir=images_dir,
            masks_dir=masks_dir,
            output_dir=output_dir,
            train_ratio=0.7,
            val_ratio=0.3,
            test_ratio=0.0,
            random_seed=42
        )
        
        logger.info(f"✅ 数据划分完成:")
        logger.info(f"   训练集: {len(split_result.train_files)} 个样本")
        logger.info(f"   验证集: {len(split_result.val_files)} 个样本")
        logger.info(f"   测试集: {len(split_result.test_files)} 个样本")
        
        # 显示类别分布
        logger.info("训练集类别分布:")
        for class_id, count in split_result.train_class_distribution.items():
            logger.info(f"   类别 {class_id}: {count} 个样本")
            
        logger.info("验证集类别分布:")
        for class_id, count in split_result.val_class_distribution.items():
            logger.info(f"   类别 {class_id}: {count} 个样本")
        
        return True, output_dir, split_result
        
    except Exception as e:
        logger.error(f"❌ 数据划分失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, None, None

def step4_validate_data(logger, split_dir, split_result):
    """步骤4：验证数据质量"""
    logger.info("=== 步骤4：验证数据质量 ===")
    
    try:
        train_images_dir = Path(split_dir) / "train" / "images"
        train_masks_dir = Path(split_dir) / "train" / "masks"
        val_images_dir = Path(split_dir) / "val" / "images"
        val_masks_dir = Path(split_dir) / "val" / "masks"
        
        # 验证文件数量
        train_img_count = len(list(train_images_dir.glob("*")))
        train_mask_count = len(list(train_masks_dir.glob("*")))
        val_img_count = len(list(val_images_dir.glob("*")))
        val_mask_count = len(list(val_masks_dir.glob("*")))
        
        logger.info(f"文件数量验证:")
        logger.info(f"   训练集图像: {train_img_count}, 掩码: {train_mask_count}")
        logger.info(f"   验证集图像: {val_img_count}, 掩码: {val_mask_count}")
        
        # 验证数据一致性
        if train_img_count == train_mask_count and val_img_count == val_mask_count:
            logger.info("✅ 数据一致性验证通过")
        else:
            logger.error("❌ 数据一致性验证失败：图像和掩码数量不匹配")
            return False
        
        # 验证样本数量不同
        if train_img_count != val_img_count:
            logger.info("✅ 训练集和验证集样本数量不同，划分正确")
        else:
            logger.error("❌ 训练集和验证集样本数量相同，划分可能有问题")
            return False
        
        # 保存数据统计信息
        stats = {
            "data_split_timestamp": datetime.now().isoformat(),
            "train_samples": train_img_count,
            "val_samples": val_img_count,
            "train_class_distribution": split_result.train_class_distribution,
            "val_class_distribution": split_result.val_class_distribution,
            "split_ratio": f"{train_img_count}:{val_img_count}",
            "data_paths": {
                "train_images": str(train_images_dir),
                "train_masks": str(train_masks_dir),
                "val_images": str(val_images_dir),
                "val_masks": str(val_masks_dir)
            }
        }
        
        stats_file = Path(split_dir) / "data_statistics.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ 数据统计信息已保存到: {stats_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据验证失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("🚀 开始执行完整的数据处理流水线")
    
    # 验证原始数据源
    if not validate_source_data(logger):
        logger.error("❌ 原始数据源验证失败，退出")
        return False
    
    # 步骤1：标注转换
    success, mask_dir = step1_convert_annotations(logger)
    if not success:
        logger.error("❌ 步骤1失败，退出")
        return False
    
    # 步骤2：图块提取
    success, patches_dir = step2_create_patches(logger, mask_dir)
    if not success:
        logger.error("❌ 步骤2失败，退出")
        return False
    
    # 步骤3：数据划分
    success, split_dir, split_result = step3_split_dataset(logger, patches_dir)
    if not success:
        logger.error("❌ 步骤3失败，退出")
        return False
    
    # 步骤4：数据验证
    success = step4_validate_data(logger, split_dir, split_result)
    if not success:
        logger.error("❌ 步骤4失败，退出")
        return False
    
    logger.info("🎉 完整的数据处理流水线执行成功！")
    logger.info(f"最终数据集路径: {split_dir}")
    
    return True, split_dir

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ 数据处理流水线执行成功！")
    else:
        print("❌ 数据处理流水线执行失败！")
        sys.exit(1)
