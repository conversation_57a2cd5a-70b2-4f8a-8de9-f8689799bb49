#!/usr/bin/env python3
"""
简化的数据处理流水线
直接使用现有的7208个图块进行7:3划分

流程：
1. 使用现有的labeled_patches数据
2. 执行7:3分层数据划分
3. 验证数据质量
4. 启动UperNet训练

Author: AI Assistant
Date: 2024
"""

import os
import sys
import logging
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置详细日志"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"simplified_pipeline_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志文件: {log_file}")
    return logger

def validate_existing_data(logger):
    """验证现有数据"""
    logger.info("=== 验证现有数据 ===")
    
    source_images_dir = Path("/home/<USER>/PycharmProjects/20250629/bee_segmentation_project/data/labeled_patches/images")
    source_masks_dir = Path("/home/<USER>/PycharmProjects/20250629/bee_segmentation_project/data/labeled_patches/masks")
    
    if not source_images_dir.exists():
        logger.error(f"❌ 源图像目录不存在: {source_images_dir}")
        return False, None, None
    
    if not source_masks_dir.exists():
        logger.error(f"❌ 源掩码目录不存在: {source_masks_dir}")
        return False, None, None
    
    # 统计文件数量
    image_files = list(source_images_dir.glob("*.jpg")) + list(source_images_dir.glob("*.png"))
    mask_files = list(source_masks_dir.glob("*.png"))
    
    logger.info(f"✅ 找到图像文件: {len(image_files)} 个")
    logger.info(f"✅ 找到掩码文件: {len(mask_files)} 个")
    
    if len(image_files) != len(mask_files):
        logger.error(f"❌ 图像和掩码数量不匹配: {len(image_files)} vs {len(mask_files)}")
        return False, None, None
    
    return True, str(source_images_dir), str(source_masks_dir)

def split_dataset(logger, images_dir, masks_dir):
    """执行7:3分层数据划分"""
    logger.info("=== 执行7:3分层数据划分 ===")
    
    try:
        from training_refactored.core.datasets.data_splitter import create_stratified_split
        
        output_dir = "/home/<USER>/PycharmProjects/20250629/processed_data/split_dataset"
        
        # 确保输出目录存在
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        logger.info(f"创建输出目录: {output_dir}")
        
        # 执行分层划分
        logger.info("开始执行7:3分层数据划分...")
        split_result = create_stratified_split(
            images_dir=images_dir,
            masks_dir=masks_dir,
            output_dir=output_dir,
            train_ratio=0.7,
            val_ratio=0.3,
            test_ratio=0.0,
            random_seed=42
        )
        
        logger.info(f"✅ 数据划分完成:")
        logger.info(f"   训练集: {len(split_result.train_files)} 个样本")
        logger.info(f"   验证集: {len(split_result.val_files)} 个样本")
        logger.info(f"   测试集: {len(split_result.test_files)} 个样本")
        
        # 显示类别分布
        logger.info("训练集类别分布:")
        for class_id, count in split_result.train_class_distribution.items():
            logger.info(f"   类别 {class_id}: {count} 个样本")
            
        logger.info("验证集类别分布:")
        for class_id, count in split_result.val_class_distribution.items():
            logger.info(f"   类别 {class_id}: {count} 个样本")
        
        return True, output_dir, split_result
        
    except Exception as e:
        logger.error(f"❌ 数据划分失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, None, None

def validate_split_data(logger, split_dir, split_result):
    """验证划分后的数据质量"""
    logger.info("=== 验证划分后的数据质量 ===")
    
    try:
        train_images_dir = Path(split_dir) / "train" / "images"
        train_masks_dir = Path(split_dir) / "train" / "masks"
        val_images_dir = Path(split_dir) / "val" / "images"
        val_masks_dir = Path(split_dir) / "val" / "masks"
        
        # 验证文件数量
        train_img_count = len(list(train_images_dir.glob("*")))
        train_mask_count = len(list(train_masks_dir.glob("*")))
        val_img_count = len(list(val_images_dir.glob("*")))
        val_mask_count = len(list(val_masks_dir.glob("*")))
        
        logger.info(f"文件数量验证:")
        logger.info(f"   训练集图像: {train_img_count}, 掩码: {train_mask_count}")
        logger.info(f"   验证集图像: {val_img_count}, 掩码: {val_mask_count}")
        
        # 验证数据一致性
        if train_img_count == train_mask_count and val_img_count == val_mask_count:
            logger.info("✅ 数据一致性验证通过")
        else:
            logger.error("❌ 数据一致性验证失败：图像和掩码数量不匹配")
            return False
        
        # 验证样本数量不同
        if train_img_count != val_img_count:
            logger.info("✅ 训练集和验证集样本数量不同，划分正确")
            ratio = train_img_count / (train_img_count + val_img_count)
            logger.info(f"   实际划分比例: {ratio:.3f}:{1-ratio:.3f}")
        else:
            logger.error("❌ 训练集和验证集样本数量相同，划分可能有问题")
            return False
        
        # 保存数据统计信息
        stats = {
            "data_split_timestamp": datetime.now().isoformat(),
            "train_samples": train_img_count,
            "val_samples": val_img_count,
            "total_samples": train_img_count + val_img_count,
            "split_ratio": f"{train_img_count}:{val_img_count}",
            "train_class_distribution": split_result.train_class_distribution,
            "val_class_distribution": split_result.val_class_distribution,
            "data_paths": {
                "train_images": str(train_images_dir),
                "train_masks": str(train_masks_dir),
                "val_images": str(val_images_dir),
                "val_masks": str(val_masks_dir)
            }
        }
        
        stats_file = Path(split_dir) / "data_statistics.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ 数据统计信息已保存到: {stats_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据验证失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def update_config_file(logger, split_dir):
    """更新配置文件中的数据路径"""
    logger.info("=== 更新配置文件 ===")
    
    try:
        import yaml
        
        config_path = project_root / "configs" / "upernet_mae_segmentation.yaml"
        
        # 读取配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 更新数据路径
        config['data']['data_root'] = split_dir
        
        # 保存更新后的配置
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"✅ 配置文件已更新: {config_path}")
        logger.info(f"   数据根目录: {split_dir}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置文件更新失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("🚀 开始执行简化的数据处理流水线")
    
    # 验证现有数据
    success, images_dir, masks_dir = validate_existing_data(logger)
    if not success:
        logger.error("❌ 现有数据验证失败，退出")
        return False
    
    # 执行数据划分
    success, split_dir, split_result = split_dataset(logger, images_dir, masks_dir)
    if not success:
        logger.error("❌ 数据划分失败，退出")
        return False
    
    # 验证划分后的数据
    success = validate_split_data(logger, split_dir, split_result)
    if not success:
        logger.error("❌ 数据验证失败，退出")
        return False
    
    # 更新配置文件
    success = update_config_file(logger, split_dir)
    if not success:
        logger.error("❌ 配置文件更新失败，退出")
        return False
    
    logger.info("🎉 简化的数据处理流水线执行成功！")
    logger.info(f"最终数据集路径: {split_dir}")
    logger.info("现在可以开始UperNet分割训练了！")
    
    return True, split_dir

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ 数据处理流水线执行成功！")
        print("现在可以开始UperNet分割训练了！")
    else:
        print("❌ 数据处理流水线执行失败！")
        sys.exit(1)
