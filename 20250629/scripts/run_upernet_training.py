#!/usr/bin/env python3
"""
UperNet分割训练启动脚本
使用MAE预训练的ViT作为backbone进行8类蜂巢语义分割

流程：
1. 加载MAE预训练权重
2. 构建UperNet分割模型
3. 执行分割训练
4. 保存详细评估指标

Author: AI Assistant
Date: 2024
"""

import os
import sys
import logging
import json
import argparse
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置详细日志"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"upernet_training_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志文件: {log_file}")
    return logger

def validate_config(logger, config_path):
    """验证配置文件"""
    logger.info(f"验证配置文件: {config_path}")
    
    if not Path(config_path).exists():
        logger.error(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 验证关键配置
        mae_path = config.get('model', {}).get('mae_checkpoint_path', '')
        if not Path(mae_path).exists():
            logger.error(f"❌ MAE预训练权重不存在: {mae_path}")
            return False
        
        data_root = config.get('data', {}).get('data_root', '')
        if not Path(data_root).exists():
            logger.error(f"❌ 数据根目录不存在: {data_root}")
            return False
        
        # 验证训练/验证集路径
        train_images_dir = Path(data_root) / config.get('data', {}).get('train_images_dir', '')
        val_images_dir = Path(data_root) / config.get('data', {}).get('val_images_dir', '')
        
        if not train_images_dir.exists():
            logger.error(f"❌ 训练集图像目录不存在: {train_images_dir}")
            return False
        
        if not val_images_dir.exists():
            logger.error(f"❌ 验证集图像目录不存在: {val_images_dir}")
            return False
        
        # 验证设备配置
        device = config.get('system', {}).get('device', '')
        if not device.startswith('cuda'):
            logger.warning(f"⚠️ 未使用GPU: {device}")
        
        logger.info("✅ 配置文件验证通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置文件验证失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def run_upernet_training(logger, config_path, data_dir=None):
    """执行UperNet分割训练"""
    logger.info("=== 开始UperNet分割训练 ===")
    
    try:
        # 导入训练模块
        from training_refactored.run_experiment_safe import run_experiment
        
        # 如果指定了数据目录，更新配置
        if data_dir:
            import yaml
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            config['data']['data_root'] = data_dir
            
            # 保存更新后的配置
            updated_config_path = Path(config_path).parent / f"updated_{Path(config_path).name}"
            with open(updated_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False)
            
            logger.info(f"✅ 配置已更新，使用数据目录: {data_dir}")
            config_path = str(updated_config_path)
        
        # 执行训练
        logger.info(f"开始训练，配置文件: {config_path}")
        success, output_dir = run_experiment(config_path)
        
        if success:
            logger.info(f"✅ UperNet分割训练成功完成!")
            logger.info(f"输出目录: {output_dir}")
            return True, output_dir
        else:
            logger.error(f"❌ UperNet分割训练失败!")
            return False, None
        
    except Exception as e:
        logger.error(f"❌ UperNet分割训练失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, None

def analyze_training_results(logger, output_dir):
    """分析训练结果"""
    logger.info("=== 分析训练结果 ===")
    
    try:
        output_dir = Path(output_dir)
        
        # 检查最佳模型
        best_model_path = output_dir / "best_model.pth"
        if best_model_path.exists():
            logger.info(f"✅ 最佳模型已保存: {best_model_path}")
        else:
            logger.warning(f"⚠️ 未找到最佳模型: {best_model_path}")
        
        # 检查训练日志
        log_file = output_dir / "training_log.json"
        if log_file.exists():
            with open(log_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)
            
            # 分析训练指标
            epochs = len(log_data.get('train_loss', []))
            best_val_loss = min(log_data.get('val_loss', [1000]))
            best_val_miou = max(log_data.get('val_miou', [0]))
            
            logger.info(f"训练总轮数: {epochs}")
            logger.info(f"最佳验证损失: {best_val_loss:.4f}")
            logger.info(f"最佳验证mIoU: {best_val_miou:.4f}")
            
            # 保存分析结果
            analysis_file = output_dir / "training_analysis.json"
            analysis = {
                "epochs": epochs,
                "best_val_loss": best_val_loss,
                "best_val_miou": best_val_miou,
                "training_completed": True,
                "timestamp": datetime.now().isoformat()
            }
            
            with open(analysis_file, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 训练分析已保存到: {analysis_file}")
        else:
            logger.warning(f"⚠️ 未找到训练日志: {log_file}")
        
        # 检查详细评估指标
        metrics_dir = output_dir / "detailed_metrics"
        if metrics_dir.exists():
            metrics_files = list(metrics_dir.glob("*.json"))
            logger.info(f"找到 {len(metrics_files)} 个评估指标文件")
            
            if metrics_files:
                latest_metrics = max(metrics_files, key=lambda p: p.stat().st_mtime)
                logger.info(f"最新评估指标文件: {latest_metrics}")
                
                with open(latest_metrics, 'r', encoding='utf-8') as f:
                    metrics_data = json.load(f)
                
                # 显示类别IoU
                if 'class_iou' in metrics_data:
                    logger.info("类别IoU:")
                    for class_name, iou in metrics_data['class_iou'].items():
                        logger.info(f"   {class_name}: {iou:.4f}")
                
                # 显示总体指标
                if 'miou' in metrics_data:
                    logger.info(f"mIoU: {metrics_data['miou']:.4f}")
                
                if 'accuracy' in metrics_data:
                    logger.info(f"准确率: {metrics_data['accuracy']:.4f}")
        else:
            logger.warning(f"⚠️ 未找到详细评估指标目录: {metrics_dir}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 分析训练结果失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="UperNet分割训练启动脚本")
    parser.add_argument("--config", type=str, default="configs/upernet_mae_segmentation.yaml", help="配置文件路径")
    parser.add_argument("--data-dir", type=str, help="数据目录路径，如果指定则覆盖配置文件中的设置")
    parser.add_argument("--skip-validation", action="store_true", help="跳过配置验证")
    args = parser.parse_args()
    
    logger = setup_logging()
    logger.info("🚀 开始UperNet分割训练流程")
    
    config_path = str(project_root / args.config)
    
    # 验证配置
    if not args.skip_validation:
        if not validate_config(logger, config_path):
            logger.error("❌ 配置验证失败，退出")
            return False
    
    # 执行训练
    success, output_dir = run_upernet_training(logger, config_path, args.data_dir)
    if not success:
        logger.error("❌ UperNet分割训练失败，退出")
        return False
    
    # 分析结果
    success = analyze_training_results(logger, output_dir)
    if not success:
        logger.warning("⚠️ 训练结果分析失败")
    
    logger.info("🎉 UperNet分割训练流程执行完成！")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ UperNet分割训练流程执行成功！")
    else:
        print("❌ UperNet分割训练流程执行失败！")
        sys.exit(1)
