#!/usr/bin/env python3
"""
数据集划分修复脚本
将现有的7208个图块按照7:3比例进行分层划分，确保8个蜂巢类别在训练集和验证集中分布平衡
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from training_refactored.core.datasets.data_splitter import StratifiedDataSplitter, create_stratified_split

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('data_split_fix.log')
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("开始数据集划分修复...")
    
    # 数据路径
    data_root = "/home/<USER>/PycharmProjects/20250629"
    source_images_dir = f"{data_root}/bee_segmentation_project/data/labeled_patches/images"
    source_masks_dir = f"{data_root}/bee_segmentation_project/data/labeled_patches/masks"
    output_dir = f"{data_root}/bee_segmentation_project/data/split_patches"
    
    # 检查源数据目录
    if not os.path.exists(source_images_dir):
        logger.error(f"源图像目录不存在: {source_images_dir}")
        return False
        
    if not os.path.exists(source_masks_dir):
        logger.error(f"源掩码目录不存在: {source_masks_dir}")
        return False
    
    # 统计源数据
    image_files = list(Path(source_images_dir).glob("*.jpg")) + list(Path(source_images_dir).glob("*.png"))
    mask_files = list(Path(source_masks_dir).glob("*.png"))
    
    logger.info(f"找到图像文件: {len(image_files)} 个")
    logger.info(f"找到掩码文件: {len(mask_files)} 个")
    
    # 执行分层划分
    try:
        logger.info("开始执行分层数据划分...")
        
        split_result = create_stratified_split(
            images_dir=source_images_dir,
            masks_dir=source_masks_dir,
            output_dir=output_dir,
            train_ratio=0.7,  # 70% 训练集
            val_ratio=0.3,   # 30% 验证集
            test_ratio=0.0,  # 不创建测试集
            random_seed=42
        )
        
        logger.info("数据划分完成！")
        logger.info(f"训练集: {len(split_result.train_files)} 个样本")
        logger.info(f"验证集: {len(split_result.val_files)} 个样本")
        logger.info(f"测试集: {len(split_result.test_files)} 个样本")
        
        # 显示类别分布
        logger.info("训练集类别分布:")
        for class_id, count in split_result.train_class_distribution.items():
            logger.info(f"  类别 {class_id}: {count} 个样本")
            
        logger.info("验证集类别分布:")
        for class_id, count in split_result.val_class_distribution.items():
            logger.info(f"  类别 {class_id}: {count} 个样本")
        
        # 验证划分结果
        train_dir = Path(output_dir) / "train"
        val_dir = Path(output_dir) / "val"
        
        train_images = len(list((train_dir / "images").glob("*")))
        train_masks = len(list((train_dir / "masks").glob("*")))
        val_images = len(list((val_dir / "images").glob("*")))
        val_masks = len(list((val_dir / "masks").glob("*")))
        
        logger.info(f"验证结果:")
        logger.info(f"  训练集图像: {train_images}, 掩码: {train_masks}")
        logger.info(f"  验证集图像: {val_images}, 掩码: {val_masks}")
        
        if train_images == train_masks and val_images == val_masks:
            logger.info("✅ 数据划分验证通过！")
            return True
        else:
            logger.error("❌ 数据划分验证失败！图像和掩码数量不匹配")
            return False
            
    except Exception as e:
        logger.error(f"数据划分失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ 数据集划分修复完成！")
    else:
        print("❌ 数据集划分修复失败！")
        sys.exit(1)
