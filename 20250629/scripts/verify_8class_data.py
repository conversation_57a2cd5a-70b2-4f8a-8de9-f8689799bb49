#!/usr/bin/env python3
"""
验证8类数据的完整性
检查所有8个类别是否都存在于重新处理的数据中

Author: AI Assistant
Date: 2024
"""

import os
import sys
import numpy as np
import cv2
import json
import logging
from pathlib import Path
from collections import Counter
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def analyze_class_distribution(masks_dir, split_name, logger):
    """分析类别分布"""
    logger.info(f"分析{split_name}的类别分布...")
    
    class_counts = Counter()
    pixel_counts = Counter()
    mask_files = list(Path(masks_dir).glob("*"))
    
    logger.info(f"找到 {len(mask_files)} 个掩码文件")
    
    for i, mask_file in enumerate(mask_files):
        if i % 500 == 0:
            logger.info(f"  处理进度: {i}/{len(mask_files)}")
        
        try:
            mask = cv2.imread(str(mask_file), cv2.IMREAD_GRAYSCALE)
            if mask is not None:
                # 统计像素级别的类别分布
                unique, counts = np.unique(mask, return_counts=True)
                class_pixel_counts = dict(zip(unique, counts))
                
                for class_id, pixel_count in class_pixel_counts.items():
                    pixel_counts[class_id] += pixel_count
                
                # 找到主要类别（排除背景）
                non_bg_counts = {k: v for k, v in class_pixel_counts.items() if k != 0}
                if non_bg_counts:
                    main_class = max(non_bg_counts.items(), key=lambda x: x[1])[0]
                else:
                    main_class = 0
                
                class_counts[main_class] += 1
        except Exception as e:
            logger.warning(f"分析掩码 {mask_file} 失败: {e}")
    
    return dict(class_counts), dict(pixel_counts), len(mask_files)

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("🔍 开始验证8类数据的完整性")
    
    # 数据路径
    patches_dir = "/home/<USER>/PycharmProjects/20250629/reprocessed_data/patches"
    train_masks_dir = Path(patches_dir) / "train" / "masks"
    val_masks_dir = Path(patches_dir) / "val" / "masks"
    
    # 检查目录是否存在
    if not train_masks_dir.exists() or not val_masks_dir.exists():
        logger.error(f"❌ 数据目录不存在:")
        logger.error(f"   训练集: {train_masks_dir}")
        logger.error(f"   验证集: {val_masks_dir}")
        return False
    
    # 分析训练集
    logger.info("=== 分析训练集 ===")
    train_class_counts, train_pixel_counts, train_total = analyze_class_distribution(
        train_masks_dir, "训练集", logger
    )
    
    # 分析验证集
    logger.info("=== 分析验证集 ===")
    val_class_counts, val_pixel_counts, val_total = analyze_class_distribution(
        val_masks_dir, "验证集", logger
    )
    
    # 合并统计
    all_classes = set(train_class_counts.keys()) | set(val_class_counts.keys())
    all_pixel_classes = set(train_pixel_counts.keys()) | set(val_pixel_counts.keys())
    
    logger.info("=== 完整性验证结果 ===")
    logger.info(f"总样本数: {train_total + val_total}")
    logger.info(f"训练集样本: {train_total}")
    logger.info(f"验证集样本: {val_total}")
    
    # 8类蜂巢语义分割类别
    class_names = {
        0: "background",
        1: "capped_brood", 
        2: "eggs",
        3: "honey",
        4: "honeycomb",
        5: "larvae",
        6: "nectar",
        7: "pollen"
    }
    
    expected_classes = set(range(8))  # 0-7
    found_classes = all_classes
    missing_classes = expected_classes - found_classes
    
    logger.info(f"预期类别: {sorted(expected_classes)}")
    logger.info(f"发现类别: {sorted(found_classes)}")
    
    if missing_classes:
        logger.error(f"❌ 缺失类别: {sorted(missing_classes)}")
        for class_id in sorted(missing_classes):
            logger.error(f"   类别 {class_id}: {class_names.get(class_id, '未知')}")
        success = False
    else:
        logger.info("✅ 所有8个类别都存在！")
        success = True
    
    # 详细的类别分布报告
    logger.info("=== 样本级别类别分布（按主要类别） ===")
    logger.info(f"{'类别ID':<6} {'类别名称':<15} {'训练集':<8} {'验证集':<8} {'总计':<8} {'训练比例':<8}")
    logger.info("-" * 70)
    
    for class_id in sorted(expected_classes):
        train_count = train_class_counts.get(class_id, 0)
        val_count = val_class_counts.get(class_id, 0)
        total_count = train_count + val_count
        train_ratio = train_count / total_count if total_count > 0 else 0
        class_name = class_names.get(class_id, "未知")
        
        logger.info(f"{class_id:<6} {class_name:<15} {train_count:<8} {val_count:<8} {total_count:<8} {train_ratio:<8.3f}")
    
    # 像素级别类别分布
    logger.info("=== 像素级别类别分布 ===")
    total_train_pixels = sum(train_pixel_counts.values())
    total_val_pixels = sum(val_pixel_counts.values())
    total_pixels = total_train_pixels + total_val_pixels
    
    logger.info(f"{'类别ID':<6} {'类别名称':<15} {'训练集像素':<12} {'验证集像素':<12} {'总像素':<12} {'比例':<8}")
    logger.info("-" * 80)
    
    for class_id in sorted(expected_classes):
        train_pixels = train_pixel_counts.get(class_id, 0)
        val_pixels = val_pixel_counts.get(class_id, 0)
        total_class_pixels = train_pixels + val_pixels
        ratio = total_class_pixels / total_pixels if total_pixels > 0 else 0
        class_name = class_names.get(class_id, "未知")
        
        logger.info(f"{class_id:<6} {class_name:<15} {train_pixels:<12,} {val_pixels:<12,} {total_class_pixels:<12,} {ratio:<8.3f}")
    
    # 保存详细统计
    stats = {
        "verification_timestamp": datetime.now().isoformat(),
        "success": success,
        "train_samples": train_total,
        "val_samples": val_total,
        "total_samples": train_total + val_total,
        "expected_classes": sorted(expected_classes),
        "found_classes": sorted(found_classes),
        "missing_classes": sorted(missing_classes),
        "train_class_distribution": {str(k): int(v) for k, v in train_class_counts.items()},
        "val_class_distribution": {str(k): int(v) for k, v in val_class_counts.items()},
        "train_pixel_distribution": {str(k): int(v) for k, v in train_pixel_counts.items()},
        "val_pixel_distribution": {str(k): int(v) for k, v in val_pixel_counts.items()},
        "class_names": class_names
    }
    
    stats_file = Path(patches_dir) / "8class_verification_report.json"
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2, ensure_ascii=False)
    
    logger.info(f"✅ 验证报告已保存到: {stats_file}")
    
    if success:
        logger.info("🎉 8类数据验证成功！所有类别都存在。")
    else:
        logger.error("❌ 8类数据验证失败！存在缺失类别。")
    
    return success

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ 8类数据验证完成！")
    else:
        print("❌ 8类数据验证失败！")
        sys.exit(1)
