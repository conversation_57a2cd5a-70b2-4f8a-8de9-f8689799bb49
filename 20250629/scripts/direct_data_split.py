#!/usr/bin/env python3
"""
直接数据划分脚本
使用现有的7208个图块，直接进行7:3划分并复制文件

Author: AI Assistant
Date: 2024
"""

import os
import sys
import shutil
import random
import logging
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置详细日志"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"direct_split_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志文件: {log_file}")
    return logger

def get_valid_pairs(images_dir, masks_dir, logger):
    """获取有效的图像-掩码对"""
    images_dir = Path(images_dir)
    masks_dir = Path(masks_dir)
    
    # 获取所有图像文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    image_files = []
    for ext in image_extensions:
        image_files.extend(list(images_dir.glob(f"*{ext}")))
        image_files.extend(list(images_dir.glob(f"*{ext.upper()}")))
    
    # 匹配掩码文件
    valid_pairs = []
    for image_file in image_files:
        mask_file = masks_dir / f"{image_file.stem}.png"
        if mask_file.exists():
            valid_pairs.append((str(image_file), str(mask_file)))
        else:
            logger.warning(f"找不到对应的掩码文件: {mask_file}")
    
    logger.info(f"找到 {len(valid_pairs)} 个有效的图像-掩码对")
    return valid_pairs

def split_data(valid_pairs, train_ratio=0.7, val_ratio=0.3, random_seed=42):
    """划分数据"""
    random.seed(random_seed)
    random.shuffle(valid_pairs)
    
    total_count = len(valid_pairs)
    train_count = int(total_count * train_ratio)
    
    train_pairs = valid_pairs[:train_count]
    val_pairs = valid_pairs[train_count:]
    
    return train_pairs, val_pairs

def copy_files(pairs, output_dir, split_name, logger):
    """复制文件到目标目录"""
    images_dir = Path(output_dir) / split_name / "images"
    masks_dir = Path(output_dir) / split_name / "masks"
    
    # 创建目录
    images_dir.mkdir(parents=True, exist_ok=True)
    masks_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"开始复制{split_name}集文件...")
    
    for i, (image_path, mask_path) in enumerate(pairs):
        if i % 500 == 0:
            logger.info(f"  复制进度: {i}/{len(pairs)}")
        
        # 复制图像文件
        image_name = Path(image_path).name
        shutil.copy2(image_path, images_dir / image_name)
        
        # 复制掩码文件
        mask_name = Path(mask_path).name
        shutil.copy2(mask_path, masks_dir / mask_name)
    
    logger.info(f"✅ {split_name}集复制完成: {len(pairs)} 个文件对")
    return len(pairs)

def validate_split(output_dir, logger):
    """验证划分结果"""
    train_images_dir = Path(output_dir) / "train" / "images"
    train_masks_dir = Path(output_dir) / "train" / "masks"
    val_images_dir = Path(output_dir) / "val" / "images"
    val_masks_dir = Path(output_dir) / "val" / "masks"
    
    # 统计文件数量
    train_img_count = len(list(train_images_dir.glob("*")))
    train_mask_count = len(list(train_masks_dir.glob("*")))
    val_img_count = len(list(val_images_dir.glob("*")))
    val_mask_count = len(list(val_masks_dir.glob("*")))
    
    logger.info(f"文件数量验证:")
    logger.info(f"   训练集图像: {train_img_count}, 掩码: {train_mask_count}")
    logger.info(f"   验证集图像: {val_img_count}, 掩码: {val_mask_count}")
    
    # 验证一致性
    if train_img_count == train_mask_count and val_img_count == val_mask_count:
        logger.info("✅ 数据一致性验证通过")
    else:
        logger.error("❌ 数据一致性验证失败")
        return False
    
    # 验证划分比例
    total_samples = train_img_count + val_img_count
    train_ratio = train_img_count / total_samples
    val_ratio = val_img_count / total_samples
    
    logger.info(f"实际划分比例: {train_ratio:.3f}:{val_ratio:.3f}")
    
    if train_img_count != val_img_count:
        logger.info("✅ 训练集和验证集样本数量不同，划分正确")
    else:
        logger.error("❌ 训练集和验证集样本数量相同")
        return False
    
    return True

def save_statistics(output_dir, train_count, val_count, logger):
    """保存统计信息"""
    stats = {
        "data_split_timestamp": datetime.now().isoformat(),
        "train_samples": train_count,
        "val_samples": val_count,
        "total_samples": train_count + val_count,
        "split_ratio": f"{train_count}:{val_count}",
        "train_ratio": train_count / (train_count + val_count),
        "val_ratio": val_count / (train_count + val_count),
        "data_paths": {
            "train_images": str(Path(output_dir) / "train" / "images"),
            "train_masks": str(Path(output_dir) / "train" / "masks"),
            "val_images": str(Path(output_dir) / "val" / "images"),
            "val_masks": str(Path(output_dir) / "val" / "masks")
        }
    }
    
    stats_file = Path(output_dir) / "data_statistics.json"
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2, ensure_ascii=False)
    
    logger.info(f"✅ 统计信息已保存到: {stats_file}")

def update_config(output_dir, logger):
    """更新配置文件"""
    try:
        import yaml
        
        config_path = project_root / "configs" / "upernet_mae_segmentation.yaml"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        config['data']['data_root'] = str(output_dir)
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"✅ 配置文件已更新: {config_path}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置文件更新失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("🚀 开始直接数据划分")
    
    # 数据路径
    source_images_dir = "/home/<USER>/PycharmProjects/20250629/bee_segmentation_project/data/labeled_patches/images"
    source_masks_dir = "/home/<USER>/PycharmProjects/20250629/bee_segmentation_project/data/labeled_patches/masks"
    output_dir = "/home/<USER>/PycharmProjects/20250629/processed_data/split_dataset"
    
    # 验证源数据
    if not Path(source_images_dir).exists():
        logger.error(f"❌ 源图像目录不存在: {source_images_dir}")
        return False
    
    if not Path(source_masks_dir).exists():
        logger.error(f"❌ 源掩码目录不存在: {source_masks_dir}")
        return False
    
    # 获取有效文件对
    valid_pairs = get_valid_pairs(source_images_dir, source_masks_dir, logger)
    if not valid_pairs:
        logger.error("❌ 没有找到有效的文件对")
        return False
    
    # 划分数据
    train_pairs, val_pairs = split_data(valid_pairs, train_ratio=0.7, val_ratio=0.3)
    logger.info(f"数据划分完成: 训练集 {len(train_pairs)}, 验证集 {len(val_pairs)}")
    
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 复制文件
    train_count = copy_files(train_pairs, output_dir, "train", logger)
    val_count = copy_files(val_pairs, output_dir, "val", logger)
    
    # 验证结果
    if not validate_split(output_dir, logger):
        logger.error("❌ 数据验证失败")
        return False
    
    # 保存统计信息
    save_statistics(output_dir, train_count, val_count, logger)
    
    # 更新配置文件
    if not update_config(output_dir, logger):
        logger.warning("⚠️ 配置文件更新失败")
    
    logger.info("🎉 直接数据划分完成！")
    logger.info(f"输出目录: {output_dir}")
    logger.info("现在可以开始UperNet分割训练了！")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ 数据划分完成！")
    else:
        print("❌ 数据划分失败！")
        sys.exit(1)
