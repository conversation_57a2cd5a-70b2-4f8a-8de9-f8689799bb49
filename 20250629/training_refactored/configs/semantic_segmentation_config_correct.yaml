# 正确的语义分割配置
# 基于真实像素级分布的正确类别映射

model:
  feature_extractor:
    type: "MultiLayerViTFeatureExtractor"
    mae_checkpoint: "/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/best_checkpoint_epoch_0527.pth"
    layers: [3, 6, 9, 12]
    img_size: 512
    patch_size: 16
    embed_dim: 768
    depth: 12
    num_heads: 12

  segmentation_head:
    type: "UPerNetHead"
    in_channels: [768, 768, 768, 768]
    in_index: [0, 1, 2, 3]
    pool_scales: [1, 2, 3, 6]
    channels: 512
    dropout_ratio: 0.1
    num_classes: 8
    fpn_dim: 256
    norm_cfg:
      type: "GroupNorm"
      num_groups: 32
    align_corners: false

# 数据配置
data:
  # 预分块数据路径 - 使用重新处理的完整8类数据
  train_dir: "/home/<USER>/PycharmProjects/20250629/reprocessed_data/patches/train"
  val_dir: "/home/<USER>/PycharmProjects/20250629/reprocessed_data/patches/val"

  # 原始数据路径（用于动态滑动窗口，如果需要）
  image_dir: "/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/static/uploads"
  mask_dir: "/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/data/processed_masks"

  # 数据配置
  image_size: [512, 512]
  num_classes: 8
  batch_size: 16  # 从8提升到16，充分利用3090 24GB显存
  num_workers: 10
  pin_memory: true

  # 滑动窗口配置（如果使用动态滑动窗口）
  patch_size: 512
  overlap_ratio: 0.25
  min_valid_pixels: 1000
  train_ratio: 0.8
  random_seed: 42

  # 数据增强配置
  augmentation:
    enable: true
    random_rotate90: 0.5
    horizontal_flip: 0.5
    vertical_flip: 0.5
    color_jitter:
      brightness: 0.2
      contrast: 0.2
      saturation: 0.2
      hue: 0.1
    elastic_transform: 0.3
    gaussian_noise: 0.2

  # 数据标准化配置
  normalize:
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]

  # 数据变换配置
  train_transforms:
    - type: "Resize"
      size: [512, 512]
    - type: "RandomHorizontalFlip"
      p: 0.5
    - type: "RandomVerticalFlip"
      p: 0.5
    - type: "ColorJitter"
      brightness: 0.2
      contrast: 0.2
      saturation: 0.2
      hue: 0.1
    - type: "RandomRotation"
      degrees: 10
    - type: "Normalize"
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
    - type: "ToTensorV2"

  val_transforms:
    - type: "Resize"
      size: [512, 512]
    - type: "Normalize"
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
    - type: "ToTensorV2"

# 正确的类别配置（基于真实像素分布）
classes:
  num_classes: 8
  # 按真实数据中的ID顺序排列
  class_names:
    - "background"    # ID=0, 39.39%像素
    - "capped_brood"  # ID=1, 10.81%像素  
    - "eggs"          # ID=2, 0.34%像素
    - "honey"         # ID=3, 2.86%像素
    - "honeycomb"     # ID=4, 42.37%像素 - 最重要！
    - "larvae"        # ID=5, 0.65%像素
    - "nectar"        # ID=6, 2.44%像素
    - "pollen"        # ID=7, 1.14%像素
  
  # 类别权重（基于真实像素分布优化 - 2024-07-24更新）
  # 计算方法: sqrt(1 / pixel_percentage) + 小类别额外加权
  # background(39.39%) -> 2.3, honeycomb(42.37%) -> 2.2
  # capped_brood(10.81%) -> 4.4, honey(2.86%) -> 8.6
  # nectar(2.44%) -> 9.3, pollen(1.14%) -> 13.7
  # larvae(0.65%) -> 36.2, eggs(0.34%) -> 50.0 (极小类别重点加权)
  class_weights: [2.3, 4.4, 50.0, 8.6, 2.2, 36.2, 9.3, 13.7]

# 损失函数配置 - 优化版本（针对极度类别不平衡）
loss:
  dice_weight: 0.2      # 降低Dice权重，避免在极度不平衡时不稳定
  ce_weight: 0.2        # 保持基础分类损失
  focal_weight: 0.6     # 大幅提高Focal权重，专门处理困难样本
  focal_alpha: [0.5, 3.0, 5.0, 1.5, 0.8, 4.0, 2.5, 3.5]  # 为每个类别设置权重
  focal_gamma: 2.0      # 降低gamma，避免过度聚焦导致训练不稳定

# 优化器配置 - 优化版本（稍微激进的学习率）
optimizer:
  feature_extractor_lr: 0.00003  # 编码器学习率提高50%
  segmentation_head_lr: 0.0003   # 分割头学习率提高50%
  weight_decay: 0.01
  betas: [0.9, 0.999]
  eps: 0.00000001

# 学习率调度器配置 - 优化版本（延长预热期）
scheduler:
  type: "WarmupCosineAnnealingLR"
  max_epochs: 300
  warmup_epochs: 30      # 延长预热期，有助于稀有类别学习
  warmup_start_lr: 0.000001
  eta_min: 0.0000001     # 降低最小学习率，允许更精细调整

# 训练配置 - 针对极度类别不平衡优化
training:
  epochs: 300  # 增加训练轮数，小类别需要更多时间学习
  mixed_precision: true

  # 验证和保存
  val_interval: 1
  save_interval: 5  # 更频繁保存，避免丢失小类别学习进度

  # 早停配置 - 基于训练曲线分析优化
  early_stopping:
    patience: 15  # 减少耐心，避免过度训练
    min_delta: 0.001  # 提高最小改进阈值，更敏感
    monitor: "val_miou"  # 监控标准mIoU
    mode: "max"
    restore_best_weights: true  # 恢复最佳权重





# 移除重复的损失函数配置（已在上面优化）

# 移除重复的输出配置（已在下面定义）



# 滑动窗口配置
sliding_window:
  window_size: 512
  stride: 256
  min_overlap: 0.3

# 调试配置
debug:
  enable: false
  save_intermediate: false
  log_level: "INFO"

# 评估配置
evaluation:
  metrics: ["accuracy", "iou", "dice", "precision", "recall", "f1"]
  save_predictions: true
  save_visualizations: true

# 输出配置
output:
  output_dir: "outputs/semantic_segmentation_correct"
  log_file: "training.log"
  log_level: "INFO"

# 硬件配置
hardware:
  device: "cuda"
  num_workers: 10
  pin_memory: true
