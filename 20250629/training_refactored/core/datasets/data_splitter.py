"""
数据集分层划分工具

实现科学的数据集划分，确保训练/验证/测试集的分布平衡，
满足学术实验的严谨性要求。
"""

import os
import random
import logging
import numpy as np
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
import json
from collections import defaultdict

logger = logging.getLogger(__name__)


@dataclass
class DataSplit:
    """数据划分结果"""
    train_files: List[str]
    val_files: List[str]
    test_files: List[str]
    
    # 统计信息
    train_class_distribution: Dict[int, int]
    val_class_distribution: Dict[int, int]
    test_class_distribution: Dict[int, int]
    
    def to_dict(self) -> Dict[str, Union[List[str], Dict[str, int]]]:
        """转换为字典格式"""
        # 转换numpy类型为Python原生类型
        def convert_dict(d):
            return {str(k): int(v) if hasattr(v, 'item') else v for k, v in d.items()}

        return {
            'train_files': self.train_files,
            'val_files': self.val_files,
            'test_files': self.test_files,
            'train_class_distribution': convert_dict(self.train_class_distribution),
            'val_class_distribution': convert_dict(self.val_class_distribution),
            'test_class_distribution': convert_dict(self.test_class_distribution)
        }


class StratifiedDataSplitter:
    """
    分层数据集划分器
    
    实现基于类别分布的分层划分，确保各个子集的类别分布尽可能平衡。
    """
    
    def __init__(
        self,
        images_dir: str,
        masks_dir: str,
        train_ratio: float = 0.7,
        val_ratio: float = 0.15,
        test_ratio: float = 0.15,
        random_seed: int = 42
    ):
        """
        初始化数据划分器
        
        Args:
            images_dir: 图像目录
            masks_dir: 掩码目录
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            test_ratio: 测试集比例
            random_seed: 随机种子
        """
        self.images_dir = Path(images_dir)
        self.masks_dir = Path(masks_dir)
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
        self.random_seed = random_seed
        
        # 验证比例
        if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
            raise ValueError("训练、验证、测试集比例之和必须等于1.0")
        
        # 设置随机种子
        random.seed(random_seed)
        np.random.seed(random_seed)
        
        logger.info(f"数据划分器初始化完成: 训练={train_ratio}, 验证={val_ratio}, 测试={test_ratio}")
    
    def split_dataset(self, stratify_by_class: bool = True) -> DataSplit:
        """
        执行数据集划分
        
        Args:
            stratify_by_class: 是否按类别分层划分
            
        Returns:
            数据划分结果
        """
        logger.info("开始数据集划分...")
        
        # 获取所有文件对
        file_pairs = self._get_valid_file_pairs()
        logger.info(f"找到 {len(file_pairs)} 个有效的图像-掩码对")
        
        if len(file_pairs) == 0:
            raise ValueError("没有找到有效的图像-掩码对")
        
        if stratify_by_class:
            # 分层划分
            split_result = self._stratified_split(file_pairs)
        else:
            # 随机划分
            split_result = self._random_split(file_pairs)
        
        # 验证划分结果
        self._validate_split(split_result)
        
        logger.info("数据集划分完成")
        self._print_split_summary(split_result)
        
        return split_result
    
    def _get_valid_file_pairs(self) -> List[Tuple[str, str]]:
        """获取有效的图像-掩码文件对"""
        # 获取图像文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(list(self.images_dir.glob(f"*{ext}")))
            image_files.extend(list(self.images_dir.glob(f"*{ext.upper()}")))
        
        # 匹配掩码文件
        valid_pairs = []
        
        for image_file in image_files:
            # 查找对应的掩码文件
            mask_file = self.masks_dir / f"{image_file.stem}.png"
            
            if mask_file.exists():
                valid_pairs.append((str(image_file), str(mask_file)))
            else:
                logger.warning(f"找不到对应的掩码文件: {mask_file}")
        
        return valid_pairs
    
    def _stratified_split(self, file_pairs: List[Tuple[str, str]]) -> DataSplit:
        """基于类别分布的分层划分"""
        logger.info("执行分层划分...")
        
        # 分析每个样本的类别分布
        sample_class_info = []
        
        for image_path, mask_path in file_pairs:
            class_distribution = self._analyze_mask_classes(mask_path)
            dominant_class = max(class_distribution.items(), key=lambda x: x[1])[0]
            
            sample_class_info.append({
                'image_path': image_path,
                'mask_path': mask_path,
                'class_distribution': class_distribution,
                'dominant_class': dominant_class
            })
        
        # 按主导类别分组
        class_groups = defaultdict(list)
        for sample in sample_class_info:
            class_groups[sample['dominant_class']].append(sample)
        
        # 对每个类别组进行划分
        train_samples = []
        val_samples = []
        test_samples = []
        
        for class_id, samples in class_groups.items():
            # 随机打乱
            random.shuffle(samples)
            
            # 计算划分点
            n_samples = len(samples)
            n_train = int(n_samples * self.train_ratio)
            n_val = int(n_samples * self.val_ratio)
            
            # 划分
            train_samples.extend(samples[:n_train])
            val_samples.extend(samples[n_train:n_train + n_val])
            test_samples.extend(samples[n_train + n_val:])
            
            logger.info(f"类别 {class_id}: {len(samples)} 样本 -> "
                       f"训练:{len(samples[:n_train])}, "
                       f"验证:{len(samples[n_train:n_train + n_val])}, "
                       f"测试:{len(samples[n_train + n_val:])}")
        
        # 构建结果
        return self._build_split_result(train_samples, val_samples, test_samples)
    
    def _random_split(self, file_pairs: List[Tuple[str, str]]) -> DataSplit:
        """随机划分"""
        logger.info("执行随机划分...")
        
        # 随机打乱
        random.shuffle(file_pairs)
        
        # 计算划分点
        n_total = len(file_pairs)
        n_train = int(n_total * self.train_ratio)
        n_val = int(n_total * self.val_ratio)
        
        # 划分
        train_pairs = file_pairs[:n_train]
        val_pairs = file_pairs[n_train:n_train + n_val]
        test_pairs = file_pairs[n_train + n_val:]
        
        # 转换为样本信息格式
        train_samples = [{'image_path': img, 'mask_path': mask} for img, mask in train_pairs]
        val_samples = [{'image_path': img, 'mask_path': mask} for img, mask in val_pairs]
        test_samples = [{'image_path': img, 'mask_path': mask} for img, mask in test_pairs]
        
        return self._build_split_result(train_samples, val_samples, test_samples)
    
    def _analyze_mask_classes(self, mask_path: str) -> Dict[int, int]:
        """分析掩码中的类别分布"""
        try:
            mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
            if mask is None:
                logger.warning(f"无法读取掩码文件: {mask_path}")
                return {}
            
            # 统计类别分布
            unique, counts = np.unique(mask, return_counts=True)
            class_distribution = dict(zip(unique.astype(int), counts.astype(int)))
            
            # 移除可能的忽略类别（如255）
            if 255 in class_distribution:
                del class_distribution[255]
            
            return class_distribution
            
        except Exception as e:
            logger.error(f"分析掩码类别失败 {mask_path}: {e}")
            return {}
    
    def _build_split_result(
        self, 
        train_samples: List[Dict], 
        val_samples: List[Dict], 
        test_samples: List[Dict]
    ) -> DataSplit:
        """构建划分结果"""
        # 提取文件路径
        train_files = [sample['image_path'] for sample in train_samples]
        val_files = [sample['image_path'] for sample in val_samples]
        test_files = [sample['image_path'] for sample in test_samples]
        
        # 计算类别分布
        train_class_dist = self._compute_class_distribution([sample['mask_path'] for sample in train_samples])
        val_class_dist = self._compute_class_distribution([sample['mask_path'] for sample in val_samples])
        test_class_dist = self._compute_class_distribution([sample['mask_path'] for sample in test_samples])
        
        return DataSplit(
            train_files=train_files,
            val_files=val_files,
            test_files=test_files,
            train_class_distribution=train_class_dist,
            val_class_distribution=val_class_dist,
            test_class_distribution=test_class_dist
        )
    
    def _compute_class_distribution(self, mask_paths: List[str]) -> Dict[int, int]:
        """计算类别分布"""
        total_distribution = defaultdict(int)
        
        for mask_path in mask_paths:
            class_dist = self._analyze_mask_classes(mask_path)
            for class_id, count in class_dist.items():
                total_distribution[class_id] += count
        
        return dict(total_distribution)
    
    def _validate_split(self, split_result: DataSplit):
        """验证划分结果"""
        total_files = len(split_result.train_files) + len(split_result.val_files) + len(split_result.test_files)
        
        if total_files == 0:
            raise ValueError("划分结果为空")
        
        # 检查比例
        actual_train_ratio = len(split_result.train_files) / total_files
        actual_val_ratio = len(split_result.val_files) / total_files
        actual_test_ratio = len(split_result.test_files) / total_files
        
        logger.info(f"实际划分比例: 训练={actual_train_ratio:.3f}, "
                   f"验证={actual_val_ratio:.3f}, 测试={actual_test_ratio:.3f}")
    
    def _print_split_summary(self, split_result: DataSplit):
        """打印划分摘要"""
        logger.info("数据集划分摘要:")
        logger.info(f"  训练集: {len(split_result.train_files)} 样本")
        logger.info(f"  验证集: {len(split_result.val_files)} 样本")
        logger.info(f"  测试集: {len(split_result.test_files)} 样本")
        
        # 打印类别分布
        all_classes = set()
        all_classes.update(split_result.train_class_distribution.keys())
        all_classes.update(split_result.val_class_distribution.keys())
        all_classes.update(split_result.test_class_distribution.keys())
        
        logger.info("类别分布:")
        for class_id in sorted(all_classes):
            train_count = split_result.train_class_distribution.get(class_id, 0)
            val_count = split_result.val_class_distribution.get(class_id, 0)
            test_count = split_result.test_class_distribution.get(class_id, 0)
            
            logger.info(f"  类别 {class_id}: 训练={train_count}, 验证={val_count}, 测试={test_count}")
    
    def save_split(self, split_result: DataSplit, output_dir: str):
        """保存划分结果"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存文件列表
        splits = {
            'train': split_result.train_files,
            'val': split_result.val_files,
            'test': split_result.test_files
        }
        
        for split_name, file_list in splits.items():
            split_file = output_path / f"{split_name}_files.txt"
            with open(split_file, 'w') as f:
                for file_path in file_list:
                    f.write(f"{file_path}\n")
        
        # 保存完整结果
        result_file = output_path / "data_split.json"
        with open(result_file, 'w') as f:
            json.dump(split_result.to_dict(), f, indent=2, default=str)
        
        logger.info(f"数据划分结果已保存到: {output_path}")


def create_stratified_split(
    images_dir: str,
    masks_dir: str,
    output_dir: str,
    train_ratio: float = 0.7,
    val_ratio: float = 0.15,
    test_ratio: float = 0.15,
    random_seed: int = 42
) -> DataSplit:
    """
    创建分层数据划分的便捷函数
    
    Args:
        images_dir: 图像目录
        masks_dir: 掩码目录
        output_dir: 输出目录
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        test_ratio: 测试集比例
        random_seed: 随机种子
        
    Returns:
        数据划分结果
    """
    splitter = StratifiedDataSplitter(
        images_dir=images_dir,
        masks_dir=masks_dir,
        train_ratio=train_ratio,
        val_ratio=val_ratio,
        test_ratio=test_ratio,
        random_seed=random_seed
    )
    
    split_result = splitter.split_dataset(stratify_by_class=True)
    splitter.save_split(split_result, output_dir)
    
    return split_result
