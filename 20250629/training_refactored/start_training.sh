#!/bin/bash

# 语义分割训练启动脚本
# 使用正确的类别映射和优化的权重配置

echo "🚀 启动语义分割训练"
echo "配置文件: configs/semantic_segmentation_config_correct.yaml"
echo "=========================================="

# 检查配置文件
if [ ! -f "configs/semantic_segmentation_config_correct.yaml" ]; then
    echo "❌ 配置文件不存在"
    exit 1
fi

# 检查MAE权重
MAE_WEIGHTS="/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/best_checkpoint_epoch_0527.pth"
if [ ! -f "$MAE_WEIGHTS" ]; then
    echo "❌ MAE权重文件不存在: $MAE_WEIGHTS"
    exit 1
fi

# 创建输出目录
mkdir -p outputs/semantic_segmentation_correct

echo "✅ 环境检查通过"
echo "🎯 8类蜂巢语义分割数据分布:"
echo "  honeycomb: 48.6% (4704个样本)"
echo "  background: 19.9% (366M像素)"
echo "  capped_brood: 14.5% (1139个样本)"
echo "  nectar: 5.5% (433个样本)"
echo "  honey: 4.5% (494个样本) ✅"
echo "  pollen: 4.6% (187个样本) ✅"
echo "  eggs: 1.3% (82个样本)"
echo "  larvae: 1.1% (9个样本)"
echo "  总样本数: 7048个 (训练集: 3185, 验证集: 3863)"
echo "=========================================="

# 启动训练 (使用第二块显卡)
export CUDA_VISIBLE_DEVICES=1
python scripts/train_semantic_segmentation.py \
    --config configs/semantic_segmentation_config_correct.yaml
