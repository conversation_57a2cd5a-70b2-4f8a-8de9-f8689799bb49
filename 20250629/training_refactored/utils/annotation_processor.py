#!/usr/bin/env python3
"""
标注数据处理器
专门处理bee_cell_annotation中的8类标注数据

功能：
1. JSON多边形标注转换为语义分割mask
2. CSV点标注处理和验证
3. 8类蜂巢内容物类别映射
4. 标注质量检查和统计

Author: AI Assistant
Date: 2024
"""

import json
import csv
import numpy as np
import cv2
from PIL import Image, ImageDraw
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import logging
from collections import defaultdict, Counter

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BeeAnnotationProcessor:
    """
    蜂巢标注数据处理器
    
    处理bee_cell_annotation目录中的JSON和CSV标注数据
    """
    
    # 8类蜂巢内容物映射
    CLASS_MAPPING = {
        'background': 0,     # 背景（多边形外的区域）
        'capped_brood': 1,   # 封盖子 (4,606个标注) - 最多
        'eggs': 2,           # 卵 (1,147个标注)
        'honey': 3,          # 蜂蜜 (361个标注)
        'honeycomb': 4,      # 蜂巢结构 (66个标注) - 最少
        'larvae': 5,         # 幼虫 (3,055个标注)
        'nectar': 6,         # 花蜜 (167个标注)
        'pollen': 7          # 花粉 (850个标注)
    }
    
    # 类别名称列表
    CLASS_NAMES = list(CLASS_MAPPING.keys())
    
    def __init__(self, annotation_dir: str, output_dir: str = None):
        """
        Args:
            annotation_dir: 标注数据目录路径
            output_dir: 输出目录，如果为None则在annotation_dir下创建masks子目录
        """
        self.annotation_dir = Path(annotation_dir)
        
        if output_dir is None:
            self.output_dir = self.annotation_dir.parent / 'processed_masks'
        else:
            self.output_dir = Path(output_dir)
        
        self.output_dir.mkdir(exist_ok=True)
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'class_counts': Counter(),
            'polygon_counts': Counter(),
            'errors': []
        }
        
        logger.info(f"标注处理器初始化完成")
        logger.info(f"输入目录: {self.annotation_dir}")
        logger.info(f"输出目录: {self.output_dir}")
    
    def process_all_annotations(self) -> Dict:
        """
        处理所有标注文件
        
        Returns:
            处理统计信息
        """
        json_files = list(self.annotation_dir.glob('*.json'))
        self.stats['total_files'] = len(json_files)
        
        logger.info(f"发现 {len(json_files)} 个JSON标注文件")
        
        for json_file in json_files:
            try:
                self._process_single_annotation(json_file)
                self.stats['processed_files'] += 1
            except Exception as e:
                error_msg = f"处理文件 {json_file.name} 时出错: {str(e)}"
                logger.error(error_msg)
                self.stats['errors'].append(error_msg)
        
        self._print_statistics()
        return self.stats
    
    def _process_single_annotation(self, json_file: Path):
        """
        处理单个标注文件

        Args:
            json_file: JSON标注文件路径
        """
        # 加载JSON标注
        with open(json_file, 'r', encoding='utf-8') as f:
            annotations = json.load(f)

        if not annotations:
            logger.warning(f"文件 {json_file.name} 中没有标注数据")
            return

        # 获取对应的原始图像尺寸
        image_size = self._get_actual_image_size(json_file)
        if image_size is None:
            logger.error(f"无法获取图像尺寸: {json_file}")
            return

        # 创建mask
        mask = self._create_mask_from_annotations(annotations, image_size)
        
        # 保存mask
        mask_filename = json_file.stem + '_mask.png'
        mask_path = self.output_dir / mask_filename
        
        # 保存为单通道PNG
        Image.fromarray(mask.astype(np.uint8)).save(mask_path)
        
        # 更新统计
        unique_classes, counts = np.unique(mask, return_counts=True)
        for cls, count in zip(unique_classes, counts):
            if cls < len(self.CLASS_NAMES):
                self.stats['class_counts'][self.CLASS_NAMES[cls]] += count
        
        self.stats['polygon_counts'][json_file.stem] = len(annotations)
        
        logger.debug(f"处理完成: {json_file.name} -> {mask_filename}")

    def _get_actual_image_size(self, json_file: Path) -> Optional[Tuple[int, int]]:
        """
        获取对应原始图像的实际尺寸

        Args:
            json_file: JSON标注文件路径

        Returns:
            (width, height) 图像尺寸，如果找不到图像则返回None
        """
        # 构建对应的图像文件路径
        image_name = json_file.stem + '.JPG'  # 假设图像是JPG格式

        # 可能的图像路径
        possible_paths = [
            self.annotation_dir.parent / 'static' / 'uploads' / image_name,
            self.annotation_dir.parent.parent / 'static' / 'uploads' / image_name,
            Path("/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/static/uploads") / image_name,
            self.annotation_dir.parent / 'images' / image_name,
            self.annotation_dir.parent / image_name
        ]

        for image_path in possible_paths:
            if image_path.exists():
                try:
                    with Image.open(image_path) as img:
                        width, height = img.size
                        logger.debug(f"获取图像尺寸: {image_name} -> {width}x{height}")
                        return width, height
                except Exception as e:
                    logger.warning(f"读取图像失败 {image_path}: {e}")
                    continue

        logger.error(f"找不到对应的图像文件: {image_name}")
        return None

    def _estimate_image_size(self, annotations: List[Dict]) -> Tuple[int, int]:
        """
        从标注数据估算图像尺寸
        
        Args:
            annotations: 标注数据列表
            
        Returns:
            (width, height) 图像尺寸
        """
        max_x, max_y = 0, 0
        
        for annotation in annotations:
            if 'points' in annotation:
                for point in annotation['points']:
                    max_x = max(max_x, point.get('x', 0))
                    max_y = max(max_y, point.get('y', 0))
        
        # 添加一些边距
        width = int(max_x) + 100
        height = int(max_y) + 100
        
        logger.debug(f"估算图像尺寸: {width} x {height}")
        return width, height
    
    def _create_mask_from_annotations(
        self,
        annotations: List[Dict],
        image_size: Tuple[int, int]
    ) -> np.ndarray:
        """
        创建8类语义分割mask，新的honeycomb逻辑：
        1. 先创建所有标注的联合区域作为honeycomb(4)
        2. 再在honeycomb基础上绘制具体内容物类别
        3. background(0)只有完全未被标注的区域

        Args:
            annotations: 标注数据列表
            image_size: (width, height) 图像尺寸

        Returns:
            8类语义分割mask数组
        """
        width, height = image_size

        # 第一步：创建honeycomb基础mask - 所有标注区域都是honeycomb
        honeycomb_mask = np.zeros((height, width), dtype=np.uint8)

        # 统计处理的标注
        processed_count = 0
        skipped_count = 0

        logger.debug(f"开始创建honeycomb基础mask...")

        # 将所有标注区域标记为honeycomb
        for annotation in annotations:
            annotation_type = annotation.get('type', '')

            if annotation_type == 'polygon' and 'points' in annotation:
                points = annotation['points']

                if len(points) >= 3:
                    try:
                        # 转换点坐标
                        polygon_points = []
                        for point in points:
                            x = int(point.get('x', 0))
                            y = int(point.get('y', 0))

                            # 确保坐标在图像范围内
                            x = max(0, min(x, width - 1))
                            y = max(0, min(y, height - 1))
                            polygon_points.append([x, y])

                        # 绘制到honeycomb基础mask
                        polygon_array = np.array([polygon_points], dtype=np.int32)
                        cv2.fillPoly(honeycomb_mask, polygon_array, 4)  # honeycomb = 4
                        processed_count += 1

                    except (ValueError, TypeError) as e:
                        logger.warning(f"处理多边形标注失败: {e}")
                        skipped_count += 1

            elif annotation_type == 'circle':
                try:
                    center_x = int(annotation.get('x', 0))
                    center_y = int(annotation.get('y', 0))
                    radius = int(annotation.get('radius', 0))

                    # 确保坐标在图像范围内
                    center_x = max(0, min(center_x, width - 1))
                    center_y = max(0, min(center_y, height - 1))

                    if radius > 0:
                        # 绘制到honeycomb基础mask
                        cv2.circle(honeycomb_mask, (center_x, center_y), radius, 4, -1)  # honeycomb = 4
                        processed_count += 1

                except (ValueError, TypeError) as e:
                    logger.warning(f"处理圆形标注失败: {e}")
                    skipped_count += 1

        logger.debug(f"honeycomb基础mask创建完成，处理了 {processed_count} 个标注")

        # 第二步：在honeycomb基础上绘制具体内容物类别
        final_mask = honeycomb_mask.copy()

        # 按类别分组标注（除了honeycomb，因为它已经是基础了）
        content_annotations = []
        for annotation in annotations:
            class_name = annotation.get('class', '').lower()
            if class_name != 'honeycomb' and class_name in self.CLASS_MAPPING:
                content_annotations.append(annotation)

        logger.debug(f"开始绘制内容物类别，共 {len(content_annotations)} 个标注...")

        # 绘制具体内容物（覆盖在honeycomb上）
        content_processed = 0
        for annotation in content_annotations:
            class_name = annotation.get('class', '').lower()

            if class_name not in self.CLASS_MAPPING:
                logger.warning(f"跳过未知类别: '{class_name}'")
                skipped_count += 1
                continue

            class_id = self.CLASS_MAPPING[class_name]
            annotation_type = annotation.get('type', '')

            if annotation_type == 'polygon' and 'points' in annotation:
                points = annotation['points']

                if len(points) >= 3:
                    try:
                        # 转换点坐标
                        polygon_points = []
                        for point in points:
                            x = int(point.get('x', 0))
                            y = int(point.get('y', 0))

                            # 确保坐标在图像范围内
                            x = max(0, min(x, width - 1))
                            y = max(0, min(y, height - 1))
                            polygon_points.append([x, y])

                        # 绘制到最终mask
                        polygon_array = np.array([polygon_points], dtype=np.int32)
                        cv2.fillPoly(final_mask, polygon_array, class_id)
                        content_processed += 1

                    except (ValueError, TypeError) as e:
                        logger.warning(f"处理多边形标注失败: {e}")
                        skipped_count += 1

            elif annotation_type == 'circle':
                try:
                    center_x = int(annotation.get('x', 0))
                    center_y = int(annotation.get('y', 0))
                    radius = int(annotation.get('radius', 0))

                    # 确保坐标在图像范围内
                    center_x = max(0, min(center_x, width - 1))
                    center_y = max(0, min(center_y, height - 1))

                    if radius > 0:
                        cv2.circle(final_mask, (center_x, center_y), radius, class_id, -1)
                        content_processed += 1

                except (ValueError, TypeError) as e:
                    logger.warning(f"处理圆形标注失败: {e}")
                    skipped_count += 1
            else:
                logger.warning(f"跳过未知标注类型: '{annotation_type}'")
                skipped_count += 1

        logger.debug(f"内容物标注处理完成: {content_processed} 个")
        logger.debug(f"总计处理: 成功 {processed_count + content_processed}, 跳过 {skipped_count}")

        return final_mask
    
    def _print_statistics(self):
        """打印处理统计信息"""
        logger.info("=" * 50)
        logger.info("标注数据处理统计")
        logger.info("=" * 50)
        logger.info(f"总文件数: {self.stats['total_files']}")
        logger.info(f"成功处理: {self.stats['processed_files']}")
        logger.info(f"处理失败: {len(self.stats['errors'])}")
        
        if self.stats['errors']:
            logger.info("\n错误列表:")
            for error in self.stats['errors']:
                logger.info(f"  - {error}")
        
        logger.info("\n类别像素统计:")
        total_pixels = sum(self.stats['class_counts'].values())
        for class_name in self.CLASS_NAMES:
            count = self.stats['class_counts'][class_name]
            percentage = (count / total_pixels * 100) if total_pixels > 0 else 0
            logger.info(f"  {class_name}: {count:,} 像素 ({percentage:.2f}%)")
        
        logger.info(f"\n总像素数: {total_pixels:,}")
        logger.info("=" * 50)
    
    def validate_csv_annotations(self) -> Dict:
        """
        验证CSV标注文件
        
        Returns:
            验证结果统计
        """
        csv_files = list(self.annotation_dir.glob('*.csv'))
        validation_stats = {
            'total_csv_files': len(csv_files),
            'valid_files': 0,
            'invalid_files': 0,
            'zero_coordinate_files': 0,
            'errors': []
        }
        
        logger.info(f"验证 {len(csv_files)} 个CSV文件")
        
        for csv_file in csv_files:
            try:
                with open(csv_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    rows = list(reader)
                
                if not rows:
                    validation_stats['invalid_files'] += 1
                    continue
                
                # 检查坐标是否为零
                zero_coords = all(
                    float(row.get('x', 0)) == 0 and float(row.get('y', 0)) == 0 
                    for row in rows
                )
                
                if zero_coords:
                    validation_stats['zero_coordinate_files'] += 1
                    logger.warning(f"CSV文件 {csv_file.name} 中所有坐标都为(0,0)")
                else:
                    validation_stats['valid_files'] += 1
                
            except Exception as e:
                error_msg = f"验证CSV文件 {csv_file.name} 时出错: {str(e)}"
                validation_stats['errors'].append(error_msg)
                validation_stats['invalid_files'] += 1
        
        logger.info("CSV验证结果:")
        logger.info(f"  有效文件: {validation_stats['valid_files']}")
        logger.info(f"  无效文件: {validation_stats['invalid_files']}")
        logger.info(f"  零坐标文件: {validation_stats['zero_coordinate_files']}")
        
        return validation_stats
    
    def create_class_mapping_file(self):
        """创建类别映射配置文件"""
        mapping_file = self.output_dir / 'class_mapping.json'
        
        mapping_config = {
            'class_mapping': self.CLASS_MAPPING,
            'class_names': self.CLASS_NAMES,
            'num_classes': len(self.CLASS_NAMES),
            'description': '蜂巢8类语义分割类别映射'
        }
        
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mapping_config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"类别映射文件已保存: {mapping_file}")
    
    def analyze_annotation_quality(self) -> Dict:
        """
        分析标注质量
        
        Returns:
            质量分析结果
        """
        json_files = list(self.annotation_dir.glob('*.json'))
        quality_stats = {
            'files_with_annotations': 0,
            'empty_files': 0,
            'avg_polygons_per_file': 0,
            'class_distribution': Counter(),
            'polygon_size_stats': []
        }
        
        total_polygons = 0
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    annotations = json.load(f)
                
                if annotations:
                    quality_stats['files_with_annotations'] += 1
                    total_polygons += len(annotations)
                    
                    for annotation in annotations:
                        class_name = annotation.get('class', 'honeycomb')
                        quality_stats['class_distribution'][class_name] += 1
                        
                        # 计算多边形大小
                        if annotation.get('type') == 'polygon' and 'points' in annotation:
                            points = annotation['points']
                            if len(points) >= 3:
                                area = self._calculate_polygon_area(points)
                                quality_stats['polygon_size_stats'].append(area)
                else:
                    quality_stats['empty_files'] += 1
                    
            except Exception as e:
                logger.error(f"分析文件 {json_file.name} 时出错: {str(e)}")
        
        if quality_stats['files_with_annotations'] > 0:
            quality_stats['avg_polygons_per_file'] = total_polygons / quality_stats['files_with_annotations']
        
        return quality_stats
    
    def _calculate_polygon_area(self, points: List[Dict]) -> float:
        """计算多边形面积"""
        if len(points) < 3:
            return 0
        
        # 使用鞋带公式计算面积
        area = 0
        n = len(points)
        
        for i in range(n):
            j = (i + 1) % n
            x1, y1 = points[i].get('x', 0), points[i].get('y', 0)
            x2, y2 = points[j].get('x', 0), points[j].get('y', 0)
            area += x1 * y2 - x2 * y1
        
        return abs(area) / 2


def main():
    """主函数，用于测试"""
    annotation_dir = "/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/data/annotations"
    
    processor = BeeAnnotationProcessor(annotation_dir)
    
    # 验证CSV文件
    csv_stats = processor.validate_csv_annotations()
    
    # 处理所有标注
    process_stats = processor.process_all_annotations()
    
    # 创建类别映射文件
    processor.create_class_mapping_file()
    
    # 分析标注质量
    quality_stats = processor.analyze_annotation_quality()
    
    print("\n质量分析结果:")
    print(f"有标注的文件: {quality_stats['files_with_annotations']}")
    print(f"空文件: {quality_stats['empty_files']}")
    print(f"平均每文件多边形数: {quality_stats['avg_polygons_per_file']:.2f}")
    print(f"类别分布: {dict(quality_stats['class_distribution'])}")


if __name__ == "__main__":
    main()
