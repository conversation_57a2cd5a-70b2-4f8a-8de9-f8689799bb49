2025-07-29 00:26:46,652 - __main__ - INFO - ============================================================
2025-07-29 00:26:46,652 - __main__ - INFO - 蜂巢语义分割训练开始
2025-07-29 00:26:46,652 - __main__ - INFO - ============================================================
2025-07-29 00:26:46,652 - __main__ - INFO - 配置文件: configs/semantic_segmentation_config_correct.yaml
2025-07-29 00:26:46,652 - __main__ - INFO - 输出目录: outputs/semantic_segmentation_correct
2025-07-29 00:26:46,652 - __main__ - INFO - 调试模式: False
2025-07-29 00:26:46,662 - __main__ - INFO - 使用设备: cuda
2025-07-29 00:26:46,667 - __main__ - INFO - ✓ 数据变换创建完成
2025-07-29 00:26:46,667 - __main__ - INFO - 创建数据集...
2025-07-29 00:26:46,667 - __main__ - INFO - 使用预分块数据...
2025-07-29 00:26:47,636 - __main__ - INFO - ✓ 预分块数据集加载完成: 训练集 3185, 验证集 3863
2025-07-29 00:26:47,636 - __main__ - INFO - ✓ 数据加载器创建完成
2025-07-29 00:26:47,637 - __main__ - INFO - 创建模型...
2025-07-29 00:26:49,428 - __main__ - INFO - ✓ 特征提取器创建完成
2025-07-29 00:26:50,531 - __main__ - INFO - ✓ MAE权重加载完成: 150 个权重已加载
2025-07-29 00:26:50,814 - __main__ - INFO - ✓ 分割头创建完成
2025-07-29 00:26:50,814 - __main__ - INFO - ✓ 完整模型创建完成
2025-07-29 00:26:51,304 - __main__ - INFO - ✓ 损失函数创建完成
2025-07-29 00:26:51,305 - __main__ - INFO - ✓ 优化器创建完成
2025-07-29 00:26:51,306 - __main__ - ERROR - 训练过程中出错: create_scheduler() takes 2 positional arguments but 3 were given
2025-07-29 00:26:51,307 - __main__ - ERROR - Traceback (most recent call last):
  File "/home/<USER>/PycharmProjects/20250629/training_refactored/scripts/train_semantic_segmentation.py", line 425, in main
    scheduler = create_scheduler(optimizer, config, logger)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: create_scheduler() takes 2 positional arguments but 3 were given

