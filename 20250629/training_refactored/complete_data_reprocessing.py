#!/usr/bin/env python3
"""
完整的8类数据重新处理脚本
从原始标注数据开始，确保所有8个类别都被正确处理

流程：
1. 使用BeeAnnotationProcessor处理JSON标注 -> 生成8类mask
2. 使用HoneycombPatchExtractor提取512x512图块
3. 使用StratifiedDataSplitter进行7:3分层划分
4. 验证所有8个类别都存在

Author: AI Assistant
Date: 2024
"""

import os
import sys
import logging
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
training_refactored_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(training_refactored_root))

def setup_logging():
    """设置详细日志"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"complete_reprocessing_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志文件: {log_file}")
    return logger

def step1_process_annotations(logger):
    """步骤1：处理JSON标注生成8类mask"""
    logger.info("=== 步骤1：处理JSON标注生成8类mask ===")
    
    try:
        from utils.annotation_processor import BeeAnnotationProcessor
        
        annotation_dir = "/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/data/annotations"
        image_dir = "/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/static/uploads"
        output_dir = "/home/<USER>/PycharmProjects/20250629/reprocessed_data/masks"
        
        # 确保输出目录存在
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        processor = BeeAnnotationProcessor(
            annotation_dir=annotation_dir,
            output_dir=output_dir
        )
        
        # 设置图像目录
        processor.image_dir = Path(image_dir)
        
        logger.info("开始处理JSON标注...")
        results = processor.process_all_annotations()
        
        logger.info(f"✅ 标注处理完成:")
        logger.info(f"   处理文件数: {results.get('processed_files', 0)}")
        logger.info(f"   总文件数: {results.get('total_files', 0)}")
        logger.info(f"   类别统计: {dict(results.get('class_counts', {}))}")
        
        # 验证所有8个类别都存在
        expected_classes = set(range(8))  # 0-7
        found_classes = set(results.get('class_counts', {}).keys())
        missing_classes = expected_classes - found_classes
        
        if missing_classes:
            logger.warning(f"⚠️ 缺失类别: {missing_classes}")
        else:
            logger.info("✅ 所有8个类别都存在于标注中")
        
        return True, output_dir, results
        
    except Exception as e:
        logger.error(f"❌ 标注处理失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, None, None

def step2_extract_patches(logger, mask_dir, annotation_results):
    """步骤2：提取512x512训练图块"""
    logger.info("=== 步骤2：提取512x512训练图块 ===")
    
    try:
        from data_preparation.create_training_patches import HoneycombPatchExtractor
        
        image_dir = "/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/static/uploads"
        output_dir = "/home/<USER>/PycharmProjects/20250629/reprocessed_data/patches"
        
        # 确保输出目录存在
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        extractor = HoneycombPatchExtractor(
            image_dir=image_dir,
            mask_dir=mask_dir,
            output_dir=output_dir,
            patch_size=512,
            stride=384,  # 25%重叠
            min_honeycomb_ratio=0.05,  # 降低阈值确保包含更多样本
            num_workers=8
        )
        
        logger.info("开始提取512x512训练图块...")
        extractor.process_all_images()
        
        # 检查生成的图块 - HoneycombPatchExtractor使用train/val结构
        train_images_dir = Path(output_dir) / "train" / "images"
        train_masks_dir = Path(output_dir) / "train" / "masks"
        val_images_dir = Path(output_dir) / "val" / "images"
        val_masks_dir = Path(output_dir) / "val" / "masks"

        if (train_images_dir.exists() and train_masks_dir.exists() and
            val_images_dir.exists() and val_masks_dir.exists()):

            train_image_count = len(list(train_images_dir.glob("*")))
            train_mask_count = len(list(train_masks_dir.glob("*")))
            val_image_count = len(list(val_images_dir.glob("*")))
            val_mask_count = len(list(val_masks_dir.glob("*")))

            total_images = train_image_count + val_image_count
            total_masks = train_mask_count + val_mask_count

            logger.info(f"✅ 图块提取完成:")
            logger.info(f"   训练集图像: {train_image_count}")
            logger.info(f"   训练集掩码: {train_mask_count}")
            logger.info(f"   验证集图像: {val_image_count}")
            logger.info(f"   验证集掩码: {val_mask_count}")
            logger.info(f"   总计: {total_images} 图像, {total_masks} 掩码")

            if total_images == total_masks and total_images > 0:
                return True, output_dir
            else:
                logger.error(f"❌ 图像和掩码数量不匹配或为空")
                return False, None
        else:
            logger.error(f"❌ 输出目录结构不正确")
            return False, None
        
    except Exception as e:
        logger.error(f"❌ 图块提取失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, None

def step3_analyze_existing_split(logger, patches_dir):
    """步骤3：分析HoneycombPatchExtractor已完成的数据划分"""
    logger.info("=== 步骤3：分析已完成的数据划分 ===")

    try:
        # HoneycombPatchExtractor已经完成了数据划分，我们只需要分析结果
        train_images_dir = Path(patches_dir) / "train" / "images"
        train_masks_dir = Path(patches_dir) / "train" / "masks"
        val_images_dir = Path(patches_dir) / "val" / "images"
        val_masks_dir = Path(patches_dir) / "val" / "masks"

        # 统计样本数量
        train_files = list(train_images_dir.glob("*"))
        val_files = list(val_images_dir.glob("*"))

        logger.info(f"✅ 数据划分分析:")
        logger.info(f"   训练集: {len(train_files)} 个样本")
        logger.info(f"   验证集: {len(val_files)} 个样本")
        logger.info(f"   总计: {len(train_files) + len(val_files)} 个样本")

        # 分析类别分布
        def analyze_class_distribution(masks_dir, split_name):
            from collections import Counter
            import cv2
            import numpy as np

            class_counts = Counter()
            mask_files = list(masks_dir.glob("*"))

            for mask_file in mask_files:
                try:
                    mask = cv2.imread(str(mask_file), cv2.IMREAD_GRAYSCALE)
                    if mask is not None:
                        # 找到主要类别（排除背景）
                        unique, counts = np.unique(mask, return_counts=True)
                        class_pixel_counts = dict(zip(unique, counts))

                        # 找到非背景类别中像素最多的
                        non_bg_counts = {k: v for k, v in class_pixel_counts.items() if k != 0}
                        if non_bg_counts:
                            main_class = max(non_bg_counts.items(), key=lambda x: x[1])[0]
                        else:
                            main_class = 0

                        class_counts[main_class] += 1
                except Exception as e:
                    logger.warning(f"分析掩码 {mask_file} 失败: {e}")

            logger.info(f"{split_name}类别分布:")
            for class_id in sorted(class_counts.keys()):
                count = class_counts[class_id]
                logger.info(f"   类别 {class_id}: {count} 个样本")

            return dict(class_counts)

        train_class_distribution = analyze_class_distribution(train_masks_dir, "训练集")
        val_class_distribution = analyze_class_distribution(val_masks_dir, "验证集")

        # 创建模拟的split_result对象
        class SplitResult:
            def __init__(self):
                self.train_files = [f.name for f in train_files]
                self.val_files = [f.name for f in val_files]
                self.train_class_distribution = train_class_distribution
                self.val_class_distribution = val_class_distribution

        split_result = SplitResult()

        return True, patches_dir, split_result

    except Exception as e:
        logger.error(f"❌ 数据划分分析失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, None, None

def step4_verify_classes(logger, final_dataset_dir, split_result):
    """步骤4：验证所有8个类别"""
    logger.info("=== 步骤4：验证所有8个类别 ===")
    
    try:
        # 检查训练集和验证集的类别
        train_classes = set(split_result.train_class_distribution.keys())
        val_classes = set(split_result.val_class_distribution.keys())
        all_classes = train_classes | val_classes
        
        expected_classes = set(range(8))  # 0-7
        missing_classes = expected_classes - all_classes
        
        logger.info(f"发现的类别: {sorted(all_classes)}")
        logger.info(f"预期的类别: {sorted(expected_classes)}")
        
        if missing_classes:
            logger.error(f"❌ 缺失类别: {sorted(missing_classes)}")
            return False
        else:
            logger.info("✅ 所有8个类别都存在于最终数据集中")
        
        # 保存详细统计
        stats = {
            "reprocessing_timestamp": datetime.now().isoformat(),
            "train_samples": len(split_result.train_files),
            "val_samples": len(split_result.val_files),
            "total_samples": len(split_result.train_files) + len(split_result.val_files),
            "found_classes": sorted(all_classes),
            "missing_classes": sorted(missing_classes),
            "train_class_distribution": {str(k): int(v) for k, v in split_result.train_class_distribution.items()},
            "val_class_distribution": {str(k): int(v) for k, v in split_result.val_class_distribution.items()},
            "class_names": {
                "0": "background",
                "1": "capped_brood", 
                "2": "eggs",
                "3": "honey",
                "4": "honeycomb",
                "5": "larvae",
                "6": "nectar",
                "7": "pollen"
            }
        }
        
        stats_file = Path(final_dataset_dir) / "complete_8class_statistics.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ 完整统计信息已保存到: {stats_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 类别验证失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("🚀 开始完整的8类数据重新处理")
    
    # 步骤1：处理标注
    success, mask_dir, annotation_results = step1_process_annotations(logger)
    if not success:
        logger.error("❌ 步骤1失败，退出")
        return False
    
    # 步骤2：提取图块
    success, patches_dir = step2_extract_patches(logger, mask_dir, annotation_results)
    if not success:
        logger.error("❌ 步骤2失败，退出")
        return False
    
    # 步骤3：分析已完成的数据划分
    success, final_dataset_dir, split_result = step3_analyze_existing_split(logger, patches_dir)
    if not success:
        logger.error("❌ 步骤3失败，退出")
        return False
    
    # 步骤4：验证类别
    success = step4_verify_classes(logger, final_dataset_dir, split_result)
    if not success:
        logger.error("❌ 步骤4失败，退出")
        return False
    
    logger.info("🎉 完整的8类数据重新处理成功完成！")
    logger.info(f"最终数据集路径: {final_dataset_dir}")
    
    return True, final_dataset_dir

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ 8类数据重新处理完成！")
    else:
        print("❌ 8类数据重新处理失败！")
        sys.exit(1)
